/**
 * @file ui_component_manager.c
 * @brief UI组件管理器实现
 * @version 1.0.0
 * @date 2025-07-22
 */

#include "ui_component_manager.h"
#include "app/ebike_x1.h"
#include <stdio.h>
#include <string.h>
#include <stddef.h>

// =============================================================================
// 内部变量
// =============================================================================

static dashboard_ui_components_t g_dashboard_components = {0};
static bool g_manager_initialized = false;

// 性能统计数据
typedef struct {
    uint32_t init_time_ms;          /**< 初始化耗时(毫秒) */
    uint32_t total_get_calls;       /**< 总获取调用次数 */
    uint32_t cache_hits;            /**< 缓存命中次数 */
    uint32_t cache_misses;          /**< 缓存未命中次数 */
    uint32_t last_validation_time;  /**< 上次验证时间 */
} ui_comp_performance_stats_t;

static ui_comp_performance_stats_t g_performance_stats = {0};

// 线程安全保护（当前LVGL配置未启用OS支持，暂时禁用互斥锁）
// TODO: 如果需要线程安全，需要在lv_conf.h中启用LV_USE_OS
#define UI_COMP_LOCK()
#define UI_COMP_UNLOCK()

// =============================================================================
// 组件配置表
// =============================================================================

#define COMPONENT_CONFIG(name, parent, id) \
    {#name, &parent, id, offsetof(dashboard_ui_components_t, name)}

static const ui_component_config_t g_component_configs[] = {
    // 状态栏组件
    COMPONENT_CONFIG(statusbar_temperature, ui_statusbar, DASHBOARD_STATUSBAR_TEMPERATURE),
    COMPONENT_CONFIG(statusbar_mode, ui_statusbar, DASHBOARD_STATUSBAR_MODE),
    COMPONENT_CONFIG(statusbar_gear, ui_statusbar, DASHBOARD_STATUSBAR_GEAR),
    COMPONENT_CONFIG(statusbar_time, ui_statusbar, DASHBOARD_STATUSBAR_TIME),
    COMPONENT_CONFIG(statusbar_power, ui_statusbar, DASHBOARD_STATUSBAR_POWER),
    COMPONENT_CONFIG(statusbar_trip, ui_statusbar, DASHBOARD_STATUSBAR_TRIP),
    COMPONENT_CONFIG(statusbar_battery_icon, ui_statusbar, DASHBOARD_STATUSBAR_BATTERY_ICON),
    COMPONENT_CONFIG(statusbar_battery_label, ui_statusbar, DASHBOARD_STATUSBAR_BATTERY_LABEL),
    COMPONENT_CONFIG(statusbar_odometer, ui_statusbar, DASHBOARD_STATUSBAR_ODOMETER),
    COMPONENT_CONFIG(statusbar_ready, ui_statusbar, DASHBOARD_STATUSBAR_READY),

    // 主页组件
    COMPONENT_CONFIG(home_battery_icon, ui_home, DASHBOARD_HOME_BATTERY_ICON),
    COMPONENT_CONFIG(home_battery_label, ui_home, DASHBOARD_HOME_BATTERY_LABEL),
    COMPONENT_CONFIG(home_battery_bar, ui_home, DASHBOARD_HOME_BATTERY_BAR),
    COMPONENT_CONFIG(home_speed_value, ui_home, DASHBOARD_HOME_SPEED_VALUE),
    COMPONENT_CONFIG(home_cruise_parent, ui_home, DASHBOARD_HOME_CRUISE_PARENT),
    COMPONENT_CONFIG(home_cruise_icon, ui_home, DASHBOARD_HOME_CRUISE_ICON),
    COMPONENT_CONFIG(home_cruise_speed, ui_home, DASHBOARD_HOME_CRUISE_SPEED),

    // 灯光组件
    COMPONENT_CONFIG(home_light_left, ui_home, DASHBOARD_HOME_LIGHT_LEFT),
    COMPONENT_CONFIG(home_light_right, ui_home, DASHBOARD_HOME_LIGHT_RIGHT),
    COMPONENT_CONFIG(home_light_runlight, ui_home, DASHBOARD_HOME_LIGHT_RUNLIGHT),
    COMPONENT_CONFIG(home_light_front, ui_home, DASHBOARD_HOME_LIGHT_FRONT),
    COMPONENT_CONFIG(home_light_brake, ui_home, DASHBOARD_HOME_LIGHT_BRAKE),

    // 姿态组件
    COMPONENT_CONFIG(home_pitch_angle, ui_home, DASHBOARD_HOME_PITCH_ANGLE),
    COMPONENT_CONFIG(home_roll_angle, ui_home, DASHBOARD_HOME_ROLL_ANGLE),
    COMPONENT_CONFIG(home_acceleration, ui_home, DASHBOARD_HOME_ACCELERATION),

    // 设置页面组件
    COMPONENT_CONFIG(setting_date, ui_SettingItem3, DASHBOARD_SETTING_DATE),
    COMPONENT_CONFIG(setting_total_mileage, ui_SettingInfo, DASHBOARD_SETTING_TOTAL_MILEAGE),
    COMPONENT_CONFIG(setting_sub_mileage, ui_SettingInfo, DASHBOARD_SETTING_SUB_MILEAGE),
    COMPONENT_CONFIG(setting_sw_version, ui_InfoLarge1, DASHBOARD_SETTING_SW_VERSION),
    COMPONENT_CONFIG(setting_hw_version, ui_InfoLarge2, DASHBOARD_SETTING_HW_VERSION),
};

static const size_t g_component_count = sizeof(g_component_configs) / sizeof(g_component_configs[0]);

// =============================================================================
// 内部函数声明
// =============================================================================

static ui_comp_result_t init_component_from_config(const ui_component_config_t *config);
static ui_comp_result_t init_all_components(void);
static ui_component_t* get_component_by_offset(size_t offset);
static ui_comp_result_t validate_component_config(const ui_component_config_t *config);
static uint32_t get_timestamp_ms(void);
static void update_performance_stats(void);

// =============================================================================
// 公共接口函数实现
// =============================================================================

ui_comp_result_t ui_component_manager_init(void) {
    // 检查是否已经初始化
    if (g_manager_initialized) {
        return UI_COMP_SUCCESS; // 已经初始化
    }

    // 初始化所有组件描述
    ui_comp_result_t result = init_all_components();
    if (result != UI_COMP_SUCCESS) {
        return result;
    }

    g_manager_initialized = true;
    UI_COMP_MGR_DEBUG_PRINT("UI组件管理器初始化完成");

    return UI_COMP_SUCCESS;
}

ui_comp_result_t ui_component_manager_deinit(void) {
    if (!g_manager_initialized) {
        return UI_COMP_SUCCESS;
    }

    // 清除所有缓存
    ui_component_clear_cache();

    // 重置组件结构体
    memset(&g_dashboard_components, 0, sizeof(dashboard_ui_components_t));

    g_manager_initialized = false;
    UI_COMP_MGR_DEBUG_PRINT("UI组件管理器已清理");

    return UI_COMP_SUCCESS;
}

const dashboard_ui_components_t* ui_component_manager_get_components(void) {
    if (!g_manager_initialized) {
        return NULL;
    }

    return &g_dashboard_components;
}

lv_obj_t* ui_component_get_object(ui_component_t *component) {
    if (!g_manager_initialized || !component || !component->parent) {
        return NULL;
    }

    // 更新性能统计
    g_performance_stats.total_get_calls++;

    // 如果已缓存且有效，直接返回
    if (component->is_cached && component->cached_obj) {
        g_performance_stats.cache_hits++;
        return component->cached_obj;
    }

    // 缓存未命中
    g_performance_stats.cache_misses++;

    // 获取组件对象，增加安全检查
    lv_obj_t *obj = NULL;
    if (component->parent) {
        obj = ui_comp_get_child(component->parent, component->component_id);
        if (obj) {
            // 缓存组件对象
            component->cached_obj = obj;
            component->is_cached = true;
            UI_COMP_MGR_DEBUG_PRINT("组件 %s 缓存成功", component->name ? component->name : "unknown");
        } else {
            UI_COMP_MGR_DEBUG_PRINT("组件 %s 获取失败: ui_comp_get_child返回NULL", component->name ? component->name : "unknown");
        }
    } else {
        UI_COMP_MGR_DEBUG_PRINT("组件 %s 获取失败: 父对象为NULL", component->name ? component->name : "unknown");
    }

    return obj;
}

ui_comp_result_t ui_component_cache_all(void) {
    if (!g_manager_initialized) {
        return UI_COMP_ERROR_NOT_INITIALIZED;
    }

    int cached_count = 0;

    // 使用配置表遍历所有组件
    for (size_t i = 0; i < g_component_count; i++) {
        ui_component_t *component = get_component_by_offset(g_component_configs[i].component_offset);
        if (component && ui_component_get_object(component)) {
            cached_count++;
        }
    }

    UI_COMP_MGR_DEBUG_PRINT("UI组件缓存完成: %d/%zu", cached_count, g_component_count);

    return UI_COMP_SUCCESS;
}

ui_comp_result_t ui_component_clear_cache(void) {
    if (!g_manager_initialized) {
        return UI_COMP_ERROR_NOT_INITIALIZED;
    }

    // 使用配置表遍历所有组件
    for (size_t i = 0; i < g_component_count; i++) {
        ui_component_t *component = get_component_by_offset(g_component_configs[i].component_offset);
        if (component) {
            component->cached_obj = NULL;
            component->is_cached = false;
        }
    }

    UI_COMP_MGR_DEBUG_PRINT("UI组件缓存已清除");

    return UI_COMP_SUCCESS;
}

bool ui_component_is_valid(const ui_component_t *component) {
    if (!component || !component->parent) {
        return false;
    }
    
    // 如果有缓存，检查缓存对象是否有效
    if (component->is_cached) {
        return component->cached_obj != NULL;
    }
    
    // 尝试获取对象来验证有效性
    lv_obj_t *obj = ui_comp_get_child(component->parent, component->component_id);
    return obj != NULL;
}

// =============================================================================
// 调试和诊断函数实现
// =============================================================================

ui_comp_result_t ui_component_print_status(void) {
    if (!g_manager_initialized) {
        printf("UI组件管理器未初始化\n");
        return UI_COMP_ERROR_NOT_INITIALIZED;
    }

    printf("=== UI组件状态 ===\n");

    int valid_count = 0;
    int total_count = 0;

    // 使用配置表遍历所有组件
    for (size_t i = 0; i < g_component_count; i++) {
        const ui_component_config_t *config = &g_component_configs[i];
        ui_component_t *component = get_component_by_offset(config->component_offset);

        if (component) {
            bool is_valid = ui_component_is_valid(component);
            printf("  %s: %s%s\n",
                   config->name,
                   is_valid ? "有效" : "无效",
                   component->is_cached ? " (已缓存)" : "");

            if (is_valid) valid_count++;
            total_count++;
        }
    }

    printf("==================\n");
    printf("总计: %d/%d 组件有效\n", valid_count, total_count);

    return UI_COMP_SUCCESS;
}

int ui_component_validate_all(void) {
    if (!g_manager_initialized) {
        return -1; // 返回负数表示错误
    }

    int valid_count = 0;

    // 使用配置表验证所有组件
    for (size_t i = 0; i < g_component_count; i++) {
        ui_component_t *component = get_component_by_offset(g_component_configs[i].component_offset);
        if (component && ui_component_is_valid(component)) {
            valid_count++;
        }
    }

    return valid_count;
}

ui_comp_result_t ui_component_get_stats(int *total_components, int *cached_components, int *valid_components) {
    if (!g_manager_initialized) {
        if (total_components) *total_components = 0;
        if (cached_components) *cached_components = 0;
        if (valid_components) *valid_components = 0;
        return UI_COMP_ERROR_NOT_INITIALIZED;
    }

    int total = (int)g_component_count;
    int cached = 0;
    int valid = 0;

    // 使用配置表统计所有组件
    for (size_t i = 0; i < g_component_count; i++) {
        ui_component_t *component = get_component_by_offset(g_component_configs[i].component_offset);
        if (component) {
            if (component->is_cached) {
                cached++;
            }
            if (ui_component_is_valid(component)) {
                valid++;
            }
        }
    }

    if (total_components) *total_components = total;
    if (cached_components) *cached_components = cached;
    if (valid_components) *valid_components = valid;

    return UI_COMP_SUCCESS;
}

// =============================================================================
// 内部函数实现
// =============================================================================

static ui_component_t* get_component_by_offset(size_t offset) {
    return (ui_component_t*)((char*)&g_dashboard_components + offset);
}

static ui_comp_result_t validate_component_config(const ui_component_config_t *config) {
    if (!config) {
        return UI_COMP_ERROR_INVALID_PARAM;
    }

    if (!config->name || !config->parent_ref) {
        UI_COMP_MGR_DEBUG_PRINT("配置验证失败: 组件 %s", config->name ? config->name : "unknown");
        return UI_COMP_ERROR_INVALID_PARAM;
    }

    return UI_COMP_SUCCESS;
}

static ui_comp_result_t init_component_from_config(const ui_component_config_t *config) {
    ui_comp_result_t result = validate_component_config(config);
    if (result != UI_COMP_SUCCESS) {
        return result;
    }

    ui_component_t *component = get_component_by_offset(config->component_offset);
    if (!component) {
        return UI_COMP_ERROR_INVALID_PARAM;
    }

    // 检查父对象是否有效
    if (!config->parent_ref) {
        UI_COMP_MGR_DEBUG_PRINT("父对象引用无效: 组件 %s", config->name);
        return UI_COMP_ERROR_INVALID_PARAM;
    }

    if (!(*config->parent_ref)) {
        UI_COMP_MGR_DEBUG_PRINT("父对象为空: 组件 %s (可能UI尚未初始化)", config->name);
        return UI_COMP_ERROR_INVALID_PARAM;
    }

    component->parent = *config->parent_ref;
    component->component_id = config->component_id;
    component->cached_obj = NULL;
    component->is_cached = false;
    component->name = config->name;

    UI_COMP_MGR_DEBUG_PRINT("组件初始化成功: %s", config->name);
    return UI_COMP_SUCCESS;
}

static ui_comp_result_t init_all_components(void) {
    int success_count = 0;
    int error_count = 0;

    // 使用配置表初始化所有组件
    for (size_t i = 0; i < g_component_count; i++) {
        ui_comp_result_t result = init_component_from_config(&g_component_configs[i]);
        if (result == UI_COMP_SUCCESS) {
            success_count++;
        } else {
            error_count++;
            UI_COMP_MGR_DEBUG_PRINT("组件初始化失败: %s, 错误码: %d",
                                   g_component_configs[i].name, result);
        }
    }

    UI_COMP_MGR_DEBUG_PRINT("UI组件初始化完成: 成功 %d, 失败 %d", success_count, error_count);

    // 只要有一半以上的组件初始化成功，就认为初始化成功
    // 这样可以容忍某些UI对象尚未创建的情况
    if (success_count >= (int)(g_component_count / 2)) {
        return UI_COMP_SUCCESS;
    } else {
        UI_COMP_MGR_DEBUG_PRINT("UI组件初始化失败: 成功率过低 (%d/%zu)", success_count, g_component_count);
        return UI_COMP_ERROR_INVALID_PARAM;
    }
}

// =============================================================================
// 新增API函数实现
// =============================================================================

ui_comp_result_t ui_component_iterator_begin(ui_component_iterator_t *iter) {
    if (!iter) {
        return UI_COMP_ERROR_INVALID_PARAM;
    }

    if (!g_manager_initialized) {
        return UI_COMP_ERROR_NOT_INITIALIZED;
    }

    iter->index = 0;
    if (g_component_count > 0) {
        iter->config = &g_component_configs[0];
        iter->component = get_component_by_offset(iter->config->component_offset);
    } else {
        iter->config = NULL;
        iter->component = NULL;
    }

    return UI_COMP_SUCCESS;
}

bool ui_component_iterator_next(ui_component_iterator_t *iter) {
    if (!iter) {
        return false;
    }

    if (!g_manager_initialized) {
        return false;
    }

    iter->index++;
    if (iter->index < g_component_count) {
        iter->config = &g_component_configs[iter->index];
        iter->component = get_component_by_offset(iter->config->component_offset);
        return true;
    } else {
        iter->config = NULL;
        iter->component = NULL;
        return false;
    }
}

int ui_component_foreach(ui_component_predicate_t predicate,
                        ui_component_action_t action,
                        void *user_data) {
    if (!action) {
        return -1; // 无效参数
    }

    if (!g_manager_initialized) {
        return -1;
    }

    int processed_count = 0;

    // 遍历所有组件
    for (size_t i = 0; i < g_component_count; i++) {
        ui_component_t *component = get_component_by_offset(g_component_configs[i].component_offset);

        if (component) {
            // 如果有过滤条件，检查是否符合条件
            if (predicate == NULL || predicate(component, user_data)) {
                action(component, user_data);
                processed_count++;
            }
        }
    }

    return processed_count;
}

// =============================================================================
// 增强功能实现
// =============================================================================

static uint32_t get_timestamp_ms(void) {
    // 简化的时间戳实现，实际项目中应该使用系统时钟
    static uint32_t counter = 0;
    return ++counter;
}

static void update_performance_stats(void) {
    g_performance_stats.last_validation_time = get_timestamp_ms();
}

/**
 * @brief 获取性能统计信息
 */
ui_comp_result_t ui_component_get_performance_stats(uint32_t *total_calls,
                                                   uint32_t *cache_hits,
                                                   uint32_t *cache_misses,
                                                   float *cache_hit_rate) {
    if (!g_manager_initialized) {
        return UI_COMP_ERROR_NOT_INITIALIZED;
    }

    if (total_calls) *total_calls = g_performance_stats.total_get_calls;
    if (cache_hits) *cache_hits = g_performance_stats.cache_hits;
    if (cache_misses) *cache_misses = g_performance_stats.cache_misses;

    if (cache_hit_rate) {
        if (g_performance_stats.total_get_calls > 0) {
            *cache_hit_rate = (float)g_performance_stats.cache_hits / g_performance_stats.total_get_calls;
        } else {
            *cache_hit_rate = 0.0f;
        }
    }

    return UI_COMP_SUCCESS;
}

/**
 * @brief 重置性能统计
 */
ui_comp_result_t ui_component_reset_performance_stats(void) {
    if (!g_manager_initialized) {
        return UI_COMP_ERROR_NOT_INITIALIZED;
    }

    memset(&g_performance_stats, 0, sizeof(ui_comp_performance_stats_t));

    return UI_COMP_SUCCESS;
}

/**
 * @brief 组件健康检查
 */
ui_comp_result_t ui_component_health_check(int *healthy_count,
                                          int *unhealthy_count,
                                          char *report,
                                          size_t report_size) {
    if (!g_manager_initialized) {
        return UI_COMP_ERROR_NOT_INITIALIZED;
    }

    int healthy = 0;
    int unhealthy = 0;
    char temp_report[1024] = {0};

    // 检查所有组件的健康状态
    for (size_t i = 0; i < g_component_count; i++) {
        const ui_component_config_t *config = &g_component_configs[i];
        ui_component_t *component = get_component_by_offset(config->component_offset);

        if (component) {
            bool is_valid = ui_component_is_valid(component);
            bool parent_valid = (component->parent != NULL);
            bool cache_consistent = (!component->is_cached || component->cached_obj != NULL);

            if (is_valid && parent_valid && cache_consistent) {
                healthy++;
            } else {
                unhealthy++;
                if (strlen(temp_report) < sizeof(temp_report) - 100) {
                    snprintf(temp_report + strlen(temp_report),
                            sizeof(temp_report) - strlen(temp_report),
                            "组件 %s: %s%s%s\n",
                            config->name,
                            is_valid ? "" : "无效 ",
                            parent_valid ? "" : "父对象空 ",
                            cache_consistent ? "" : "缓存不一致 ");
                }
            }
        }
    }

    if (healthy_count) *healthy_count = healthy;
    if (unhealthy_count) *unhealthy_count = unhealthy;

    if (report && report_size > 0) {
        snprintf(report, report_size,
                "=== 组件健康检查报告 ===\n"
                "健康组件: %d\n"
                "异常组件: %d\n"
                "总计组件: %zu\n"
                "健康率: %.1f%%\n"
                "%s%s",
                healthy, unhealthy, g_component_count,
                g_component_count > 0 ? (float)healthy * 100 / g_component_count : 0.0f,
                unhealthy > 0 ? "\n异常详情:\n" : "",
                temp_report);
    }

    update_performance_stats();

    return UI_COMP_SUCCESS;
}
