/**
 * @file ui_component_manager.c
 * @brief UI组件管理器实现
 * @version 1.0.0
 * @date 2025-07-22
 */

#include "ui_component_manager.h"
#include "app/ebike_x1.h"
#include <stdio.h>
#include <string.h>

// =============================================================================
// 内部变量
// =============================================================================

static dashboard_ui_components_t g_dashboard_components = {0};
static bool g_manager_initialized = false;

// =============================================================================
// 内部函数声明
// =============================================================================

static void init_component(ui_component_t *comp, lv_obj_t *parent, uint32_t comp_id, const char *name);
static void init_all_components(void);

// =============================================================================
// 公共接口函数实现
// =============================================================================

int ui_component_manager_init(void) {
    if (g_manager_initialized) {
        return 0; // 已经初始化
    }
    
    // 初始化所有组件描述
    init_all_components();
    
    g_manager_initialized = true;
    UI_COMP_MGR_DEBUG_PRINT("UI组件管理器初始化完成");
    return 0;
}

void ui_component_manager_deinit(void) {
    if (!g_manager_initialized) {
        return;
    }
    
    // 清除所有缓存
    ui_component_clear_cache();
    
    // 重置组件结构体
    memset(&g_dashboard_components, 0, sizeof(dashboard_ui_components_t));
    
    g_manager_initialized = false;
    UI_COMP_MGR_DEBUG_PRINT("UI组件管理器已清理");
}

const dashboard_ui_components_t* ui_component_manager_get_components(void) {
    if (!g_manager_initialized) {
        return NULL;
    }
    return &g_dashboard_components;
}

lv_obj_t* ui_component_get_object(ui_component_t *component) {
    if (!g_manager_initialized || !component || !component->parent) {
        return NULL;
    }
    
    // 如果已缓存且有效，直接返回
    if (component->is_cached && component->cached_obj) {
        return component->cached_obj;
    }
    
    // 获取组件对象
    lv_obj_t *obj = ui_comp_get_child(component->parent, component->component_id);
    if (obj) {
        // 缓存组件对象
        component->cached_obj = obj;
        component->is_cached = true;
    }
    
    return obj;
}

int ui_component_cache_all(void) {
    if (!g_manager_initialized) {
        return -1;
    }
    
    int cached_count = 0;
    dashboard_ui_components_t *comps = &g_dashboard_components;
    
    // 缓存所有组件（使用反射或手动列举）
    ui_component_t *component_list[] = {
        // 状态栏组件
        &comps->statusbar_temperature,
        &comps->statusbar_mode,
        &comps->statusbar_gear,
        &comps->statusbar_time,
        &comps->statusbar_power,
        &comps->statusbar_trip,
        &comps->statusbar_battery_icon,
        &comps->statusbar_battery_label,
        &comps->statusbar_odometer,
        &comps->statusbar_ready,
        
        // 主页组件
        &comps->home_battery_icon,
        &comps->home_battery_label,
        &comps->home_battery_bar,
        &comps->home_speed_value,
        &comps->home_cruise_parent,
        &comps->home_cruise_icon,
        &comps->home_cruise_speed,
        
        // 灯光组件
        &comps->home_light_left,
        &comps->home_light_right,
        &comps->home_light_runlight,
        &comps->home_light_front,
        &comps->home_light_brake,
        
        // 姿态组件
        &comps->home_pitch_angle,
        &comps->home_roll_angle,
        &comps->home_acceleration,
        
        // 设置组件
        &comps->setting_date,
        &comps->setting_total_mileage,
        &comps->setting_sub_mileage,
        &comps->setting_sw_version,
        &comps->setting_hw_version,
    };
    
    int total_components = sizeof(component_list) / sizeof(component_list[0]);
    
    for (int i = 0; i < total_components; i++) {
        if (ui_component_get_object(component_list[i])) {
            cached_count++;
        }
    }
    
    UI_COMP_MGR_DEBUG_PRINT("UI组件缓存完成: %d/%d", cached_count, total_components);
    return 0;
}

void ui_component_clear_cache(void) {
    if (!g_manager_initialized) {
        return;
    }
    
    dashboard_ui_components_t *comps = &g_dashboard_components;
    
    // 清除所有组件缓存（手动列举）
    ui_component_t *component_list[] = {
        &comps->statusbar_temperature, &comps->statusbar_mode, &comps->statusbar_gear,
        &comps->statusbar_time, &comps->statusbar_power,
        &comps->statusbar_trip, &comps->statusbar_battery_icon, &comps->statusbar_battery_label,
        &comps->statusbar_odometer, &comps->statusbar_ready, &comps->home_battery_icon, &comps->home_battery_label,
        &comps->home_battery_bar, &comps->home_speed_value, &comps->home_cruise_parent,
        &comps->home_cruise_icon, &comps->home_cruise_speed, &comps->home_light_left,
        &comps->home_light_right, &comps->home_light_runlight, &comps->home_light_front,
        &comps->home_light_brake, &comps->home_pitch_angle, &comps->home_roll_angle,
        &comps->home_acceleration, &comps->setting_date, &comps->setting_total_mileage,
        &comps->setting_sub_mileage, &comps->setting_sw_version, &comps->setting_hw_version,
    };
    
    int total_components = sizeof(component_list) / sizeof(component_list[0]);
    
    for (int i = 0; i < total_components; i++) {
        component_list[i]->cached_obj = NULL;
        component_list[i]->is_cached = false;
    }
    
    UI_COMP_MGR_DEBUG_PRINT("UI组件缓存已清除");
}

bool ui_component_is_valid(const ui_component_t *component) {
    if (!component || !component->parent) {
        return false;
    }
    
    // 如果有缓存，检查缓存对象是否有效
    if (component->is_cached) {
        return component->cached_obj != NULL;
    }
    
    // 尝试获取对象来验证有效性
    lv_obj_t *obj = ui_comp_get_child(component->parent, component->component_id);
    return obj != NULL;
}

// =============================================================================
// 调试和诊断函数实现
// =============================================================================

void ui_component_print_status(void) {
    if (!g_manager_initialized) {
        printf("UI组件管理器未初始化\n");
        return;
    }
    
    printf("=== UI组件状态 ===\n");
    
    dashboard_ui_components_t *comps = &g_dashboard_components;
    
    printf("状态栏组件:\n");
    printf("  温度: %s\n", ui_component_is_valid(&comps->statusbar_temperature) ? "有效" : "无效");
    printf("  模式: %s\n", ui_component_is_valid(&comps->statusbar_mode) ? "有效" : "无效");
    printf("  档位: %s\n", ui_component_is_valid(&comps->statusbar_gear) ? "有效" : "无效");
    printf("  时间: %s\n", ui_component_is_valid(&comps->statusbar_time) ? "有效" : "无效");
    printf("  功率: %s\n", ui_component_is_valid(&comps->statusbar_power) ? "有效" : "无效");
    printf("  行程: %s\n", ui_component_is_valid(&comps->statusbar_trip) ? "有效" : "无效");
    printf("  电池图标: %s\n", ui_component_is_valid(&comps->statusbar_battery_icon) ? "有效" : "无效");
    printf("  电池标签: %s\n", ui_component_is_valid(&comps->statusbar_battery_label) ? "有效" : "无效");
    printf("  里程表: %s\n", ui_component_is_valid(&comps->statusbar_odometer) ? "有效" : "无效");
    printf("  启动: %s\n", ui_component_is_valid(&comps->statusbar_ready) ? "有效" : "无效");

    
    printf("主页组件:\n");
    printf("  速度值: %s\n", ui_component_is_valid(&comps->home_speed_value) ? "有效" : "无效");
    printf("  电池条: %s\n", ui_component_is_valid(&comps->home_battery_bar) ? "有效" : "无效");
    
    printf("灯光组件:\n");
    printf("  左转灯: %s\n", ui_component_is_valid(&comps->home_light_left) ? "有效" : "无效");
    printf("  右转灯: %s\n", ui_component_is_valid(&comps->home_light_right) ? "有效" : "无效");
    printf("  日行灯: %s\n", ui_component_is_valid(&comps->home_light_runlight) ? "有效" : "无效");
    
    printf("==================\n");
}

int ui_component_validate_all(void) {
    if (!g_manager_initialized) {
        return 0;
    }
    
    int valid_count = 0;
    dashboard_ui_components_t *comps = &g_dashboard_components;
    
    // 验证所有组件
    ui_component_t *component_list[] = {
        &comps->statusbar_temperature, &comps->statusbar_mode, &comps->statusbar_gear,
        &comps->statusbar_time, &comps->statusbar_power,
        &comps->statusbar_trip, &comps->statusbar_battery_icon, &comps->statusbar_battery_label,
        &comps->statusbar_odometer, &comps->statusbar_ready, &comps->home_battery_icon, &comps->home_battery_label,
        &comps->home_battery_bar, &comps->home_speed_value, &comps->home_cruise_parent,
        &comps->home_cruise_icon, &comps->home_cruise_speed, &comps->home_light_left,
        &comps->home_light_right, &comps->home_light_runlight, &comps->home_light_front,
        &comps->home_light_brake, &comps->home_pitch_angle, &comps->home_roll_angle,
        &comps->home_acceleration, &comps->setting_date, &comps->setting_total_mileage,
        &comps->setting_sub_mileage, &comps->setting_sw_version, &comps->setting_hw_version,
    };
    
    int total_components = sizeof(component_list) / sizeof(component_list[0]);
    
    for (int i = 0; i < total_components; i++) {
        if (ui_component_is_valid(component_list[i])) {
            valid_count++;
        }
    }
    
    return valid_count;
}

void ui_component_get_stats(int *total_components, int *cached_components, int *valid_components) {
    if (!g_manager_initialized) {
        if (total_components) *total_components = 0;
        if (cached_components) *cached_components = 0;
        if (valid_components) *valid_components = 0;
        return;
    }
    
    dashboard_ui_components_t *comps = &g_dashboard_components;
    
    ui_component_t *component_list[] = {
        &comps->statusbar_temperature, &comps->statusbar_mode, &comps->statusbar_gear,
        &comps->statusbar_time, &comps->statusbar_power, &comps->statusbar_ready,
        &comps->statusbar_trip, &comps->statusbar_battery_icon, &comps->statusbar_battery_label,
        &comps->statusbar_odometer, &comps->home_battery_icon, &comps->home_battery_label,
        &comps->home_battery_bar, &comps->home_speed_value, &comps->home_cruise_parent,
        &comps->home_cruise_icon, &comps->home_cruise_speed, &comps->home_light_left,
        &comps->home_light_right, &comps->home_light_runlight, &comps->home_light_front,
        &comps->home_light_brake, &comps->home_pitch_angle, &comps->home_roll_angle,
        &comps->home_acceleration, &comps->setting_date, &comps->setting_total_mileage,
        &comps->setting_sub_mileage, &comps->setting_sw_version, &comps->setting_hw_version,
    };
    
    int total = sizeof(component_list) / sizeof(component_list[0]);
    int cached = 0;
    int valid = 0;
    
    for (int i = 0; i < total; i++) {
        if (component_list[i]->is_cached) {
            cached++;
        }
        if (ui_component_is_valid(component_list[i])) {
            valid++;
        }
    }
    
    if (total_components) *total_components = total;
    if (cached_components) *cached_components = cached;
    if (valid_components) *valid_components = valid;
}

// =============================================================================
// 内部函数实现
// =============================================================================

static void init_component(ui_component_t *comp, lv_obj_t *parent, uint32_t comp_id, const char *name) {
    if (!comp) {
        return;
    }

    comp->parent = parent;
    comp->component_id = comp_id;
    comp->cached_obj = NULL;
    comp->is_cached = false;
    comp->name = name;
}

static void init_all_components(void) {
    dashboard_ui_components_t *comps = &g_dashboard_components;

    // 初始化状态栏组件
    init_component(&comps->statusbar_temperature, ui_statusbar, DASHBOARD_STATUSBAR_TEMPERATURE, "statusbar_temperature");
    init_component(&comps->statusbar_mode, ui_statusbar, DASHBOARD_STATUSBAR_MODE, "statusbar_mode");
    init_component(&comps->statusbar_gear, ui_statusbar, DASHBOARD_STATUSBAR_GEAR, "statusbar_gear");
    init_component(&comps->statusbar_time, ui_statusbar, DASHBOARD_STATUSBAR_TIME, "statusbar_time");
    init_component(&comps->statusbar_power, ui_statusbar, DASHBOARD_STATUSBAR_POWER, "statusbar_power");
    init_component(&comps->statusbar_trip, ui_statusbar, DASHBOARD_STATUSBAR_TRIP, "statusbar_trip");
    init_component(&comps->statusbar_battery_icon, ui_statusbar, DASHBOARD_STATUSBAR_BATTERY_ICON, "statusbar_battery_icon");
    init_component(&comps->statusbar_battery_label, ui_statusbar, DASHBOARD_STATUSBAR_BATTERY_LABEL, "statusbar_battery_label");
    init_component(&comps->statusbar_odometer, ui_statusbar, DASHBOARD_STATUSBAR_ODOMETER, "statusbar_odometer");
    init_component(&comps->statusbar_ready, ui_statusbar, DASHBOARD_STATUSBAR_READY, "statusbar_ready");

    // 初始化主页组件
    init_component(&comps->home_battery_icon, ui_home, DASHBOARD_HOME_BATTERY_ICON, "home_battery_icon");
    init_component(&comps->home_battery_label, ui_home, DASHBOARD_HOME_BATTERY_LABEL, "home_battery_label");
    init_component(&comps->home_battery_bar, ui_home, DASHBOARD_HOME_BATTERY_BAR, "home_battery_bar");
    init_component(&comps->home_speed_value, ui_home, DASHBOARD_HOME_SPEED_VALUE, "home_speed_value");
    init_component(&comps->home_cruise_parent, ui_home, DASHBOARD_HOME_CRUISE_PARENT, "home_cruise_parent");
    init_component(&comps->home_cruise_icon, ui_home, DASHBOARD_HOME_CRUISE_ICON, "home_cruise_icon");
    init_component(&comps->home_cruise_speed, ui_home, DASHBOARD_HOME_CRUISE_SPEED, "home_cruise_speed");

    // 初始化灯光组件
    init_component(&comps->home_light_left, ui_home, DASHBOARD_HOME_LIGHT_LEFT, "home_light_left");
    init_component(&comps->home_light_right, ui_home, DASHBOARD_HOME_LIGHT_RIGHT, "home_light_right");
    init_component(&comps->home_light_runlight, ui_home, DASHBOARD_HOME_LIGHT_RUNLIGHT, "home_light_runlight");
    init_component(&comps->home_light_front, ui_home, DASHBOARD_HOME_LIGHT_FRONT, "home_light_front");
    init_component(&comps->home_light_brake, ui_home, DASHBOARD_HOME_LIGHT_BRAKE, "home_light_brake");

    // 初始化姿态组件
    init_component(&comps->home_pitch_angle, ui_home, DASHBOARD_HOME_PITCH_ANGLE, "home_pitch_angle");
    init_component(&comps->home_roll_angle, ui_home, DASHBOARD_HOME_ROLL_ANGLE, "home_roll_angle");
    init_component(&comps->home_acceleration, ui_home, DASHBOARD_HOME_ACCELERATION, "home_acceleration");

    // 初始化设置页面组件
    init_component(&comps->setting_date, ui_SettingItem3, DASHBOARD_SETTING_DATE, "setting_date");
    init_component(&comps->setting_total_mileage, ui_SettingInfo, DASHBOARD_SETTING_TOTAL_MILEAGE, "setting_total_mileage");
    init_component(&comps->setting_sub_mileage, ui_SettingInfo, DASHBOARD_SETTING_SUB_MILEAGE, "setting_sub_mileage");
    init_component(&comps->setting_sw_version, ui_InfoLarge1, DASHBOARD_SETTING_SW_VERSION, "setting_sw_version");
    init_component(&comps->setting_hw_version, ui_InfoLarge2, DASHBOARD_SETTING_HW_VERSION, "setting_hw_version");

    UI_COMP_MGR_DEBUG_PRINT("UI组件描述初始化完成");
}
