/**
 * @file vehicle_data_updater.h
 * @brief 车辆数据更新模块接口
 * @version 1.0.0
 * @date 2025-07-31 14:35 GMT+8
 * <AUTHOR> (全栈开发者)
 */

#ifndef VEHICLE_DATA_SETTER_H
#define VEHICLE_DATA_SETTER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

// =============================================================================
// 公共接口函数
// =============================================================================

/**
 * @brief 初始化数据更新模块
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_updater_init(void);

/**
 * @brief 清理数据更新模块
 */
void vehicle_data_updater_deinit(void);

/**
 * @brief 检查数据更新模块是否已初始化
 * @return true: 已初始化, false: 未初始化
 */
bool vehicle_data_updater_is_initialized(void);

// =============================================================================
// 灯光控制更新函数
// =============================================================================

/**
 * @brief 设置左转向灯状态
 * @param state 灯光状态 (true: 开启, false: 关闭)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_left_turn(bool state);

/**
 * @brief 设置右转向灯状态
 * @param state 灯光状态 (true: 开启, false: 关闭)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_right_turn(bool state);

/**
 * @brief 设置前大灯状态
 * @param state 灯光状态 (true: 开启, false: 关闭)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_headlight(bool state);

/**
 * @brief 设置刹车灯状态
 * @param state 灯光状态 (true: 开启, false: 关闭)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_brake_light(bool state);

/**
 * @brief 设置双闪灯状态
 * @param state 灯光状态 (true: 开启, false: 关闭)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_double_flash(bool state);

/**
 * @brief 设置复合灯光控制
 * @param left_turn 左转向灯状态
 * @param right_turn 右转向灯状态
 * @param headlight 前大灯状态
 * @param double_flash 双闪灯状态
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_light_control(bool left_turn, bool right_turn, 
                                  bool headlight, bool double_flash);

// =============================================================================
// 运行数据更新函数
// =============================================================================

/**
 * @brief 设置车辆速度
 * @param speed 速度值 (km/h)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_speed(uint16_t speed);

/**
 * @brief 设置当前功率
 * @param power 功率值 (W)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_power(uint32_t power);

/**
 * @brief 设置控制器温度
 * @param temperature 温度值 (°C)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_temperature(float temperature);

/**
 * @brief 设置里程信息
 * @param total_mileage 总里程 (km)
 * @param trip_mileage 小计里程 (km)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_mileage(uint32_t total_mileage, uint32_t trip_mileage);

/**
 * @brief 设置行程时间
 * @param trip_time 行程时间 (秒)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_trip_time(uint32_t trip_time);

/**
 * @brief 设置当前挡位
 * @param gear 挡位 (0: 空挡, 1-5: 1-5挡)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_gear(uint8_t gear);

/**
 * @brief 设置剩余续航
 * @param range 剩余续航里程 (km)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_remain_range(uint16_t range);

// =============================================================================
// 电池数据更新函数
// =============================================================================

/**
 * @brief 设置电池电量
 * @param level 电池电量百分比 (0-100)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_battery_level(uint8_t level);

/**
 * @brief 设置充电状态
 * @param charging 充电状态 (true: 充电中, false: 未充电)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_charging_state(bool charging);

/**
 * @brief 设置电池电压
 * @param voltage 电池电压 (V)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_voltage(float voltage);

// =============================================================================
// 时间数据更新函数
// =============================================================================

/**
 * @brief 设置时间
 * @param hour 小时 (0-23)
 * @param minute 分钟 (0-59)
 * @param second 秒 (0-59)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_time(uint8_t hour, uint8_t minute, uint8_t second);

/**
 * @brief 设置日期
 * @param year 年份 (2020-2099)
 * @param month 月份 (1-12)
 * @param day 日期 (1-31)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_date(uint16_t year, uint8_t month, uint8_t day);

// =============================================================================
// 姿态数据更新函数
// =============================================================================

/**
 * @brief 设置车身角度
 * @param pitch 俯仰角 (度)
 * @param roll 横滚角 (度)
 * @param yaw 偏航角 (度)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_attitude(float pitch, float roll, float yaw);

/**
 * @brief 设置倾斜状态
 * @param tilted 是否倾斜 (true: 倾斜, false: 正常)
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_set_tilt_status(bool tilted);

#ifdef __cplusplus
}
#endif

#endif // VEHICLE_DATA_SETTER_H
