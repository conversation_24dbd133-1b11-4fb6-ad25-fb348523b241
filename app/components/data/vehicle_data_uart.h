/**
 * @file vehicle_data_uart.h
 * @brief 车辆数据UART事件处理模块接口
 * @version 1.0.0
 * @date 2025-07-31 14:05 GMT+8
 * <AUTHOR> (全栈开发者)
 */

#ifndef VEHICLE_DATA_UART_H
#define VEHICLE_DATA_UART_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

// =============================================================================
// 数据结构定义
// =============================================================================

/**
 * @brief UART数据帧结构（与plug_uart.c保持一致）
 */
typedef struct {
    uint8_t id;           /**< 消息ID */
    uint8_t data[256];    /**< 数据内容 */
    uint16_t data_len;    /**< 数据长度 */
} uart_frame_data_t;

// =============================================================================
// 公共接口函数
// =============================================================================

/**
 * @brief 初始化UART事件处理模块
 * @return 0: 成功, -1: 失败
 */
int vehicle_data_uart_init(void);

/**
 * @brief 清理UART事件处理模块
 */
void vehicle_data_uart_deinit(void);

/**
 * @brief 检查UART模块是否已初始化
 * @return true: 已初始化, false: 未初始化
 */
bool vehicle_data_uart_is_initialized(void);

// =============================================================================
// 内部函数声明（仅供模块内部使用）
// =============================================================================

// 注意：以下函数为内部使用，通过event_bus自动调用，不对外暴露

#ifdef __cplusplus
}
#endif

#endif // VEHICLE_DATA_UART_H
