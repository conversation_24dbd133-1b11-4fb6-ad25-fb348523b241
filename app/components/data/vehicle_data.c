/**
 * @file vehicle_data.c
 * @brief 车辆数据管理核心协调器 (重构后)
 * @version 2.0.0
 * @date 2025-08-01 14:30 GMT+8
 * <AUTHOR> (全栈开发者)
 *
 * 重构说明:
 * - UART处理已移动到vehicle_data_uart.c
 * - 数据更新已移动到vehicle_data_setter.c
 * - 本模块专注于核心协调和数据访问接口
 */

#include "vehicle_data.h"
#include "vehicle_data_uart.h"
#include "vehicle_data_setter.h"
#include "app/ebike_x1.h"
#include "app/constant.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>

// =============================================================================
// 内部数据和状态
// =============================================================================

static vehicle_data_t g_vehicle_data = {0};
static bool g_initialized = false;
static bool g_event_bus_enabled = false;

// =============================================================================
// 初始化和清理函数
// =============================================================================

int vehicle_data_init(void) {
    if (g_initialized) {
        return 0; // 已经初始化
    }

    // 初始化数据结构
    memset(&g_vehicle_data, 0, sizeof(vehicle_data_t));

    // 设置默认值
    strcpy(g_vehicle_data.system.sw_version, "1.0.0");
    strcpy(g_vehicle_data.system.hw_version, "1.0.0");
    
    // 运行状态 
    g_vehicle_data.running.cruise_enabled = false;
    g_vehicle_data.running.cruise_speed = 100;
    g_vehicle_data.running.gear = 1;
    g_vehicle_data.running.work_mode = 2;
    g_vehicle_data.running.total_mileage = 12345;
    g_vehicle_data.running.trip_mileage = 123;
    g_vehicle_data.running.power_current = 100;
    g_vehicle_data.running.power_average = 88;
    g_vehicle_data.running.trip_time = 12; 
    g_vehicle_data.running.temperature = 26; 

    // 车身姿态
    g_vehicle_data.attitude.pitch = 12; 
    g_vehicle_data.attitude.roll = 12; 
    g_vehicle_data.attitude.acceleration = 0.17; 

    // 电池状态
    g_vehicle_data.battery.level = 12; 
    g_vehicle_data.battery.is_charging = false;
    g_vehicle_data.battery.voltage = 72.f;
    g_vehicle_data.battery.current = 10.f;

    // 灯光系统默认全部关闭
    g_vehicle_data.light.left_turn = false;
    g_vehicle_data.light.right_turn = false;
    g_vehicle_data.light.runlight = false;
    g_vehicle_data.light.double_flash = false;
    g_vehicle_data.light.brake_light = false;
    g_vehicle_data.light.headlight = false;


    // 先设置初始化标志
    g_event_bus_enabled = true;
    g_initialized = true;

    // 初始化UART事件处理模块
    if (vehicle_data_uart_init() != 0) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA] UART事件处理模块初始化失败");
        g_initialized = false;
        return -1;
    }

    g_initialized = true;
    VEHICLE_DATA_DEBUG_PRINT("车辆数据管理模块初始化完成（已启用event_bus集成）");
    return 0;
}

void vehicle_data_deinit(void) {
    if (!g_initialized) {
        return;
    }

    // 清理UART事件处理模块
    vehicle_data_uart_deinit();

    g_initialized = false;
    VEHICLE_DATA_DEBUG_PRINT("车辆数据管理模块已清理");
}

// =============================================================================
// 数据访问函数
// =============================================================================

const vehicle_data_t* vehicle_data_get_all(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data;
}

const vehicle_light_data_t* vehicle_data_get_light(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.light;
}

const vehicle_battery_data_t* vehicle_data_get_battery(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.battery;
}

const vehicle_running_data_t* vehicle_data_get_running(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.running;
}

const vehicle_attitude_data_t* vehicle_data_get_attitude(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.attitude;
}

const vehicle_system_data_t* vehicle_data_get_system(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.system;
}

const vehicle_time_data_t* vehicle_data_get_time(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.time;
}

// =============================================================================
// 事件发布接口实现（使用event_bus）
// =============================================================================

void vehicle_data_publish_event(uint16_t event_id, const void *data, size_t data_size) {

    if (!data || data_size == 0) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_PUBLISHER] 事件发布失败: 数据为空或大小为0");
        return;
    }

    // 分配内存并复制数据
    void *event_data = malloc(data_size);
    if (!event_data) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_PUBLISHER] 事件发布失败: 内存分配失败, 大小=%zu", data_size);
        return;
    }

    memcpy(event_data, data, data_size);

    // 发布事件到event_bus
    if (eb_publish(event_id, event_data)) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA] 事件发布成功: ID=0x%04X, 大小=%zu", event_id, data_size);
        // 注意: event_data的内存由event_bus负责释放
    } else {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA] 事件发布失败: ID=0x%04X, 大小=%zu", event_id, data_size);
        free(event_data);  // 发布失败时释放内存
    }
}

// =============================================================================
// 内部接口实现（供其他模块使用）
// =============================================================================

vehicle_data_t* vehicle_data_get_mutable(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data;
}

uint32_t get_current_timestamp(void) {
    // 使用现有的全局时间变量
    return g_time_second * 1000 + g_time_ms;
}

void vehicle_data_sync_to_globals(void) {

}