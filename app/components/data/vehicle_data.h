/**
 * @file vehicle_data.h
 * @brief 车辆数据管理模块 - 统一管理所有车辆状态数据
 * @version 1.0.0
 * @date 2025-07-22
 */

#ifndef VEHICLE_DATA_H
#define VEHICLE_DATA_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>  // for size_t

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 灯光状态结构体
 */
typedef struct {
    bool runlight;       /**< 日行灯 */
    bool left_turn;      /**< 左转向灯状态 */
    bool right_turn;     /**< 右转向灯状态 */
    bool headlight;      /**< 前大灯状态 */
    bool double_flash;   /**< 双闪状态 */
    bool brake_light;    /**< 刹车灯状态 */
    bool reverse_light;  /**< 倒车灯状态 */
    uint32_t last_update; /**< 最后更新时间戳 */
} vehicle_light_data_t;

/**
 * @brief 电池状态结构体
 */
typedef struct {
    uint8_t level;       /**< 电池电量百分比 (0-100) */
    bool is_charging;    /**< 是否正在充电 */
    float voltage;       /**< 电池电压 */
    float current;       /**< 电池电流 */
    uint32_t last_update; /**< 最后更新时间戳 */
} vehicle_battery_data_t;

/**
 * @brief 运行状态结构体
 */
typedef struct {
    uint16_t speed;         /**< 当前速度 km/h */
    uint8_t gear;           /**< 当前挡位 (0-5) 0-P档 */
    uint32_t total_mileage; /**< 总里程 km */
    uint32_t trip_mileage;  /**< 小计里程 km */
    uint16_t remain_range;  /**< 剩余续航里程 km */
    float temperature;      /**< 温度 °C */
    uint32_t power_current; /**< 当前功率 KW */
    uint32_t power_average; /**< 平均功率 W */
    uint32_t trip_time;     /**< 行程时间(分钟) */
    bool cruise_enabled;    /**< 巡航开启状态 */
    uint16_t cruise_speed;  /**< 巡航速度 km/h */
    uint32_t last_update;   /**< 最后更新时间戳 */
    uint8_t work_mode;      /**< 工作模式 (0-2) */
} vehicle_running_data_t;

/**
 * @brief 车身姿态结构体
 */
typedef struct {
    float pitch;         /**< 俯仰角 */
    float roll;          /**< 横滚角 */
    float acceleration;  /**< 加速度 */
    uint32_t last_update; /**< 最后更新时间戳 */
} vehicle_attitude_data_t;

/**
 * @brief 时间数据结构体
 */
typedef struct {
    uint8_t hour;         /**< 小时 (0-23) */
    uint8_t minute;       /**< 分钟 (0-59) */
    uint8_t second;       /**< 秒 (0-59) */
    uint16_t millisecond; /**< 毫秒 (0-999) */
    uint16_t year;        /**< 年 */
    uint8_t month;        /**< 月 (1-12) */
    uint8_t day;          /**< 日 (1-31) */
    uint32_t last_update; /**< 最后更新时间戳 */
} vehicle_time_data_t;

/**
 * @brief 系统状态结构体
 */
typedef struct {
    char sw_version[20]; /**< 软件版本 */
    char hw_version[20]; /**< 硬件版本 */
    uint32_t uptime;     /**< 系统运行时间 */
    uint32_t last_update; /**< 最后更新时间戳 */
} vehicle_system_data_t;

/**
 * @brief 车辆完整数据结构体
 */
typedef struct {
    vehicle_light_data_t light;      /**< 灯光数据 */
    vehicle_battery_data_t battery;  /**< 电池数据 */
    vehicle_running_data_t running;  /**< 运行数据 */
    vehicle_attitude_data_t attitude; /**< 车身姿态数据 */
    vehicle_system_data_t system;    /**< 系统数据 */
    vehicle_time_data_t time;        /**< 时间数据 */
} vehicle_data_t;

// =============================================================================
// 初始化和清理函数
// =============================================================================

/**
 * @brief 初始化车辆数据管理模块
 * @return 0 成功，-1 失败
 */
int vehicle_data_init(void);

/**
 * @brief 清理车辆数据管理模块
 */
void vehicle_data_deinit(void);

// =============================================================================
// 数据访问函数
// =============================================================================

/**
 * @brief 获取完整的车辆数据
 * @return 车辆数据指针（只读）
 */
const vehicle_data_t* vehicle_data_get_all(void);

/**
 * @brief 获取灯光数据
 * @return 灯光数据指针（只读）
 */
const vehicle_light_data_t* vehicle_data_get_light(void);

/**
 * @brief 获取电池数据
 * @return 电池数据指针（只读）
 */
const vehicle_battery_data_t* vehicle_data_get_battery(void);

/**
 * @brief 获取运行数据
 * @return 运行数据指针（只读）
 */
const vehicle_running_data_t* vehicle_data_get_running(void);

/**
 * @brief 获取车身姿态数据
 * @return 车身姿态数据指针（只读）
 */
const vehicle_attitude_data_t* vehicle_data_get_attitude(void);

/**
 * @brief 获取系统数据
 * @return 系统数据指针（只读）
 */
const vehicle_system_data_t* vehicle_data_get_system(void);

/**
 * @brief 获取时间数据
 * @return 时间数据指针（只读）
 */
const vehicle_time_data_t* vehicle_data_get_time(void);

// =============================================================================
// 数据更新函数
// =============================================================================

/**
 * @brief 更新灯光数据
 * @param light 新的灯光数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_light(const vehicle_light_data_t* light);

/**
 * @brief 更新电池数据
 * @param battery 新的电池数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_battery(const vehicle_battery_data_t* battery);

/**
 * @brief 更新运行数据
 * @param running 新的运行数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_running(const vehicle_running_data_t* running);

/**
 * @brief 更新车身姿态数据
 * @param attitude 新的车身姿态数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_attitude(const vehicle_attitude_data_t* attitude);

/**
 * @brief 更新系统数据
 * @param system 新的系统数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_system(const vehicle_system_data_t* system);

/**
 * @brief 更新时间数据
 * @param time 新的时间数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_time(const vehicle_time_data_t* time);

/**
 * @brief 发布数据变化事件到event_bus
 * @param event_id 事件ID
 * @param data 事件数据指针
 * @param data_size 数据大小
 */
void vehicle_data_publish_event(uint16_t event_id, const void *data, size_t data_size);

// =============================================================================
// 兼容性函数（与现有全局变量兼容）
// =============================================================================

/**
 * @brief 同步数据到全局变量（向后兼容）
 */
void vehicle_data_sync_to_globals(void);

/**
 * @brief 从全局变量同步数据
 */
void vehicle_data_sync_from_globals(void);

// =============================================================================
// 内部接口（仅供模块内部使用）
// =============================================================================

/**
 * @brief 获取可变的车辆数据指针（仅供内部模块使用）
 * @return 车辆数据指针（可写）
 * @warning 此函数仅供vehicle_data_updater等内部模块使用，不对外暴露
 */
vehicle_data_t* vehicle_data_get_mutable(void);

/**
 * @brief 通知订阅者数据变化（仅供内部模块使用）
 * @param data_type 数据类型
 * @param data 数据指针
 */
void notify_subscribers(const char* data_type, const void* data);

/**
 * @brief 获取当前时间戳（仅供内部模块使用）
 * @return 时间戳
 */
uint32_t get_current_timestamp(void);

/**
 * @brief 发布数据变化事件（仅供内部模块使用）
 * @param event_id 事件ID
 * @param data 数据指针
 * @param data_size 数据大小
 */
void publish_data_change_event(uint16_t event_id, const void *data, size_t data_size);


// =============================================================================
// 调试宏
// =============================================================================

#ifdef VEHICLE_DATA_DEBUG
#include <stdio.h>
#define VEHICLE_DATA_DEBUG_PRINT(fmt, ...) \
    printf("" fmt "\n", ##__VA_ARGS__)
#else
#define VEHICLE_DATA_DEBUG_PRINT(fmt, ...)
#endif

#ifdef __cplusplus
}
#endif

#endif // VEHICLE_DATA_H
