/**
 * @file vehicle_data_updater.c
 * @brief 车辆数据更新模块实现
 * @version 1.0.0
 * @date 2025-07-31 14:35 GMT+8
 * <AUTHOR> (全栈开发者)
 */

#include "vehicle_data_setter.h"
#include "vehicle_data.h"
#include "app/constant.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>

// =============================================================================
// 内部函数声明
// =============================================================================

// 内部辅助函数
static bool is_vehicle_data_initialized(void);

// =============================================================================
// 内部辅助函数实现
// =============================================================================

static bool is_vehicle_data_initialized(void) {
    return vehicle_data_get_all() != NULL;
}

// =============================================================================
// 灯光控制更新函数实现
// =============================================================================

int vehicle_data_set_left_turn(bool state) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置左转向灯失败: 模块未初始化");
        return -1;
    }
    
    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }
    
    if (data->light.left_turn != state) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 左转向灯状态变化: %s -> %s",
               data->light.left_turn ? "开启" : "关闭",
               state ? "开启" : "关闭");
        data->light.left_turn = state;
        data->light.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布灯光数据变化事件
        vehicle_data_publish_event(EVENT_DATA_LIGHT_CHANGED, &data->light, sizeof(vehicle_light_data_t));
    }
    
    return 0;
}

int vehicle_data_set_right_turn(bool state) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置右转向灯失败: 模块未初始化");
        return -1;
    }
    
    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }
    
    if (data->light.right_turn != state) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 右转向灯状态变化: %s -> %s",
               data->light.right_turn ? "开启" : "关闭",
               state ? "开启" : "关闭");
        data->light.right_turn = state;
        data->light.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布灯光数据变化事件
        vehicle_data_publish_event(EVENT_DATA_LIGHT_CHANGED, &data->light, sizeof(vehicle_light_data_t));
    }
    
    return 0;
}

int vehicle_data_set_headlight(bool state) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置前大灯失败: 模块未初始化");
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->light.headlight != state) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 前大灯状态变化: %s -> %s",
               data->light.headlight ? "开启" : "关闭",
               state ? "开启" : "关闭");
        data->light.headlight = state;
        data->light.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布灯光数据变化事件
        vehicle_data_publish_event(EVENT_DATA_LIGHT_CHANGED, &data->light, sizeof(vehicle_light_data_t));
    }

    return 0;
}

int vehicle_data_set_brake_light(bool state) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置刹车灯失败: 模块未初始化");
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->light.brake_light != state) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 刹车灯状态变化: %s -> %s",
               data->light.brake_light ? "开启" : "关闭",
               state ? "开启" : "关闭");
        data->light.brake_light = state;
        data->light.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布灯光数据变化事件
        vehicle_data_publish_event(EVENT_DATA_LIGHT_CHANGED, &data->light, sizeof(vehicle_light_data_t));
    }

    return 0;
}

int vehicle_data_set_double_flash(bool state) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置双闪灯失败: 模块未初始化");
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->light.double_flash != state) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 双闪灯状态变化: %s -> %s",
               data->light.double_flash ? "开启" : "关闭",
               state ? "开启" : "关闭");

        // 只修改双闪状态，保持左右转向灯的独立性
        data->light.double_flash = state;
        data->light.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();

        // 发布灯光数据变化事件
        vehicle_data_publish_event(EVENT_DATA_LIGHT_CHANGED, &data->light, sizeof(vehicle_light_data_t));
    }

    return 0;
}

int vehicle_data_set_light_control(bool left_turn, bool right_turn, 
                                  bool headlight, bool double_flash) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置复合灯光控制失败: 模块未初始化");
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    bool changed = false;
    
    if (data->light.left_turn != left_turn) {
        data->light.left_turn = left_turn;
        changed = true;
    }
    
    if (data->light.right_turn != right_turn) {
        data->light.right_turn = right_turn;
        changed = true;
    }
    
    if (data->light.headlight != headlight) {
        data->light.headlight = headlight;
        changed = true;
    }
    
    if (data->light.double_flash != double_flash) {
        data->light.double_flash = double_flash;
        changed = true;
    }

    if (changed) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 复合灯光控制更新: 左转=%s, 右转=%s, 前灯=%s, 双闪=%s",
               left_turn ? "开" : "关", right_turn ? "开" : "关",
               headlight ? "开" : "关", double_flash ? "开" : "关");
        data->light.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布灯光数据变化事件
        vehicle_data_publish_event(EVENT_DATA_LIGHT_CHANGED, &data->light, sizeof(vehicle_light_data_t));
    }

    return 0;
}

// =============================================================================
// 运行数据更新函数实现
// =============================================================================

int vehicle_data_set_speed(uint16_t speed) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置速度失败: 模块未初始化");
        return -1;
    }

    // 速度合理性检查
    if (speed > 200) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 速度数据异常: %d km/h > 200", speed);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->running.speed != speed) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 速度变化: %d -> %d km/h",
               data->running.speed, speed);
        data->running.speed = speed;
        data->running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布运行数据变化事件
        vehicle_data_publish_event(EVENT_DATA_RUNNING_CHANGED, &data->running, sizeof(vehicle_running_data_t));
    }

    return 0;
}

int vehicle_data_set_power(uint32_t power) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置功率失败: 模块未初始化");
        return -1;
    }

    // 功率合理性检查
    if (power > 50000) {  // 50kW上限
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 功率数据异常: %u W > 50000", power);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->running.power_current != power) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 功率变化: %u -> %u W",
               data->running.power_current, power);
        data->running.power_current = power;
        data->running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布运行数据变化事件
        vehicle_data_publish_event(EVENT_DATA_RUNNING_CHANGED, &data->running, sizeof(vehicle_running_data_t));
    }

    return 0;
}

int vehicle_data_set_temperature(float temperature) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置温度失败: 模块未初始化");
        return -1;
    }

    // 温度合理性检查
    if (temperature < -40.0f || temperature > 85.0f) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 温度数据异常: %.1f°C 超出合理范围", temperature);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->running.temperature != temperature) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 温度变化: %.1f -> %.1f°C",
               data->running.temperature, temperature);
        data->running.temperature = temperature;
        data->running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布运行数据变化事件
        vehicle_data_publish_event(EVENT_DATA_RUNNING_CHANGED, &data->running, sizeof(vehicle_running_data_t));
    }

    return 0;
}

int vehicle_data_set_gear(uint8_t gear) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置挡位失败: 模块未初始化");
        return -1;
    }

    // 挡位合理性检查
    if (gear > 5) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 挡位数据异常: %d > 5", gear);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->running.gear != gear) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 挡位变化: %d -> %d",
               data->running.gear, gear);
        data->running.gear = gear;
        data->running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布运行数据变化事件
        vehicle_data_publish_event(EVENT_DATA_RUNNING_CHANGED, &data->running, sizeof(vehicle_running_data_t));
    }

    return 0;
}

int vehicle_data_set_remain_range(uint16_t range) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置剩余续航失败: 模块未初始化");
        return -1;
    }

    // 续航合理性检查
    if (range > 1000) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 续航数据异常: %d km > 1000", range);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->running.remain_range != range) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 剩余续航变化: %d -> %d km",
               data->running.remain_range, range);
        data->running.remain_range = range;
        data->running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布运行数据变化事件
        vehicle_data_publish_event(EVENT_DATA_RUNNING_CHANGED, &data->running, sizeof(vehicle_running_data_t));
    }

    return 0;
}

int vehicle_data_set_mileage(uint32_t total_mileage, uint32_t trip_mileage) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置里程失败: 模块未初始化");
        return -1;
    }

    // 里程合理性检查
    if (total_mileage > 999999 || trip_mileage > 9999) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 里程数据异常: 总里程=%u, 小计里程=%u",
               total_mileage, trip_mileage);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    bool changed = false;

    if (data->running.total_mileage != total_mileage) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 总里程变化: %u -> %u km",
               data->running.total_mileage, total_mileage);
        data->running.total_mileage = total_mileage;
        changed = true;
    }

    if (data->running.trip_mileage != trip_mileage) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 小计里程变化: %u -> %u km",
               data->running.trip_mileage, trip_mileage);
        data->running.trip_mileage = trip_mileage;
        changed = true;
    }

    if (changed) {
        data->running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布运行数据变化事件
        vehicle_data_publish_event(EVENT_DATA_RUNNING_CHANGED, &data->running, sizeof(vehicle_running_data_t));
    }

    return 0;
}

int vehicle_data_set_trip_time(uint32_t trip_time) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置行程时间失败: 模块未初始化");
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->running.trip_time != trip_time) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 行程时间变化: %u -> %u 秒",
               data->running.trip_time, trip_time);
        data->running.trip_time = trip_time;
        data->running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布运行数据变化事件
        vehicle_data_publish_event(EVENT_DATA_RUNNING_CHANGED, &data->running, sizeof(vehicle_running_data_t));
    }

    return 0;
}

// =============================================================================
// 电池数据更新函数实现
// =============================================================================

int vehicle_data_set_battery_level(uint8_t level) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置电池电量失败: 模块未初始化");
        return -1;
    }

    // 电量合理性检查
    if (level > 100) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 电池电量数据异常: %d%% > 100", level);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->battery.level != level) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 电池电量变化: %d%% -> %d%%",
               data->battery.level, level);
        data->battery.level = level;
        data->battery.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布电池数据变化事件
        vehicle_data_publish_event(EVENT_DATA_BATTERY_CHANGED, &data->battery, sizeof(vehicle_battery_data_t));
    }

    return 0;
}

int vehicle_data_set_charging_state(bool charging) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置充电状态失败: 模块未初始化");
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->battery.is_charging != charging) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 充电状态变化: %s -> %s",
               data->battery.is_charging ? "充电中" : "未充电",
               charging ? "充电中" : "未充电");
        data->battery.is_charging = charging;
        data->battery.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布电池数据变化事件
        vehicle_data_publish_event(EVENT_DATA_BATTERY_CHANGED, &data->battery, sizeof(vehicle_battery_data_t));
    }

    return 0;
}

int vehicle_data_set_voltage(float voltage) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置电池电压失败: 模块未初始化");
        return -1;
    }

    // 电压合理性检查
    if (voltage < 0.0f || voltage > 100.0f) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 电池电压数据异常: %.1fV 超出合理范围", voltage);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    if (data->battery.voltage != voltage) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 电池电压变化: %.1f -> %.1fV",
               data->battery.voltage, voltage);
        data->battery.voltage = voltage;
        data->battery.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布电池数据变化事件
        vehicle_data_publish_event(EVENT_DATA_BATTERY_CHANGED, &data->battery, sizeof(vehicle_battery_data_t));
    }

    return 0;
}

// =============================================================================
// 时间数据更新函数实现
// =============================================================================

int vehicle_data_set_time(uint8_t hour, uint8_t minute, uint8_t second) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置时间失败: 模块未初始化");
        return -1;
    }

    // 时间合理性检查
    if (hour > 23 || minute > 59 || second > 59) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 时间数据异常: %02d:%02d:%02d", hour, minute, second);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    bool changed = false;

    if (data->time.hour != hour) {
        data->time.hour = hour;
        changed = true;
    }

    if (data->time.minute != minute) {
        data->time.minute = minute;
        changed = true;
    }

    if (data->time.second != second) {
        data->time.second = second;
        changed = true;
    }

    if (changed) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 时间更新: %02d:%02d:%02d", hour, minute, second);
        data->time.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布时间数据变化事件
        vehicle_data_publish_event(EVENT_DATA_TIME_CHANGED, &data->time, sizeof(vehicle_time_data_t));
    }

    return 0;
}

int vehicle_data_set_date(uint16_t year, uint8_t month, uint8_t day) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置日期失败: 模块未初始化");
        return -1;
    }

    // 日期合理性检查
    if (year < 2020 || year > 2099 || month < 1 || month > 12 || day < 1 || day > 31) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 日期数据异常: %04d-%02d-%02d", year, month, day);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    bool changed = false;

    if (data->time.year != year) {
        data->time.year = year;
        changed = true;
    }

    if (data->time.month != month) {
        data->time.month = month;
        changed = true;
    }

    if (data->time.day != day) {
        data->time.day = day;
        changed = true;
    }

    if (changed) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 日期更新: %04d-%02d-%02d", year, month, day);
        data->time.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布时间数据变化事件
        vehicle_data_publish_event(EVENT_DATA_TIME_CHANGED, &data->time, sizeof(vehicle_time_data_t));
    }

    return 0;
}

// =============================================================================
// 姿态数据更新函数实现
// =============================================================================

int vehicle_data_set_attitude(float pitch, float roll, float yaw) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置姿态失败: 模块未初始化");
        return -1;
    }

    // 角度合理性检查
    if (pitch < -90.0f || pitch > 90.0f ||
        roll < -180.0f || roll > 180.0f) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 姿态数据异常: pitch=%.1f, roll=%.1f",
               pitch, roll);
        return -1;
    }

    vehicle_data_t* data = vehicle_data_get_mutable();
    if (!data) {
        return -1;
    }

    bool changed = false;

    if (data->attitude.pitch != pitch) {
        data->attitude.pitch = pitch;
        changed = true;
    }

    if (data->attitude.roll != roll) {
        data->attitude.roll = roll;
        changed = true;
    }

    if (changed) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 姿态更新: pitch=%.1f°, roll=%.1f°",
               pitch, roll);
        data->attitude.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        

        // 发布姿态数据变化事件
        vehicle_data_publish_event(EVENT_DATA_ATTITUDE_CHANGED, &data->attitude, sizeof(vehicle_attitude_data_t));
    }

    // 注意：yaw参数被忽略，因为当前vehicle_attitude_data_t结构体没有yaw字段
    if (yaw != 0.0f) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 注意: yaw=%.1f° 被忽略，当前数据结构不支持偏航角", yaw);
    }

    return 0;
}

int vehicle_data_set_tilt_status(bool tilted) {
    if (!is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 设置倾斜状态失败: 模块未初始化");
        return -1;
    }

    // 注意：当前vehicle_attitude_data_t结构体没有is_tilted字段
    // 这个函数暂时只记录日志，不实际更新数据
    // 如果需要倾斜状态，应该在vehicle_attitude_data_t中添加is_tilted字段

    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 倾斜状态更新请求: %s", tilted ? "倾斜" : "正常");
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UPDATER] 注意: 当前数据结构不支持倾斜状态字段");

    return 0;
}
