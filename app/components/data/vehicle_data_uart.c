/**
 * @file vehicle_data_uart.c
 * @brief 车辆数据UART事件处理模块实现
 * @version 1.0.0
 * @date 2025-07-31 14:05 GMT+8
 * <AUTHOR> (全栈开发者)
 */

#include "vehicle_data_uart.h"
#include "vehicle_data.h"
#include "vehicle_data_setter.h"
#include "app/constant.h"
#include "event_bus/include/eb_core.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>

// =============================================================================
// 内部状态变量
// =============================================================================

static bool g_uart_initialized = false;

// =============================================================================
// 内部函数声明
// =============================================================================

// UART事件处理器
static void handle_uart_vehicle_status(uint16_t id, void *data, void *ctx);
static void handle_uart_speed_info(uint16_t id, void *data, void *ctx);
static void handle_uart_light_control(uint16_t id, void *data, void *ctx);
static void handle_uart_mileage_data(uint16_t id, void *data, void *ctx);
static void handle_uart_time_data(uint16_t id, void *data, void *ctx);

// 数据解析函数
static void parse_controller_status(const uint8_t *data);
static void parse_battery_data(const uint8_t *data);
static void parse_attitude_data(const uint8_t *data);

// 工具函数
static bool is_vehicle_data_initialized(void);

// =============================================================================
// 公共接口实现
// =============================================================================

int vehicle_data_uart_init(void) {
    if (g_uart_initialized) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 模块已经初始化");
        return 0;
    }

    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 初始化UART事件处理模块");

    // 订阅UART相关事件
    if (!eb_subscribe(EVENT_BIZ_VEHICLE_STATUS, handle_uart_vehicle_status, NULL)) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 订阅车辆状态事件失败");
        return -1;
    } else {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 订阅车辆状态事件成功: ID=0x%04X", EVENT_BIZ_VEHICLE_STATUS);
    }

    if (!eb_subscribe(EVENT_BIZ_SPEED_INFO, handle_uart_speed_info, NULL)) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 订阅速度信息事件失败");
        return -1;
    } else {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 订阅速度信息事件成功: ID=0x%04X", EVENT_BIZ_SPEED_INFO);
    }

    if (!eb_subscribe(EVENT_BIZ_LIGHT_CONTROL, handle_uart_light_control, NULL)) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 订阅灯光控制事件失败");
        return -1;
    } else {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 订阅灯光控制事件成功: ID=0x%04X", EVENT_BIZ_LIGHT_CONTROL);
    }

    if (!eb_subscribe(EVENT_BIZ_MILEAGE_DATA, handle_uart_mileage_data, NULL)) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 订阅里程数据事件失败");
        return -1;
    } else {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 订阅里程数据事件成功: ID=0x%04X", EVENT_BIZ_MILEAGE_DATA);
    }

    if (!eb_subscribe(EVENT_BIZ_TIME_DATA, handle_uart_time_data, NULL)) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 订阅时间数据事件失败");
        return -1;
    } else {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 订阅时间数据事件成功: ID=0x%04X", EVENT_BIZ_TIME_DATA);
    }

    g_uart_initialized = true;
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] UART事件处理模块初始化完成");
    return 0;
}

void vehicle_data_uart_deinit(void) {
    if (!g_uart_initialized) {
        return;
    }

    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 清理UART事件处理模块");

    // 取消所有UART事件订阅
    eb_unsubscribe(EVENT_BIZ_VEHICLE_STATUS, handle_uart_vehicle_status);
    eb_unsubscribe(EVENT_BIZ_SPEED_INFO, handle_uart_speed_info);
    eb_unsubscribe(EVENT_BIZ_LIGHT_CONTROL, handle_uart_light_control);
    eb_unsubscribe(EVENT_BIZ_MILEAGE_DATA, handle_uart_mileage_data);
    eb_unsubscribe(EVENT_BIZ_TIME_DATA, handle_uart_time_data);

    g_uart_initialized = false;
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] UART事件处理模块已清理");
}

bool vehicle_data_uart_is_initialized(void) {
    return g_uart_initialized;
}

// =============================================================================
// 内部函数实现
// =============================================================================

static bool is_vehicle_data_initialized(void) {
    // 检查主vehicle_data模块是否已初始化
    // 通过尝试获取数据来判断
    return vehicle_data_get_all() != NULL;
}

/**
 * @brief 处理UART车辆状态事件
 */
static void handle_uart_vehicle_status(uint16_t id, void *data, void *ctx) {
    if (!data || !is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] UART车辆状态事件处理失败: 数据为空或模块未初始化");
        return;
    }

    uart_frame_data_t *frame = (uart_frame_data_t *)data;

    // 数据有效性验证
    if (frame->data_len > sizeof(frame->data)) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] UART数据长度异常: %d > %zu", frame->data_len, sizeof(frame->data));
        return;
    }

    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 处理UART车辆状态事件: ID=0x%02X, 长度=%d", frame->id, frame->data_len);

    switch (frame->id) {
        case UART_MSG_GEAR: //挡位
            if (frame->data_len >= 1) {
                uint8_t gear = frame->data[0];
                // 挡位数据验证
                if (gear <= 5) {  // 假设最大5挡
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 更新挡位(0x62): %d", gear);
                    vehicle_data_set_gear(gear);
                } else {
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 挡位数据无效(0x62): %d > 5", gear);
                }
            } else {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 挡位数据长度不足: %d < 1", frame->data_len);
            }
            break;

        case UART_MSG_CONTROLLER_STATUS:  // 控制器状态
            if (frame->data_len >= 18) {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 解析中控控制器状态数据");
                parse_controller_status(frame->data);
            } else {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 控制器状态数据长度不足: %d < 18", frame->data_len);
            }
            break;

        case UART_MSG_REMAIN_RANGE:  // 0x64 剩余续航里程
            if (frame->data_len >= 2) {
                uint16_t remain_range = frame->data[0] | (frame->data[1] << 8);
                if (remain_range <= 1000) {  // 合理性检查：小于1000公里
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 更新剩余续航(0x64): %u km", remain_range);
                    vehicle_data_set_remain_range(remain_range);
                } else {
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 剩余续航数据异常(0x64): %u km > 1000", remain_range);
                }
            } else {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 剩余续航数据长度不足: %d < 2", frame->data_len);
            }
            break;

        case 0x6A:  // 电池数据 (模拟)
            if (frame->data_len >= 4) {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 解析电池数据");
                parse_battery_data(frame->data);
            } else {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 电池数据长度不足: %d < 4", frame->data_len);
            }
            break;

        case 0x6B:  // 姿态数据 (模拟)
            if (frame->data_len >= 8) {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 解析姿态数据");
                parse_attitude_data(frame->data);
            } else {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 姿态数据长度不足: %d < 8", frame->data_len);
            }
            break;

        default:
            VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 未处理的车辆状态消息: ID=0x%02X", frame->id);
            break;
    }
}

/**
 * @brief 处理UART速度信息事件
 */
static void handle_uart_speed_info(uint16_t id, void *data, void *ctx) {
    if (!data || !is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] UART速度信息事件处理失败: 数据为空或模块未初始化");
        return;
    }

    uart_frame_data_t *frame = (uart_frame_data_t *)data;

    // 数据有效性验证
    if (frame->data_len > sizeof(frame->data)) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] UART数据长度异常: %d > %zu", frame->data_len, sizeof(frame->data));
        return;
    }

    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 处理UART速度信息事件: ID=0x%02X, 长度=%d", frame->id, frame->data_len);

    switch (frame->id) {
        case 0x60:  // 功率同步消息 - 包含速度信息
            if (frame->data_len >= 2) {
                // 尝试不同的数据解析方式
                uint16_t speed_raw = frame->data[0] | (frame->data[1] << 8);  // 小端序
                uint16_t speed = speed_raw;  // 先尝试直接使用

                // 如果数值太大，可能需要除以某个因子
                if (speed > 1000) {
                    speed = speed / 100;  // 尝试除以100
                }

                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 原始速度数据(0x60): 0x%02X 0x%02X -> %d -> %d km/h",
                       frame->data[0], frame->data[1], speed_raw, speed);

                // 速度数据验证（假设最大速度120 km/h）
                if (speed <= 120) {
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 更新速度(0x60): %d km/h", speed);
                    vehicle_data_set_speed(speed);
                } else {
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 速度数据异常(0x60): %d km/h > 120", speed);
                }
            } else {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 功率同步数据长度不足: %d < 2", frame->data_len);
            }
            break;

        case 0x61:  // 心跳消息 - 包含转速信息
            if (frame->data_len >= 2) {
                // 尝试不同的数据解析方式
                uint16_t rpm_raw = frame->data[0] | (frame->data[1] << 8);  // 小端序
                uint16_t rpm = rpm_raw;

                // 如果数值太大，可能需要除以某个因子
                if (rpm > 20000) {
                    rpm = rpm / 10;  // 尝试除以10
                }

                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 原始转速数据(0x61): 0x%02X 0x%02X -> %d -> %d RPM",
                       frame->data[0], frame->data[1], rpm_raw, rpm);

                // 转速数据验证（假设最大转速10000 RPM）
                if (rpm <= 10000) {
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 更新转速(0x61): %d RPM", rpm);
                    // 这里可以添加转速数据更新逻辑
                    // vehicle_data_set_rpm(rpm);
                } else {
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 转速数据异常(0x61): %d RPM > 10000", rpm);
                }
            } else {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 心跳数据长度不足: %d < 2", frame->data_len);
            }
            break;

        case 0x65:  // UART_MSG_SPEED - 标准速度消息
            if (frame->data_len >= 1) {
                uint16_t speed = frame->data[0];
                // 速度数据验证（假设最大速度120 km/h）
                if (speed <= 120) {
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 更新速度(0x65): %d km/h", speed);
                    vehicle_data_set_speed(speed);
                } else {
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 速度数据异常(0x65): %d km/h > 120", speed);
                }
            } else {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 速度数据长度不足: %d < 1", frame->data_len);
            }
            break;

        case 0x68:  // UART_MSG_RPM - 标准转速消息
            if (frame->data_len >= 2) {
                uint16_t rpm = (frame->data[0] << 8) | frame->data[1];
                // 转速数据验证（假设最大转速10000 RPM）
                if (rpm <= 10000) {
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 更新转速(0x68): %d RPM", rpm);
                    // 这里可以添加转速数据更新逻辑
                    // vehicle_data_set_rpm(rpm);
                } else {
                    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 转速数据异常(0x68): %d RPM > 10000", rpm);
                }
            } else {
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 转速数据长度不足: %d < 2", frame->data_len);
            }
            break;

        default:
            VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 未处理的速度信息消息: ID=0x%02X", frame->id);
            break;
    }
}

/**
 * @brief 解析中控控制器状态数据
 */
static void parse_controller_status(const uint8_t *data) {
    if (!data) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 中控控制器状态数据为空");
        return;
    }

    // 解析控制器状态1 (Data0)
    uint8_t status1 = data[0];
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 控制器状态1: 0x%02X", status1);

    // 解析各种状态位
    bool tcs_status = (status1 & 0x01) != 0;        // TCS状态
    bool brake_status = (status1 & 0x02) != 0;      // 制动状态
    bool light_status = (status1 & 0x04) != 0;      // 灯光状态
    bool p_gear = (status1 & 0x10) != 0;            // P档状态

    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] TCS状态: %s", tcs_status ? "开启" : "关闭");
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 制动状态: %s", brake_status ? "制动中" : "未制动");
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 灯光状态: %s", light_status ? "开启" : "关闭");
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] P档状态: %s", p_gear ? "开启" : "关闭");

    // 更新车辆数据
    const vehicle_data_t* current_data = vehicle_data_get_all();
    if (current_data && current_data->light.headlight != light_status) {
        vehicle_data_set_headlight(light_status);
    }

    if (current_data && current_data->running.gear != (int)p_gear) {
        vehicle_data_set_gear((uint8_t)0);
    }

    // 解析温度 (Data3) - 添加合理性验证
    if (data[3] != 0xFE || data[3] != 0XFF) {
      float temperature = data[3] - 40.0f; // 偏移量

      // 温度合理性检查（-40°C 到 +213°C）
      if (temperature >= -40.0f && temperature <= 213.0f)
      {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 温度: %.1f°C", temperature);
        vehicle_data_set_temperature(temperature);
      } else {
            VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 温度数据异常: %.1f°C 超出合理范围", temperature);
        }
    }

    // 解析电池是否充电中 (Data15)
    uint8_t vehicle_state = data[15];
    if (vehicle_state != NULL) {
      bool is_charging = (vehicle_state & 0x01) != 0;
      VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 电池充电中: %s", is_charging ? "是" : "否");
      vehicle_data_set_charging_state(is_charging);
    }

    // 解析挡位信息 (Data4) - 假设在控制器状态中也包含挡位
    if (data[4] != 0xFF) {
        uint8_t gear = data[4];
        if (gear <= 5) {
            VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 控制器挡位: %d", gear);
            vehicle_data_set_gear(gear);
        }
    }

    // // 解析里程信息 (Data5-Data8) - 假设为总里程，4字节小端序
    // if (data[5] != 0xFF || data[6] != 0xFF || data[7] != 0xFF || data[8] != 0xFF) {
    //     uint32_t total_mileage = data[5] | (data[6] << 8) | (data[7] << 16) | (data[8] << 24);
    //     if (total_mileage < 999999) {  // 合理性检查：小于100万公里
    //         VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 总里程: %u km", total_mileage);
    //         // 只更新总里程，保持小计里程不变
    //         if (current_data) {
    //             vehicle_data_set_mileage(total_mileage, current_data->running.trip_mileage);
    //         }
    //     }
    // }

    // // 解析剩余续航 (Data9-Data10) - 假设为2字节小端序
    // if (data[9] != 0xFF || data[10] != 0xFF) {
    //     uint16_t remain_range = data[9] | (data[10] << 8);
    //     if (remain_range <= 1000) {  // 合理性检查：小于1000公里
    //         VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 剩余续航: %u km", remain_range);
    //         vehicle_data_set_remain_range(remain_range);
    //     }
    // }

    // // 解析功率信息 (Data11-Data12) - 假设为当前功率，2字节小端序
    // if (data[11] != 0xFF || data[12] != 0xFF) {
    //     uint16_t power = data[11] | (data[12] << 8);
    //     if (power <= 10000) {  // 合理性检查：小于10kW
    //         VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 当前功率: %u W", power);
    //         vehicle_data_set_power(power);
    //     }
    // }

    // 打印其他未解析的状态数据
    char all_data[sizeof(data) * 5 + 1] = {0};
    for (size_t i = 0; i < sizeof(data); ++i) {
        snprintf(&all_data[i * 5], sizeof(all_data) - i * 5, " 0x%02X", data[i]);
    }
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 中控控制器状态数据:%s", all_data);
}

/**
 * @brief 处理UART灯光控制事件
 */
static void handle_uart_light_control(uint16_t id, void *data, void *ctx) {
    if (!data || !is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] UART灯光控制事件处理失败: 数据为空或模块未初始化");
        return;
    }

    uart_frame_data_t *frame = (uart_frame_data_t *)data;
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 处理UART灯光控制事件: ID=0x%02X, 长度=%d", frame->id, frame->data_len);

    if (frame->id == UART_MSG_LIGHT_SCREEN_CTRL && frame->data_len >= 1) {
        uint8_t light_cmd = frame->data[0];
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 灯光控制命令: 0x%02X", light_cmd);

        // 根据灯光控制命令更新灯光状态
        bool left_turn = false, right_turn = false, headlight = false, double_flash = false;

        switch (light_cmd) {
            case 0x01:  // 打开双闪
                double_flash = true;
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 双闪灯开启");
                break;
            case 0x02:  // 关闭双闪
                double_flash = false;
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 双闪灯关闭");
                break;
            case 0x03:  // 打开左转向
                left_turn = true;
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 左转向灯开启");
                break;
            case 0x04:  // 打开右转向
                right_turn = true;
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 右转向灯开启");
                break;
            case 0x05:  // 关闭转向
                left_turn = false;
                right_turn = false;
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 转向灯关闭");
                break;
            case 0x06:  // 打开车灯
                headlight = true;
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 前大灯开启");
                break;
            default:
                VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 未知灯光控制命令: 0x%02X", light_cmd);
                return;
        }

        // 更新灯光数据
        vehicle_data_set_light_control(left_turn, right_turn, headlight, double_flash);
    } else {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 灯光控制数据格式错误: ID=0x%02X, 长度=%d", frame->id, frame->data_len);
    }
}

/**
 * @brief 处理UART里程数据事件
 */
static void handle_uart_mileage_data(uint16_t id, void *data, void *ctx) {
    if (!data || !is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] UART里程数据事件处理失败: 数据为空或模块未初始化");
        return;
    }

    uart_frame_data_t *frame = (uart_frame_data_t *)data;
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 处理UART里程数据事件: ID=0x%02X, 长度=%d", frame->id, frame->data_len);

    if (frame->id == UART_MSG_MILEAGE && frame->data_len >= 8) {
        // 解析总里程 (前4字节，小端序)
        uint32_t total_mileage = frame->data[0] | (frame->data[1] << 8) |
                                (frame->data[2] << 16) | (frame->data[3] << 24);

        // 解析小计里程 (后4字节，小端序)
        uint32_t trip_mileage = frame->data[4] | (frame->data[5] << 8) |
                               (frame->data[6] << 16) | (frame->data[7] << 24);

        // 数据合理性检查
        if (total_mileage <= 999999 && trip_mileage <= 9999) {
            VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 里程数据更新: 总里程=%u km, 小计里程=%u km",
                   total_mileage, trip_mileage);
            vehicle_data_set_mileage(total_mileage, trip_mileage);
        } else {
            VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 里程数据异常: 总里程=%u, 小计里程=%u",
                   total_mileage, trip_mileage);
        }
    } else {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 里程数据长度不足: %d < 8", frame->data_len);
    }
}

/**
 * @brief 处理UART时间数据事件
 */
static void handle_uart_time_data(uint16_t id, void *data, void *ctx) {
    if (!data || !is_vehicle_data_initialized()) {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] UART时间数据事件处理失败: 数据为空或模块未初始化");
        return;
    }

    uart_frame_data_t *frame = (uart_frame_data_t *)data;
    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 处理UART时间数据事件: ID=0x%02X, 长度=%d", frame->id, frame->data_len);

    if (frame->id == UART_MSG_TIME && frame->data_len >= 7) {
        // 解析时间数据：年(2字节) 月(1字节) 日(1字节) 时(1字节) 分(1字节) 秒(1字节)
        uint16_t year = frame->data[0] | (frame->data[1] << 8);
        uint8_t month = frame->data[2];
        uint8_t day = frame->data[3];
        uint8_t hour = frame->data[4];
        uint8_t minute = frame->data[5];
        uint8_t second = frame->data[6];

        // 数据合理性检查
        if (year >= 2020 && year <= 2099 &&
            month >= 1 && month <= 12 &&
            day >= 1 && day <= 31 &&
            hour <= 23 && minute <= 59 && second <= 59) {

            VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 时间数据更新: %04d-%02d-%02d %02d:%02d:%02d",
                   year, month, day, hour, minute, second);

            vehicle_data_set_date(year, month, day);
            vehicle_data_set_time(hour, minute, second);
        } else {
            VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 时间数据异常: %04d-%02d-%02d %02d:%02d:%02d",
                   year, month, day, hour, minute, second);
        }
    } else {
        VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 时间数据长度不足: %d < 7", frame->data_len);
    }
}

/**
 * @brief 解析电池数据
 */
static void parse_battery_data(const uint8_t *data) {
    if (!data) {
        return;
    }

    // 解析电池数据包: [电量%, 充电状态, 电压低字节, 电压高字节]
    uint8_t battery_level = data[0];
    bool charging = (data[1] != 0);
    uint16_t voltage_raw = data[2] | (data[3] << 8);
    float voltage = voltage_raw / 10.0f;

    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 电池数据: 电量=%d%%, 充电=%s, 电压=%.1fV",
                            battery_level, charging ? "是" : "否", voltage);

    // 更新电池数据
    vehicle_data_set_battery_level(battery_level);
    vehicle_data_set_charging_state(charging);
    vehicle_data_set_voltage(voltage);
}

/**
 * @brief 解析姿态数据
 */
static void parse_attitude_data(const uint8_t *data) {
    if (!data) {
        return;
    }

    // 解析姿态数据包: [俯仰角低, 俯仰角高, 横滚角低, 横滚角高, 加速度低, 加速度高, 保留, 保留]
    int16_t pitch_raw = data[0] | (data[1] << 8);
    int16_t roll_raw = data[2] | (data[3] << 8);
    uint16_t acc_raw = data[4] | (data[5] << 8);

    float pitch = pitch_raw / 10.0f;  // 精度0.1度
    float roll = roll_raw / 10.0f;    // 精度0.1度
    float acceleration = acc_raw / 100.0f; // 精度0.01g

    VEHICLE_DATA_DEBUG_PRINT("[VEHICLE_DATA_UART] 姿态数据: 俯仰=%.1f°, 横滚=%.1f°, 加速度=%.2fg",
                            pitch, roll, acceleration);

    // 更新姿态数据
    vehicle_data_set_attitude(pitch, roll, 0.0f); // yaw暂时设为0
}
