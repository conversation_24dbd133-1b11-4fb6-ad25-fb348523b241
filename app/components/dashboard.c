/**
 * @file dashboard.c
 * @brief 仪表盘组件 - 重构版本，使用数据驱动架构
 * @version 2.0.0
 * @date 2025-07-22
 */

#include "app/ebike_x1.h"
#include "app/constant.h"
#include "ui_component_manager.h"
#include "data/vehicle_data.h"
#include "data/vehicle_data_setter.h"
#include <math.h>
#include <stdio.h>
#include <string.h>

// =============================================================================
// 内部变量
// =============================================================================

static bool g_dashboard_initialized = false;
static bool g_event_bus_subscribed = false;
static lv_timer_t *g_dashboard_timer = NULL;
static uint32_t g_blink_counter = 0;

// =============================================================================
// 内部函数声明
// =============================================================================

static void dashboard_timer_callback(lv_timer_t *timer);

// event_bus事件处理器声明
static void handle_running_data_changed(uint16_t id, void *data, void *ctx);
static void handle_light_data_changed(uint16_t id, void *data, void *ctx);
static void handle_battery_data_changed(uint16_t id, void *data, void *ctx);
static void handle_attitude_data_changed(uint16_t id, void *data, void *ctx);
static void handle_system_data_changed(uint16_t id, void *data, void *ctx);
static void handle_time_data_changed(uint16_t id, void *data, void *ctx);

// UI更新函数
static void dashboard_update_time(void);
static void dashboard_update_battery(void);
static void dashboard_update_speed(void);
static void dashboard_update_power(void);
static void dashboard_update_odometer(void);
static void dashboard_update_temperature(void);
static void dashboard_update_trip(void);
static void dashboard_update_lights(void);
static void dashboard_update_cruise(void);
static void dashboard_update_attitude(void);
static void dashboard_update_system_info(void);

// 辅助函数
static void dashboard_handle_blink_effects(void);
static void dashboard_format_time_string(char *buffer, size_t size, const vehicle_time_data_t *time_data);
static void dashboard_format_date_string(char *buffer, size_t size, const vehicle_time_data_t *time_data);
static void calculate_light_display_state(const vehicle_light_data_t* light_data, bool* left_display, bool* right_display);

// =============================================================================
// 公共接口函数
// =============================================================================

int dashboard_init(void) {
    if (g_dashboard_initialized) {
        return 0; // 已经初始化
    }

    // 初始化车辆数据管理模块
    if (vehicle_data_init() != 0) {
       UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化失败: 车辆数据管理模块初始化失败");
        return -1;
    }

    // 初始化UI组件管理器
    if (ui_component_manager_init() != 0) {
       UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化失败: UI组件管理器初始化失败");
        return -1;
    }

    // 缓存所有UI组件
    if (ui_component_cache_all() != 0) {
       UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化警告: 部分UI组件缓存失败");
    }

    // 订阅event_bus数据变化事件
    if (!eb_subscribe(EVENT_DATA_RUNNING_CHANGED, handle_running_data_changed, NULL)) {
       UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化警告: 订阅运行数据变化事件失败");
    }

    if (!eb_subscribe(EVENT_DATA_LIGHT_CHANGED, handle_light_data_changed, NULL)) {
       UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化警告: 订阅灯光数据变化事件失败");
    }

    if (!eb_subscribe(EVENT_DATA_BATTERY_CHANGED, handle_battery_data_changed, NULL)) {
       UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化警告: 订阅电池数据变化事件失败");
    }

    if (!eb_subscribe(EVENT_DATA_ATTITUDE_CHANGED, handle_attitude_data_changed, NULL)) {
       UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化警告: 订阅姿态数据变化事件失败");
    }

    if (!eb_subscribe(EVENT_DATA_SYSTEM_CHANGED, handle_system_data_changed, NULL)) {
       UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化警告: 订阅系统数据变化事件失败");
    }

    if (!eb_subscribe(EVENT_DATA_TIME_CHANGED, handle_time_data_changed, NULL)) {
       UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化警告: 订阅时间数据变化事件失败");
    }

    g_event_bus_subscribed = true;

    // 创建定时器（100ms周期）
    g_dashboard_timer = lv_timer_create(dashboard_timer_callback, 100, NULL);
    if (!g_dashboard_timer) {
       UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化失败: 定时器创建失败");
        return -1;
    }

    // 初始化所有UI显示
    dashboard_update_time();
    dashboard_update_battery();
    dashboard_update_speed();
    dashboard_update_power();
    dashboard_update_odometer();
    dashboard_update_temperature();
    dashboard_update_trip();
    dashboard_update_lights();
    dashboard_update_cruise();
    dashboard_update_attitude();
    dashboard_update_system_info();

    g_dashboard_initialized = true;
   UI_COMP_MGR_DEBUG_PRINT("仪表盘初始化完成");
    return 0;
}

void dashboard_deinit(void) {
    if (!g_dashboard_initialized) {
        return;
    }

    // 删除定时器
    if (g_dashboard_timer) {
        lv_timer_del(g_dashboard_timer);
        g_dashboard_timer = NULL;
    }

    // 取消event_bus事件订阅
    if (g_event_bus_subscribed) {
        eb_unsubscribe(EVENT_DATA_RUNNING_CHANGED, handle_running_data_changed);
        eb_unsubscribe(EVENT_DATA_LIGHT_CHANGED, handle_light_data_changed);
        eb_unsubscribe(EVENT_DATA_BATTERY_CHANGED, handle_battery_data_changed);
        eb_unsubscribe(EVENT_DATA_ATTITUDE_CHANGED, handle_attitude_data_changed);
        eb_unsubscribe(EVENT_DATA_SYSTEM_CHANGED, handle_system_data_changed);
        eb_unsubscribe(EVENT_DATA_TIME_CHANGED, handle_time_data_changed);
        g_event_bus_subscribed = false;
    }

    // 清理UI组件管理器
    ui_component_manager_deinit();

    // 清理车辆数据管理模块
    vehicle_data_deinit();

    g_dashboard_initialized = false;
   UI_COMP_MGR_DEBUG_PRINT("仪表盘已清理");
}

// =============================================================================
// 定时器回调函数
// =============================================================================

static void dashboard_timer_callback(lv_timer_t *timer) {
    if (!g_dashboard_initialized) {
        return;
    }

    g_blink_counter++;

    // 处理闪烁效果（每500ms切换一次）
    if (g_blink_counter % 5 == 0) {
        dashboard_handle_blink_effects();
    }

    // 定期更新时间显示（每秒更新一次）
    if (g_blink_counter % 10 == 0) {
      if (!date_get_time(&g_date_time)) {
        // 更新时间
        vehicle_data_set_date(g_date_time.year, g_date_time.month, g_date_time.day);
        vehicle_data_set_time(g_date_time.hour, g_date_time.minute, g_date_time.second);
      }
      // dashboard_update_time();
    }
}


// =============================================================================
// UI更新函数实现
// =============================================================================

static void dashboard_update_time(void) {
    const vehicle_time_data_t* time_data = vehicle_data_get_time();
    if (!time_data) {
        return;
    }

    char time_str[16];
    char date_str[32];

    // 格式化时间和日期字符串
    dashboard_format_time_string(time_str, sizeof(time_str), time_data);
    dashboard_format_date_string(date_str, sizeof(date_str), time_data);

    // 更新状态栏时间
    UI_UPDATE_LABEL(statusbar_time, time_str);

    // 更新设置页面日期
    UI_UPDATE_LABEL(setting_date, date_str);
}

static void dashboard_update_battery(void) {
   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 开始更新电池UI");

    const vehicle_battery_data_t* battery_data = vehicle_data_get_battery();
    if (!battery_data) {
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 获取电池数据失败");
        return;
    }

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 电池数据: 电量=%d%%, 充电=%s",
           battery_data->level, battery_data->is_charging ? "是" : "否");

  char battery_str[8];
  snprintf(battery_str, sizeof(battery_str), "%d%%", (int)ceil(battery_data->level));

   // 更新状态栏电池
   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 更新状态栏电池标签: %s", battery_str);
   UI_UPDATE_LABEL(statusbar_battery_label, battery_str);

   // 更新主页电池
   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 更新主页电池标签和进度条: %s, %d", battery_str, battery_data->level);
   UI_UPDATE_LABEL(home_battery_label, battery_str);
   UI_UPDATE_BAR(home_battery_bar, (int)ceil(battery_data->level));

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 电池UI更新完成");
   // 充电状态闪烁效果在dashboard_handle_blink_effects中处理
}

static void dashboard_update_speed(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    if (!running_data) {
        return;
    }

    if (running_data->gear <= 5) {
        char gear_str[5];
        switch (running_data->gear) {
            case 0: strcpy(gear_str, "P"); break;
            case 1: strcpy(gear_str, "D1"); break; 
            case 2: strcpy(gear_str, "D2"); break;
            case 3: strcpy(gear_str, "D3"); break; 
            case 4: strcpy(gear_str, "D4"); break; 
            default: strcpy(gear_str, "R"); break;
        }
        UI_UPDATE_LABEL(statusbar_gear, gear_str);
        UI_SET_HIDDEN(statusbar_ready, running_data->gear == 0);
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 挡位显示更新: %s",  gear_str);
    }

    if (running_data->work_mode <= 2) {
        char mode_str[8];
        switch (running_data->gear) {
            case 0: strcpy(mode_str, "ECO"); break;
            case 1: strcpy(mode_str, "POWER"); break; 
            case 2: strcpy(mode_str, "CRUISE"); break;
            default: strcpy(mode_str, "NORMAL"); break;
        }
        UI_UPDATE_LABEL(statusbar_mode, mode_str);
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 模式显示更新: %s", mode_str);
    }
    // 更新状态栏速度
    
    // 更新主页速度显示
    char speed_str[8];
    snprintf(speed_str, sizeof(speed_str), "%d", running_data->speed);
    UI_UPDATE_LABEL(home_speed_value, speed_str);
}

static void dashboard_update_power(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    if (!running_data) {
        return;
    }

    // 使用国际化文本格式化功率显示
    lv_obj_t *power_label = UI_GET_COMPONENT(statusbar_power);
    if (power_label) {
        lv_label_set_text(power_label,
            lv_i18n_get_text_fmt("POWER", running_data->power_current, running_data->remain_range));
    }
}

static void dashboard_update_odometer(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    if (!running_data) {
        return;
    }

    // 更新状态栏里程
    lv_obj_t *odo_label = UI_GET_COMPONENT(statusbar_odometer);
    if (odo_label) {
        lv_label_set_text(odo_label,
            lv_i18n_get_text_fmt("STA_ODO", (int)running_data->total_mileage));
    }

    // 更新设置页面里程
    lv_obj_t *total_label = UI_GET_COMPONENT(setting_total_mileage);
    if (total_label) {
        lv_i18n_set_text_fmt(total_label, "ODO", (float)running_data->total_mileage);
    }

    lv_obj_t *sub_label = UI_GET_COMPONENT(setting_sub_mileage);
    if (sub_label) {
        lv_i18n_set_text_fmt(sub_label, "SUB_MILEAGE", (float)running_data->trip_mileage);
    }
}

static void dashboard_update_temperature(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    if (!running_data) {
        return;
    }

    // 更新温度显示 - 添加合理性检查
    if (running_data->temperature >= -40.0f && running_data->temperature <= 85.0f) {
        char temp_str[16];
        snprintf(temp_str, sizeof(temp_str), "%.1f°C", running_data->temperature);
        UI_UPDATE_LABEL(statusbar_temperature, lv_i18n_get_text_fmt("TEMPERATURE", running_data->temperature));
    }
}

static void dashboard_update_trip(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    const vehicle_time_data_t* time_data = vehicle_data_get_time();
    if (!running_data || !time_data) {
        return;
    }

    // 计算行程时间
    uint32_t trip_minutes = running_data->trip_time;
    uint32_t trip_hours = trip_minutes / 60;
    trip_minutes = trip_minutes % 60;

    // 更新行程显示
    lv_obj_t *trip_label = UI_GET_COMPONENT(statusbar_trip);
    if (trip_label) {
        lv_label_set_text(trip_label,
            lv_i18n_get_text_fmt("TRIP", trip_hours, trip_minutes, running_data->trip_mileage));
    }
}

static void dashboard_update_lights(void) {
    const vehicle_light_data_t* light_data = vehicle_data_get_light();
    if (!light_data) {
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 灯光数据获取失败");
        return;
    }

    // 计算显示状态（考虑优先级）
    bool left_display, right_display;
    calculate_light_display_state(light_data, &left_display, &right_display);

    UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 灯光状态更新: 日行灯=%s, 左转=%s->%s, 右转=%s->%s, 双闪=%s, 前灯=%s, 刹车=%s",
                            light_data->runlight ? "开" : "关",
                            light_data->left_turn ? "开" : "关",
                            left_display ? "显示" : "隐藏",
                            light_data->right_turn ? "开" : "关",
                            right_display ? "显示" : "隐藏",
                            light_data->double_flash ? "开" : "关",
                            light_data->headlight ? "开" : "关",
                            light_data->brake_light ? "开" : "关");
    // 更新日行灯状态
    UI_SET_HIDDEN(home_light_runlight, !light_data->runlight);

    // 更新前大灯状态
    UI_SET_HIDDEN(home_light_front, !light_data->headlight);

    // 更新刹车灯状态
    UI_SET_HIDDEN(home_light_brake, !light_data->brake_light);

    // 注意：转向灯的实际显示（包括闪烁效果）在dashboard_handle_blink_effects中处理
    // 这里只记录状态，不直接设置UI

    UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 灯光UI更新完成");
}

static void dashboard_update_cruise(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    if (!running_data) {
        return;
    }

    // 巡航状态显示
    printf("[DASHBOARD] 巡航状态=%s", running_data->cruise_enabled ? "开" : "关");
    if(running_data->cruise_enabled) {
      UI_SET_HIDDEN(home_cruise_icon, !running_data->cruise_enabled);
      UI_SET_HIDDEN(home_cruise_speed.parent, !running_data->cruise_enabled);
      char cruise_str[8];
      snprintf(cruise_str, sizeof(cruise_str), "%d", running_data->cruise_speed);
      UI_UPDATE_LABEL(home_cruise_speed, cruise_str);
    }

    // 巡航图标的闪烁效果在dashboard_handle_blink_effects中处理
}

static void dashboard_update_attitude(void) {
    const vehicle_attitude_data_t* attitude_data = vehicle_data_get_attitude();
    if (!attitude_data) {
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 姿态数据获取失败");
        return;
    }

    UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 更新姿态数据: 俯仰=%.1f°, 横滚=%.1f°, 加速度=%.2f",
                            attitude_data->pitch, attitude_data->roll, attitude_data->acceleration);

    // 更新车身姿态显示
    lv_obj_t *pitch_label = UI_GET_COMPONENT(home_pitch_angle);
    if (pitch_label) {
        lv_label_set_text(pitch_label,
            lv_i18n_get_text_fmt("PITCH_ANGLE", attitude_data->pitch));
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 俯仰角标签更新: %.1f°", attitude_data->pitch);
    } else {
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 俯仰角标签组件未找到");
    }

    lv_obj_t *roll_label = UI_GET_COMPONENT(home_roll_angle);
    if (roll_label) {
        lv_label_set_text(roll_label,
            lv_i18n_get_text_fmt("ROLL_ANGLE", attitude_data->roll));
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 横滚角标签更新: %.1f°", attitude_data->roll);
    } else {
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 横滚角标签组件未找到");
    }

    lv_obj_t *acc_label = UI_GET_COMPONENT(home_acceleration);
    if (acc_label) {
        lv_label_set_text(acc_label,
            lv_i18n_get_text_fmt("ACCELERATION", attitude_data->acceleration));
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 加速度标签更新: %.2f", attitude_data->acceleration);
    } else {
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 加速度标签组件未找到");
    }

    UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 姿态UI更新完成");
}

static void dashboard_update_system_info(void) {
    const vehicle_system_data_t* system_data = vehicle_data_get_system();
    if (!system_data) {
        return;
    }

    // 更新软件版本
    lv_obj_t *sw_label = UI_GET_COMPONENT(setting_sw_version);
    if (sw_label) {
        if (strlen(system_data->sw_version) > 0) {
            lv_i18n_set_text_fmt(sw_label, "SW_INFO", system_data->sw_version);
        } else {
            lv_label_set_text(sw_label, "--");
        }
    }

    // 更新硬件版本
    lv_obj_t *hw_label = UI_GET_COMPONENT(setting_hw_version);
    if (hw_label) {
        if (strlen(system_data->hw_version) > 0) {
            lv_i18n_set_text_fmt(hw_label, "HW_INFO", system_data->hw_version);
        } else {
            lv_label_set_text(hw_label, "--");
        }
    }
}

// =============================================================================
// 辅助函数实现
// =============================================================================

static void dashboard_handle_blink_effects(void) {
    const vehicle_battery_data_t* battery_data = vehicle_data_get_battery();
    const vehicle_light_data_t* light_data = vehicle_data_get_light();
    const vehicle_running_data_t* running_data = vehicle_data_get_running();

    bool blink_state = (g_blink_counter % 10) < 5; // 500ms周期闪烁

    // 充电状态闪烁
    if (battery_data && battery_data->is_charging) {
        UI_SET_HIDDEN(statusbar_battery_icon, !blink_state);
        UI_SET_HIDDEN(home_battery_icon, !blink_state);
    } else {
        UI_SET_HIDDEN(statusbar_battery_icon, false);
        UI_SET_HIDDEN(home_battery_icon, false);
    }

    // 转向灯闪烁（考虑优先级）
    if (light_data) {
        bool left_display, right_display;
        calculate_light_display_state(light_data, &left_display, &right_display);

        // 左转向灯闪烁
        if (left_display) {
            UI_SET_HIDDEN(home_light_left, !blink_state);
        } else {
            UI_SET_HIDDEN(home_light_left, true);
        }

        // 右转向灯闪烁
        if (right_display) {
            UI_SET_HIDDEN(home_light_right, !blink_state);
        } else {
            UI_SET_HIDDEN(home_light_right, true);
        }
    }

    // 巡航图标闪烁
    if (running_data && running_data->cruise_enabled) {
        UI_SET_HIDDEN(home_cruise_icon, !blink_state);
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 巡航图标: %s", running_data->cruise_enabled ? "显示" : "隐藏");
    } else {
        UI_SET_HIDDEN(home_cruise_icon, true);
    }
}

static void dashboard_format_time_string(char *buffer, size_t size, const vehicle_time_data_t *time_data) {
    if (!buffer || !time_data) {
        return;
    }

    snprintf(buffer, size, "%02d:%02d", time_data->hour, time_data->minute);
}

static void dashboard_format_date_string(char *buffer, size_t size, const vehicle_time_data_t *time_data) {
    if (!buffer || !time_data) {
        return;
    }

    snprintf(buffer, size, "%04d-%02d-%02d %02d:%02d:%02d",
             time_data->year, time_data->month, time_data->day,
             time_data->hour, time_data->minute, time_data->second);
}

/**
 * @brief 计算灯光显示状态（考虑优先级）
 * @param light_data 灯光数据
 * @param left_display 输出：左转灯显示状态
 * @param right_display 输出：右转灯显示状态
 */
static void calculate_light_display_state(const vehicle_light_data_t* light_data, bool* left_display, bool* right_display) {
    if (!light_data || !left_display || !right_display) {
        return;
    }

    if (light_data->double_flash) {
        // 双闪优先级最高，左右转向灯都显示
        *left_display = true;
        *right_display = true;
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 双闪模式: 左转=显示, 右转=显示");
    } else {
        // 双闪关闭时，显示各自的转向灯状态
        *left_display = light_data->left_turn;
        *right_display = light_data->right_turn;
        UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 转向灯模式: 左转=%s, 右转=%s",
                               *left_display ? "显示" : "隐藏",
                               *right_display ? "显示" : "隐藏");
    }
}

// =============================================================================
// event_bus事件处理器实现
// =============================================================================

/**
 * @brief 处理运行数据变化事件
 */
static void handle_running_data_changed(uint16_t id, void *data, void *ctx) {
    if (!data) {
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 运行数据变化事件处理失败: 数据为空");
        return;
    }

    if (!g_dashboard_initialized) {
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 仪表盘未初始化，跳过运行数据更新");
        return;
    }

    vehicle_running_data_t *running_data = (vehicle_running_data_t *)data;

    // 数据合理性验证
    if (running_data->speed > 200) {  // 速度上限检查
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 速度数据异常: %d km/h", running_data->speed);
        return;
    }

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 处理运行数据变化事件: 速度=%d km/h, 挡位=%d, 功率=%d W, 续航=%d km",
           running_data->speed, running_data->gear, running_data->power_current, running_data->remain_range);

    // 更新功率显示 - 添加错误处理
    // lv_obj_t *power_label = UI_GET_COMPONENT(statusbar_power);
    // if (power_label) {
    //     char power_text[32];
    //     snprintf(power_text, sizeof(power_text), "%dW/%dW",
    //             running_data->power_current, running_data->power_average);
    //     lv_label_set_text(power_label, power_text);
    // } else {
    //    UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 功率显示组件未找到");
    // }

    // // 更新剩余续航显示 - 新增
    // if (running_data->remain_range <= 1000) {
    //     char range_str[16];
    //     snprintf(range_str, sizeof(range_str), "%d km", running_data->remain_range);
    //     // 假设有续航显示组件
    //     // UI_UPDATE_LABEL(statusbar_range, range_str);
    //    UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 续航显示更新: %s", range_str);
    // }

    // 更新里程显示 - 完善
    char odo_str[32];
    snprintf(odo_str, sizeof(odo_str), "总:%u km 小计:%u km",
             running_data->total_mileage, running_data->trip_mileage);
   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 里程显示更新: %s", odo_str);

    // 更新状态栏里程
    lv_obj_t *odo_label = UI_GET_COMPONENT(statusbar_odometer);
    if (odo_label) {
        char total_odo_str[16];
        snprintf(total_odo_str, sizeof(total_odo_str), "%u km", running_data->total_mileage);
        lv_label_set_text(odo_label, total_odo_str);
    }

    // 更新巡航状态显示 - 新增
    // if (running_data->cruise_enabled) {
    //     char cruise_str[16];
    //     snprintf(cruise_str, sizeof(cruise_str), "巡航:%d", running_data->cruise_speed);
    //    UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 巡航状态更新: %s", cruise_str);
    // } else {
    //    UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 巡航状态: 关闭");
    // }

    // 调用统一更新函数，避免重复逻辑
    dashboard_update_cruise();
    dashboard_update_temperature();
    dashboard_update_trip();
    dashboard_update_battery();
    dashboard_update_speed();
    dashboard_update_power();
    dashboard_update_trip();
    dashboard_update_cruise();
    dashboard_update_attitude();

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 运行数据UI更新完成");
}

/**
 * @brief 处理灯光数据变化事件
 */
static void handle_light_data_changed(uint16_t id, void *data, void *ctx) {
    if (!data) {
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 灯光数据变化事件处理失败: 数据为空");
        return;
    }

    vehicle_light_data_t *light_data = (vehicle_light_data_t *)data;
   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 处理灯光数据变化事件: 日行灯=%s, 前灯=%s, 左转=%s, 右转=%s, 双闪=%s",
           light_data->runlight ? "开启" : "关闭",
           light_data->headlight ? "开启" : "关闭",
           light_data->left_turn ? "开启" : "关闭",
           light_data->right_turn ? "开启" : "关闭",
           light_data->double_flash ? "开启" : "关闭");

    // 调用统一的灯光更新函数，避免重复逻辑
    dashboard_update_lights();

    // 立即更新转向灯显示，避免等待定时器
    bool left_display, right_display;
    calculate_light_display_state(light_data, &left_display, &right_display);

    // 获取当前闪烁状态
    bool blink_state = (g_blink_counter % 10) < 5;

    // 立即更新转向灯UI
    if (left_display) {
        UI_SET_HIDDEN(home_light_left, !blink_state);
    } else {
        UI_SET_HIDDEN(home_light_left, true);
    }

    if (right_display) {
        UI_SET_HIDDEN(home_light_right, !blink_state);
    } else {
        UI_SET_HIDDEN(home_light_right, true);
    }

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 灯光数据UI更新完成（包括转向灯立即更新）");
}

/**
 * @brief 处理电池数据变化事件
 */
static void handle_battery_data_changed(uint16_t id, void *data, void *ctx) {
    if (!data) {
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 电池数据变化事件处理失败: 数据为空");
        return;
    }

    vehicle_battery_data_t *battery_data = (vehicle_battery_data_t *)data;
   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 处理电池数据变化事件: 电量=%d%%, 充电=%s",
           battery_data->level, battery_data->is_charging ? "是" : "否");

    // 调用统一的电池更新函数，避免重复逻辑
    dashboard_update_battery();

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 电池数据UI更新完成");
}

/**
 * @brief 处理姿态数据变化事件
 */
static void handle_attitude_data_changed(uint16_t id, void *data, void *ctx) {
    if (!data) {
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 姿态数据变化事件处理失败: 数据为空");
        return;
    }

    vehicle_attitude_data_t *attitude_data = (vehicle_attitude_data_t *)data;
   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 处理姿态数据变化事件: 俯仰=%.1f°, 横滚=%.1f°",
           attitude_data->pitch, attitude_data->roll);

    // 更新姿态UI
    dashboard_update_attitude();

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 姿态数据UI更新完成");
}

/**
 * @brief 处理系统数据变化事件
 */
static void handle_system_data_changed(uint16_t id, void *data, void *ctx) {
    if (!data) {
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 系统数据变化事件处理失败: 数据为空");
        return;
    }

    if (!g_dashboard_initialized) {
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 系统数据变化事件被忽略: dashboard未初始化");
        return;
    }

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 处理系统数据变化事件: ID=0x%04X", id);

    // 更新系统信息UI
    dashboard_update_system_info();

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 系统数据事件处理完成");
}

/**
 * @brief 处理时间数据变化事件
 */
static void handle_time_data_changed(uint16_t id, void *data, void *ctx) {
    if (!data) {
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 时间数据变化事件处理失败: 数据为空");
        return;
    }

    if (!g_dashboard_initialized) {
       UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 时间数据变化事件被忽略: dashboard未初始化");
        return;
    }

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 处理时间数据变化事件: ID=0x%04X", id);

    // 更新时间UI
    dashboard_update_time();

   UI_COMP_MGR_DEBUG_PRINT("[DASHBOARD] 时间数据事件处理完成");
}
