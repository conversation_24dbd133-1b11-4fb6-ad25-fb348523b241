/**
 * @file dialog.c
 */

#include "app/ebike_x1.h"

lv_timer_t **dialog_timer;

typedef enum
{
  WIFI = 0,
  BLUETOOTH,
  VIEW_NULL,
} dialog_view_t;

dialog_view_t g_cur_dialog_view = VIEW_NULL;

void ui_dialog_show_confirm(void);
void ui_dialog_reset(void);
void ui_dialog_msg_event_cb(lv_event_t *e);
void ui_dialog_view_back_event_cb(lv_event_t *e);

void ui_dialog_mount()
{
  // 根容器绑定事件
  lv_obj_add_event_cb(ui_dialog, ui_dialog_msg_event_cb, LV_EVENT_CLICKED, NULL);
  // 返回
  lv_obj_add_event_cb(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT_ICON), ui_dialog_view_back_event_cb, LV_EVENT_CLICKED, NULL);
  ui_dialog_reset();
}

void ui_dialog_msg_event_cb(lv_event_t *e)
{
  lv_event_code_t event_code = lv_event_get_code(e);
  lv_obj_t *cur = lv_event_get_current_target(e);

  if (event_code == LV_EVENT_CLICKED)
  {
    if (cur == ui_dialog_msg)
    {
      ui_dialog_reset();
    }

    // 重置状态
    ui_dialog_reset();
  }
}

void ui_dialog_show_msg(char *msg, char *sub_msg, void *icon, uint32_t timeout_ms)
{
  LV_LOG_USER("Message: %s", msg);
  LV_LOG_USER("Sub-message: %s", sub_msg);
  LV_LOG_USER("Icon address: %p", (void *)icon);
  LV_LOG_USER("Timeout (ms): %lu", (unsigned long)timeout_ms);

  // 设置默认值
  if (msg == NULL)
    return;
  if (icon == NULL)
    icon = &ui_img_dialog_ic_wifi_msg_png;
  if (timeout_ms == NULL)
    timeout_ms = 3000;

  ui_dialog_reset();
  // 设置icon
  lv_img_set_src(ui_comp_get_child(ui_ToastMsg, UI_COMP_TOASTMSG_TOAST_MSG_ICON_TOAST_MSG_ICON_ASSET), icon);

  // 设置大标题
  lv_obj_t *titel = ui_comp_get_child(ui_ToastMsg, UI_COMP_TOASTMSG_TOAST_MSG_LABEL_TOAST_MSG_LABEL_VALUE);
  lv_label_set_text(titel, msg);

  // 设置副标题
  lv_obj_t *child = ui_comp_get_child(ui_ToastMsg, UI_COMP_TOASTMSG_TOAST_MSG_SUB_LABEL_TOAST_MSG_SUB_LABEL_VALUE);
  if (sub_msg == NULL)
  {
    lv_obj_add_flag(child, LV_OBJ_FLAG_HIDDEN);
  }
  else
  {
    lv_label_set_text(child, sub_msg);
    lv_obj_clear_flag(child, LV_OBJ_FLAG_HIDDEN);
  }

  // 显示弹窗
  lv_obj_clear_flag(ui_dialog, LV_OBJ_FLAG_HIDDEN);
  lv_obj_clear_flag(ui_dialog_msg, LV_OBJ_FLAG_HIDDEN);

  // 倒计时自动关闭弹窗
  dialog_timer = lv_timer_create(ui_dialog_reset, timeout_ms, NULL);

  LV_LOG_USER("ui_dialog_show_msg end");
}

void ui_dialog_show_confirm()
{
  ui_dialog_reset();
  lv_obj_clear_flag(ui_dialog, LV_OBJ_FLAG_HIDDEN);
  lv_obj_clear_flag(ui_dialog_confirm, LV_OBJ_FLAG_HIDDEN);
}

void ui_dialog_view_show()
{
  ui_dialog_reset();
  lv_obj_clear_flag(ui_dialog, LV_OBJ_FLAG_HIDDEN);
  lv_obj_clear_flag(ui_dialog_view, LV_OBJ_FLAG_HIDDEN);
}

void ui_dialog_reset()
{
  // 重置wifi/bt状态
  switch (g_cur_dialog_view)
  {
  case WIFI:
    // ui_wifi_view_unmount();
    break;
  case BLUETOOTH:
    //ui_bt_view_unmount();
    break;
  }

  // 重置定时器
  if (dialog_timer != NULL)
  {
    lv_timer_del(dialog_timer);
    dialog_timer = NULL;
  }

  // 重置状态
  g_cur_dialog_view = VIEW_NULL;

  // 隐藏所有内容
  lv_obj_add_flag(ui_dialog, LV_OBJ_FLAG_HIDDEN);
  lv_obj_add_flag(ui_dialog_msg, LV_OBJ_FLAG_HIDDEN);
  lv_obj_add_flag(ui_dialog_confirm, LV_OBJ_FLAG_HIDDEN);
  lv_obj_add_flag(ui_dialog_view, LV_OBJ_FLAG_HIDDEN);
}

void ui_dialog_view_back_event_cb(lv_event_t *e)
{
  ui_dialog_reset();
}
