/**
 * @file ui_component_manager.h
 * @brief UI组件管理器 - 统一管理dashboard UI组件访问
 * @version 1.0.0
 * @date 2025-07-22
 */

#ifndef UI_COMPONENT_MANAGER_H
#define UI_COMPONENT_MANAGER_H

#include "../../lvgl/lvgl.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// =============================================================================
// 规范化的组件ID定义
// =============================================================================

// 状态栏组件ID
#define DASHBOARD_STATUSBAR_TEMPERATURE   UI_COMP_STATUSBAR2_LEFT_REAL_STA_TEMP
#define DASHBOARD_STATUSBAR_MODE          UI_COMP_STATUSBAR2_LEFT_REAL_MODE_MODE_LABEL
#define DASHBOARD_STATUSBAR_GEAR          UI_COMP_STATUSBAR2_LEFT_REAL_GEAR_GEAR_LABEL
#define DASHBOARD_STATUSBAR_TIME          UI_COMP_STATUSBAR2_LEFT_REAL_STA_TIME
#define DASHBOARD_STATUSBAR_POWER         UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_POWER_POWER_LABEL
#define DASHBOARD_STATUSBAR_TRIP          UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_TRIP_TRIP_INFO
#define DASHBOARD_STATUSBAR_BATTERY_ICON  UI_COMP_STATUSBAR2_RIGHT_NORMAL_BATTERY_BAT_ICON
#define DASHBOARD_STATUSBAR_BATTERY_LABEL UI_COMP_STATUSBAR2_RIGHT_NORMAL_BATTERY_BAT_LABEL
#define DASHBOARD_STATUSBAR_ODOMETER      UI_COMP_STATUSBAR2_RIGHT_ODO_LABEL
#define DASHBOARD_STATUSBAR_READY         UI_COMP_STATUSBAR2_RIGHT_ODO_READY

// 主页组件ID
#define DASHBOARD_HOME_BATTERY_ICON       UI_COMP_HOME_HOME_RIGHT_BATTERY_INFO_BATTERY_ICON
#define DASHBOARD_HOME_BATTERY_LABEL      UI_COMP_HOME_HOME_RIGHT_BATTERY_INFO_BATTERY_LABEL
#define DASHBOARD_HOME_BATTERY_BAR        UI_COMP_HOME_HOME_RIGHT_BATTERY_VAL
#define DASHBOARD_HOME_SPEED_VALUE        UI_COMP_HOME_HOME_RIGHT_RUN_SPEED_STA_RUN_SPEED_VALUE

// 灯光组件ID
#define DASHBOARD_HOME_LIGHT_LEFT         UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_L_STA_MAIN_RUN1
#define DASHBOARD_HOME_LIGHT_RIGHT        UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_R_STA_ICON_R_6
#define DASHBOARD_HOME_LIGHT_RUNLIGHT     UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FE_STA_ICON_R_10
#define DASHBOARD_HOME_LIGHT_FRONT        UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_F_STA_ICON_R_9
#define DASHBOARD_HOME_LIGHT_BRAKE        UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FD_STA_ICON_R_8

// 姿态组件ID
#define DASHBOARD_HOME_PITCH_ANGLE        UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_PITCH_ANGLE
#define DASHBOARD_HOME_ROLL_ANGLE         UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_ROLL_ANGLE
#define DASHBOARD_HOME_ACCELERATION       UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_ACCELERATION

// 巡航组件ID
#define DASHBOARD_HOME_CRUISE_PARENT      UI_COMP_HOME_HOME_RIGHT_RUN_NOS
#define DASHBOARD_HOME_CRUISE_ICON        UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_ICON_STA_MAIN_NOS_ICON
#define DASHBOARD_HOME_CRUISE_SPEED       UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_SPEED_BG_STA_NOS_SPEED_VALUE

// 设置页面组件ID
#define DASHBOARD_SETTING_DATE            UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_RIGHT_LIST_ITEM_SUB
#define DASHBOARD_SETTING_TOTAL_MILEAGE   UI_COMP_SETTINGINFO_INFO_LEFT_INFO_LEFT_LABEL_VAL
#define DASHBOARD_SETTING_SUB_MILEAGE     UI_COMP_SETTINGINFO_INFO_RIGHT_INFO_RIGHT_LABEL_VAL
#define DASHBOARD_SETTING_SW_VERSION      UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL
#define DASHBOARD_SETTING_HW_VERSION      UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL

// =============================================================================
// 错误码定义
// =============================================================================

/**
 * @brief UI组件管理器错误码
 */
typedef enum {
    UI_COMP_SUCCESS = 0,                    /**< 成功 */
    UI_COMP_ERROR_NOT_INITIALIZED = -1,     /**< 未初始化 */
    UI_COMP_ERROR_INVALID_PARAM = -2,       /**< 无效参数 */
    UI_COMP_ERROR_NOT_FOUND = -3,           /**< 组件未找到 */
    UI_COMP_ERROR_CACHE_FAILED = -4,        /**< 缓存失败 */
    UI_COMP_ERROR_MUTEX_FAILED = -5         /**< 互斥锁操作失败 */
} ui_comp_result_t;

// =============================================================================
// 数据结构定义
// =============================================================================

/**
 * @brief UI组件描述结构体
 */
typedef struct {
    lv_obj_t *parent;        /**< 父组件对象 */
    uint32_t component_id;   /**< 组件ID */
    lv_obj_t *cached_obj;    /**< 缓存的组件对象 */
    bool is_cached;          /**< 是否已缓存 */
    const char *name;        /**< 组件名称（用于调试） */
} ui_component_t;

/**
 * @brief UI组件配置结构体
 */
typedef struct {
    const char *name;           /**< 组件名称 */
    lv_obj_t **parent_ref;      /**< 父组件对象引用 */
    uint32_t component_id;      /**< 组件ID */
    size_t component_offset;    /**< 组件在结构体中的偏移量 */
} ui_component_config_t;

/**
 * @brief Dashboard UI组件集合
 */
typedef struct {
    // 状态栏组件
    ui_component_t statusbar_temperature;
    ui_component_t statusbar_mode;
    ui_component_t statusbar_gear;
    ui_component_t statusbar_time;
    ui_component_t statusbar_power;
    ui_component_t statusbar_trip;
    ui_component_t statusbar_battery_icon;
    ui_component_t statusbar_battery_label;
    ui_component_t statusbar_odometer;
    ui_component_t statusbar_ready;
    
    // 主页电池组件
    ui_component_t home_battery_icon;
    ui_component_t home_battery_label;
    ui_component_t home_battery_bar;
    
    // 主页运行组件
    ui_component_t home_speed_value;
    ui_component_t home_cruise_parent;
    ui_component_t home_cruise_icon;
    ui_component_t home_cruise_speed;
    
    // 灯光组件
    ui_component_t home_light_left;
    ui_component_t home_light_right;
    ui_component_t home_light_runlight;
    ui_component_t home_light_front;
    ui_component_t home_light_brake;
    
    // 姿态组件
    ui_component_t home_pitch_angle;
    ui_component_t home_roll_angle;
    ui_component_t home_acceleration;
    
    // 设置页面组件
    ui_component_t setting_date;
    ui_component_t setting_total_mileage;
    ui_component_t setting_sub_mileage;
    ui_component_t setting_sw_version;
    ui_component_t setting_hw_version;
} dashboard_ui_components_t;

/**
 * @brief 组件更新回调函数类型
 * @param component 组件对象
 * @param user_data 用户数据
 */
typedef void (*ui_component_update_callback_t)(lv_obj_t *component, void *user_data);

// =============================================================================
// 公共接口函数
// =============================================================================

/**
 * @brief 初始化UI组件管理器
 * @return UI_COMP_SUCCESS 成功，其他值表示错误
 */
ui_comp_result_t ui_component_manager_init(void);

/**
 * @brief 清理UI组件管理器
 * @return UI_COMP_SUCCESS 成功，其他值表示错误
 */
ui_comp_result_t ui_component_manager_deinit(void);

/**
 * @brief 获取Dashboard UI组件集合
 * @return 组件集合指针（只读），失败返回NULL
 */
const dashboard_ui_components_t* ui_component_manager_get_components(void);

/**
 * @brief 获取指定的UI组件对象
 * @param component 组件描述结构体
 * @return 组件对象指针，失败返回NULL
 */
lv_obj_t* ui_component_get_object(ui_component_t *component);

/**
 * @brief 缓存所有UI组件
 * @return UI_COMP_SUCCESS 成功，其他值表示错误
 */
ui_comp_result_t ui_component_cache_all(void);

/**
 * @brief 清除所有组件缓存
 * @return UI_COMP_SUCCESS 成功，其他值表示错误
 */
ui_comp_result_t ui_component_clear_cache(void);

/**
 * @brief 检查组件是否有效
 * @param component 组件描述结构体
 * @return true 有效，false 无效
 */
bool ui_component_is_valid(const ui_component_t *component);

/**
 * @brief 组件迭代器结构体
 */
typedef struct {
    size_t index;                           /**< 当前索引 */
    const ui_component_config_t *config;    /**< 当前配置 */
    ui_component_t *component;              /**< 当前组件 */
} ui_component_iterator_t;

/**
 * @brief 组件操作回调函数类型
 * @param component 组件对象
 * @param user_data 用户数据
 */
typedef void (*ui_component_action_t)(ui_component_t *component, void *user_data);

/**
 * @brief 组件过滤回调函数类型
 * @param component 组件对象
 * @param user_data 用户数据
 * @return true 符合条件，false 不符合条件
 */
typedef bool (*ui_component_predicate_t)(const ui_component_t *component, void *user_data);

/**
 * @brief 获取组件迭代器
 * @param iter 迭代器指针
 * @return UI_COMP_SUCCESS 成功，其他值表示错误
 */
ui_comp_result_t ui_component_iterator_begin(ui_component_iterator_t *iter);

/**
 * @brief 迭代到下一个组件
 * @param iter 迭代器指针
 * @return true 有下一个组件，false 已到末尾
 */
bool ui_component_iterator_next(ui_component_iterator_t *iter);

/**
 * @brief 对所有组件执行操作
 * @param predicate 过滤条件（可为NULL）
 * @param action 操作函数
 * @param user_data 用户数据
 * @return 处理的组件数量，负数表示错误
 */
int ui_component_foreach(ui_component_predicate_t predicate,
                        ui_component_action_t action,
                        void *user_data);

// =============================================================================
// 便捷访问宏定义
// =============================================================================

// =============================================================================
// 增强的便捷访问宏定义
// =============================================================================

/**
 * @brief 安全获取组件对象
 * @param comp_name 组件名称
 * @return 组件对象指针，失败返回NULL
 */
#define UI_GET_COMPONENT(comp_name) \
    ui_component_get_object(&(ui_component_manager_get_components()->comp_name))

/**
 * @brief 安全更新标签文本
 * @param comp_name 组件名称
 * @param text 文本内容
 */
#define UI_UPDATE_LABEL_SAFE(comp_name, text) \
    do { \
        const dashboard_ui_components_t *_comps = ui_component_manager_get_components(); \
        if (_comps && (text)) { \
            lv_obj_t *_obj = _comps->comp_name.cached_obj; \
            if (!_obj && _comps->comp_name.parent) { \
                _obj = ui_comp_get_child(_comps->comp_name.parent, _comps->comp_name.component_id); \
            } \
            if (_obj) { \
                lv_label_set_text(_obj, text); \
                UI_COMP_MGR_DEBUG_PRINT("标签 %s 更新成功: %s", #comp_name, text); \
            } else { \
                UI_COMP_MGR_DEBUG_PRINT("标签 %s 更新失败: 对象为空", #comp_name); \
            } \
        } \
    } while(0)

/**
 * @brief 兼容性宏 - 保持向后兼容
 */
#define UI_UPDATE_LABEL(comp_name, text) \
    do { \
        UI_UPDATE_LABEL_SAFE(comp_name, text); \
    } while(0)

/**
 * @brief 安全更新进度条值
 * @param comp_name 组件名称
 * @param value 进度值
 */
#define UI_UPDATE_BAR_SAFE(comp_name, value) \
    do { \
        const dashboard_ui_components_t *_comps = ui_component_manager_get_components(); \
        if (_comps) { \
            lv_obj_t *_obj = _comps->comp_name.cached_obj; \
            if (!_obj && _comps->comp_name.parent) { \
                _obj = ui_comp_get_child(_comps->comp_name.parent, _comps->comp_name.component_id); \
            } \
            if (_obj) { \
                lv_bar_set_value(_obj, value, LV_ANIM_OFF); \
                UI_COMP_MGR_DEBUG_PRINT("进度条 %s 更新成功: %d", #comp_name, value); \
            } else { \
                UI_COMP_MGR_DEBUG_PRINT("进度条 %s 更新失败: 对象为空", #comp_name); \
            } \
        } \
    } while(0)

/**
 * @brief 兼容性宏 - 保持向后兼容
 */
#define UI_UPDATE_BAR(comp_name, value) \
    do { \
        UI_UPDATE_BAR_SAFE(comp_name, value); \
    } while(0)

/**
 * @brief 安全设置组件可见性（更安全的版本）
 * @param comp_name 组件名称
 * @param hidden 是否隐藏
 */
#define UI_SET_HIDDEN_SAFE(comp_name, hidden) \
    do { \
        const dashboard_ui_components_t *_comps = ui_component_manager_get_components(); \
        if (_comps) { \
            lv_obj_t *_obj = _comps->comp_name.cached_obj; \
            if (!_obj && _comps->comp_name.parent) { \
                _obj = ui_comp_get_child(_comps->comp_name.parent, _comps->comp_name.component_id); \
            } \
            if (_obj) { \
                if (hidden) lv_obj_add_flag(_obj, LV_OBJ_FLAG_HIDDEN); \
                else lv_obj_clear_flag(_obj, LV_OBJ_FLAG_HIDDEN); \
            } \
        } \
    } while(0)

/**
 * @brief 兼容性宏 - 保持向后兼容
 */
#define UI_SET_HIDDEN(comp_name, hidden) \
    do { \
        UI_SET_HIDDEN_SAFE(comp_name, hidden); \
    } while(0)

/**
 * @brief 设置组件闪烁效果
 * @param comp_name 组件名称
 * @param blink_condition 闪烁条件
 */
#define UI_SET_BLINK(comp_name, blink_condition) \
    UI_SET_HIDDEN(comp_name, !(blink_condition))

/**
 * @brief 检查组件是否有效（简化版本，不返回值）
 * @param comp_name 组件名称
 */
#define UI_IS_COMPONENT_VALID(comp_name) \
    do { \
        const dashboard_ui_components_t *_comps = ui_component_manager_get_components(); \
        if (_comps) { \
            ui_component_is_valid(&(_comps->comp_name)); \
        } \
    } while(0)

/**
 * @brief 安全执行组件操作
 * @param comp_name 组件名称
 * @param operation 操作代码块
 */
#define UI_WITH_COMPONENT(comp_name, operation) \
    do { \
        const dashboard_ui_components_t *_comps = ui_component_manager_get_components(); \
        if (_comps) { \
            lv_obj_t *_obj = ui_component_get_object(&(_comps->comp_name)); \
            if (_obj) { \
                lv_obj_t *comp_name = _obj; \
                operation; \
            } else { \
                UI_COMP_MGR_DEBUG_PRINT("组件 %s 不可用", #comp_name); \
            } \
        } \
    } while(0)

/**
 * @brief 类型安全的标签更新宏
 * @param comp_name 组件名称（必须是标签类型）
 * @param text 文本内容
 */
#define UI_UPDATE_LABEL_TYPED(comp_name, text) \
    UI_WITH_COMPONENT(comp_name, { \
        lv_label_set_text(comp_name, text); \
        UI_COMP_MGR_DEBUG_PRINT("标签 %s 更新: %s", #comp_name, text); \
    })

/**
 * @brief 类型安全的进度条更新宏
 * @param comp_name 组件名称（必须是进度条类型）
 * @param value 进度值
 */
#define UI_UPDATE_BAR_TYPED(comp_name, value) \
    UI_WITH_COMPONENT(comp_name, { \
        lv_bar_set_value(comp_name, value, LV_ANIM_OFF); \
        UI_COMP_MGR_DEBUG_PRINT("进度条 %s 更新: %d", #comp_name, value); \
    })

/**
 * @brief 设置组件样式
 * @param comp_name 组件名称
 * @param property 样式属性
 * @param value 样式值
 * @param selector 选择器
 */
#define UI_SET_STYLE(comp_name, property, value, selector) \
    UI_WITH_COMPONENT(comp_name, { \
        lv_obj_set_style_##property(comp_name, value, selector); \
        UI_COMP_MGR_DEBUG_PRINT("组件 %s 样式更新: " #property, #comp_name); \
    })

/**
 * @brief 条件性组件操作
 * @param condition 条件表达式
 * @param comp_name 组件名称
 * @param operation 操作代码块
 */
#define UI_IF_COMPONENT(condition, comp_name, operation) \
    do { \
        if (condition) { \
            UI_WITH_COMPONENT(comp_name, operation); \
        } \
    } while(0)

// =============================================================================
// 调试和诊断函数
// =============================================================================

/**
 * @brief 打印所有组件状态
 * @return UI_COMP_SUCCESS 成功，其他值表示错误
 */
ui_comp_result_t ui_component_print_status(void);

/**
 * @brief 验证所有组件的有效性
 * @return 有效组件数量，负数表示错误
 */
int ui_component_validate_all(void);

/**
 * @brief 获取组件统计信息
 * @param total_components 总组件数
 * @param cached_components 已缓存组件数
 * @param valid_components 有效组件数
 * @return UI_COMP_SUCCESS 成功，其他值表示错误
 */
ui_comp_result_t ui_component_get_stats(int *total_components, int *cached_components, int *valid_components);

/**
 * @brief 获取性能统计信息
 * @param total_calls 总调用次数
 * @param cache_hits 缓存命中次数
 * @param cache_misses 缓存未命中次数
 * @param cache_hit_rate 缓存命中率
 * @return UI_COMP_SUCCESS 成功，其他值表示错误
 */
ui_comp_result_t ui_component_get_performance_stats(uint32_t *total_calls,
                                                   uint32_t *cache_hits,
                                                   uint32_t *cache_misses,
                                                   float *cache_hit_rate);

/**
 * @brief 重置性能统计
 * @return UI_COMP_SUCCESS 成功，其他值表示错误
 */
ui_comp_result_t ui_component_reset_performance_stats(void);

/**
 * @brief 组件健康检查
 * @param healthy_count 健康组件数量
 * @param unhealthy_count 异常组件数量
 * @param report 检查报告缓冲区
 * @param report_size 报告缓冲区大小
 * @return UI_COMP_SUCCESS 成功，其他值表示错误
 */
ui_comp_result_t ui_component_health_check(int *healthy_count,
                                          int *unhealthy_count,
                                          char *report,
                                          size_t report_size);

// =============================================================================
// 调试宏
// =============================================================================

#ifdef UI_COMP_MGR_DEBUG
#include <stdio.h>
#define UI_COMP_MGR_DEBUG_PRINT(fmt, ...) \
    printf("[UI_COMP_MGR_DEBUG] " fmt "\n", ##__VA_ARGS__)
#else
#define UI_COMP_MGR_DEBUG_PRINT(fmt, ...)
#endif

#ifdef __cplusplus
}
#endif

#endif // UI_COMPONENT_MANAGER_H
