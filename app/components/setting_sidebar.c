/**
 * @file setting_sidebar.c
 */

#include "app/ebike_x1.h"

typedef struct
{
  uint32_t id;
  lv_obj_t *target;
} item_obj_t;

typedef struct
{
  void *icon;
  char *msg;
  char *sub_msg;
  uint32_t timeout_ms;
} dialog_msg_t;

void ui_setting_sidebar_event_cb(lv_event_t *e);
void ui_setting_content_switch_to(size_t index); // 新增内容切换函数声明

// 修改为动态数组指针并添加计数器
item_obj_t **g_setting_sidebar_btns = NULL;
size_t g_setting_sidebar_btns_count = 0;
size_t g_setting_sidebar_current_index = 0; // 新增当前选中索引

void ui_setting_sidebar_mount()
{
  // 或者设置的菜单
  lv_obj_t *menus = lv_obj_get_child(ui_SettingSidebar, 1);
  // 侧边栏按钮ID列表
  const int count = lv_obj_get_child_cnt(menus);
  LV_LOG_USER("Get ui_setting_sidebar count %d", count);

  if (count <= 0)
    return;

  // 释放旧内存
  if (g_setting_sidebar_btns)
  {
    free(g_setting_sidebar_btns);
    g_setting_sidebar_btns = NULL;
  }

  // 动态分配新内存
  g_setting_sidebar_btns = malloc(count * sizeof(item_obj_t *));
  if (!g_setting_sidebar_btns)
  {
    LV_LOG_ERROR("Failed to allocate memory for setting_sidebar buttons");
    return;
  }

  g_setting_sidebar_btns_count = count;

  for (uint32_t i = 0; i < count; i++)
  {
    lv_obj_t *child = lv_obj_get_child(menus, i);
    if (child == NULL)
      continue;

    // 创建按钮描述符并缓存
    item_obj_t *btn_desc = malloc(sizeof(item_obj_t));
    if (!btn_desc)
    {
      LV_LOG_ERROR("Failed to allocate memory for button descriptor");
      continue;
    }

    btn_desc->id = i;
    btn_desc->target = child;
    g_setting_sidebar_btns[i] = btn_desc; // 存储到动态数组

    // 移除原事件
    lv_obj_remove_event_cb(child, LV_EVENT_ALL);
    // 绑定新事件
    lv_obj_add_event_cb(child, ui_setting_sidebar_event_cb, LV_EVENT_ALL, NULL);
  }

  // 默认选中第一个
  if (count > 0)
  {
    g_setting_sidebar_current_index = 0;
    ui_setting_content_switch_to(0);
  }
}

// 修改事件处理函数使用动态数组计数
void ui_setting_sidebar_event_cb(lv_event_t *e)
{
  lv_event_code_t event_code = lv_event_get_code(e);
  lv_obj_t *cur = lv_event_get_current_target(e);

  if (event_code == LV_EVENT_CLICKED)
  {
    for (size_t i = 0; i < g_setting_sidebar_btns_count; i++)
    {
      if (g_setting_sidebar_btns[i]->target == cur)
      {
        lv_obj_add_state(g_setting_sidebar_btns[i]->target, LV_STATE_CHECKED);

        // 新增内容切换逻辑
        if (i != g_setting_sidebar_current_index)
        {
          ui_setting_content_switch_to(i);
          g_setting_sidebar_current_index = i;
        }
      }
      else
      {
        lv_obj_clear_state(g_setting_sidebar_btns[i]->target, LV_STATE_CHECKED);
      }
    }
  }
}

// 新增内容切换实现
void ui_setting_content_switch_to(size_t index)
{
  if (index >= g_setting_sidebar_btns_count)
    return;

  // 隐藏所有内容
  lv_obj_add_flag(ui_system_setting_warp, LV_OBJ_FLAG_HIDDEN);
  lv_obj_add_flag(ui_vehicle_setting_warp, LV_OBJ_FLAG_HIDDEN);
  lv_obj_add_flag(ui_other_setting_warp, LV_OBJ_FLAG_HIDDEN);

  // 显示目标内容
  switch (index)
  {
  case 0:
    lv_obj_clear_flag(ui_system_setting_warp, LV_OBJ_FLAG_HIDDEN);
    break;
  case 1:
    lv_obj_clear_flag(ui_vehicle_setting_warp, LV_OBJ_FLAG_HIDDEN);
    break;
  default:
    lv_obj_clear_flag(ui_other_setting_warp, LV_OBJ_FLAG_HIDDEN);
  }
}
