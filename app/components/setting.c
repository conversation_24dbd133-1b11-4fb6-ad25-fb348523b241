/**
 * @file setting.c
 */

#include "app/ebike_x1.h"

#define _SETTING_ITEM_RIGHT UI_COMP_INFOLARGE_INFO_RIGHT // 设置sub父组件
#define _SETTING_TITLE UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT_LABEL

void ui_setting_system_mount(void);
void ui_setting_vehicle_mount(void);
void ui_setting_other_mount(void);
void ui_setting_reset_event_cb(lv_event_t *e);
void ui_setting_wifi_event_cb(lv_event_t *e);
void ui_setting_bt_event_cb(lv_event_t *e);

// 重新挂载事件
void ui_setting_mount()
{
  ui_setting_system_mount();
  ui_setting_vehicle_mount();
  ui_setting_other_mount();
}

void ui_setting_system_mount()
{
  lv_obj_add_event_cb(ui_SettingItem, ui_setting_wifi_event_cb, LV_EVENT_CLICKED, NULL);
  lv_obj_add_event_cb(ui_SettingItem2, ui_setting_bt_event_cb, LV_EVENT_CLICKED, NULL);
}

void ui_setting_vehicle_mount()
{
}

void ui_setting_other_mount()
{
  // 重置
  lv_obj_t *reset_btn = ui_comp_get_child(ui_InfoLarge3, _SETTING_ITEM_RIGHT);
  LV_LOG_USER("ui_setting_other_mount %d", lv_obj_get_child_cnt(reset_btn));
  // lv_obj_remove_event_cb(reset_btn, LV_EVENT_ALL);
  lv_obj_add_flag(reset_btn, LV_OBJ_FLAG_CLICKABLE);
  lv_obj_add_event_cb(reset_btn, ui_setting_reset_event_cb, LV_EVENT_CLICKED, NULL);
}

// 事件处理函数
void ui_setting_system_event_cb(lv_event_t *e)
{
}

void ui_setting_vehicle_event_cb(lv_event_t *e)
{
}

const lv_obj_t **cur;
const lv_timer_t **setting_timer;
const uint32_t timeout_ms = 1000;

void ui_setting_reset_event_cb(lv_event_t *e)
{
  LV_LOG_USER("ui_setting_reset_event_cb click %d", e);

  if (cur != NULL || setting_timer != NULL)
  {
    lv_obj_add_flag(cur, LV_OBJ_FLAG_CLICKABLE);
    lv_timer_del(setting_timer);
    setting_timer = NULL;
    cur = NULL;
    return;
  }
  lv_event_code_t event_code = lv_event_get_code(e);
  cur = lv_event_get_current_target(e);
  if (event_code == LV_EVENT_CLICKED)
  {
    // 消抖
    lv_obj_clear_flag(cur, LV_OBJ_FLAG_CLICKABLE);
    // 默认3秒自动重置状态
    setting_timer = lv_timer_create(ui_setting_reset_event_cb, timeout_ms, NULL);
    // 执行重置流程

    // 显示重置消息
    char *msg = lv_i18n_get_text_fmt("LINK_2", lv_i18n_get_text("RESET"), lv_i18n_get_text("SUCCESS"));

    // 显示消息
    ui_dialog_show_msg(msg, NULL, &ui_img_dialog_ic_wifi_msg_png, timeout_ms);
  }
}

void ui_setting_wifi_event_cb(lv_event_t *e)
{
  lv_event_code_t event_code = lv_event_get_code(e);
  if (event_code != LV_EVENT_CLICKED)
    return;

  // ui_wifi_view_mount();
}

void ui_setting_bt_event_cb(lv_event_t *e)
{
  lv_event_code_t event_code = lv_event_get_code(e);
  if (event_code != LV_EVENT_CLICKED)
    return;
}