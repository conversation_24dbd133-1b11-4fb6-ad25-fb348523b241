/**
 * @file lv_i18n.h
 * @brief LVGL国际化支持模块
 *
 * 提供LVGL应用程序的多语言支持，包括文本翻译和字体切换功能。
 */

#ifndef LV_I18N_H
#define LV_I18N_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "lvgl/lvgl.h"

/*********************
 *      DEFINES
 *********************/

/* 设置最大语言数量，可在lv_conf.h中覆盖 */
#ifndef LV_I18N_LANGUAGE_CNT
#define LV_I18N_LANGUAGE_CNT 8
#endif

/* 设置最大文本长度，可在lv_conf.h中覆盖 */
#ifndef LV_I18N_MAX_TEXT_LEN
#define LV_I18N_MAX_TEXT_LEN 256
#endif

/* 是否启用调试日志，可在lv_conf.h中覆盖 */
#ifndef LV_I18N_DEBUG_ENABLE
#define LV_I18N_DEBUG_ENABLE 0
#endif

/**********************
 *      TYPEDEFS
 **********************/

/**
 * 语言ID类型
 *
 * 注意：用户需要定义自己的语言ID枚举，从0开始编号
 * 例如: LV_LANG_EN = 0, LV_LANG_ZH, LV_LANG_JP, ...
 */
typedef uint8_t lv_i18n_lang_t;

/**
 * 字体大小类型
 */
typedef enum {
    LV_I18N_FONT_SMALL = 0,  /** 小号字体 */
    LV_I18N_FONT_NORMAL,     /** 标准字体 */
    LV_I18N_FONT_LARGE       /** 大号字体 */
} lv_i18n_font_size_t;

/**
 * 语言资源项结构定义
 */
typedef struct {
    const char * key;    /** 文本键名 */
    const char * value;  /** 文本内容 */
} lv_i18n_lang_pack_t;

/**
 * 语言字体配置结构
 */
typedef struct {
    const lv_font_t * small_font;   /** 小字体 */
    const lv_font_t * normal_font;  /** 中等字体 */
    const lv_font_t * large_font;   /** 大字体 */
} lv_i18n_font_t;

/**
 * 语言本地化配置
 */
typedef struct {
    const char * language_name;            /** 语言名称 */
    const lv_i18n_lang_pack_t * lang_pack; /** 语言资源包 */
    lv_i18n_font_t fonts;                  /** 语言字体 */
} lv_i18n_language_t;

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**
 * 初始化多语言模块
 *
 * @param languages 语言配置数组
 * @param default_lang 默认语言ID
 * @return LV_RES_OK: 初始化成功; LV_RES_INV: 初始化失败
 */
lv_res_t lv_i18n_init(const lv_i18n_language_t * languages, lv_i18n_lang_t default_lang);

/**
 * 获取当前语言ID
 *
 * @return 当前语言ID
 */
lv_i18n_lang_t lv_i18n_get_current_lang(void);

/**
 * 切换到指定语言
 *
 * @param lang_id 目标语言ID
 * @return LV_RES_OK: 切换成功; LV_RES_INV: 切换失败
 */
lv_res_t lv_i18n_set_lang(lv_i18n_lang_t lang_id);

/**
 * 获取指定键对应的翻译文本
 *
 * @param key 文本键
 * @return 当前语言下对应的翻译文本，若未找到则返回键名
 */
const char * lv_i18n_get_text(const char * key);

/**
 * 格式化方式获取翻译文本
 *
 * @param key 文本键
 * @param ... 格式化参数
 * @return 格式化后的翻译文本
 */
const char * lv_i18n_get_text_fmt(const char * key, ...);

/**
 * 获取当前语言的字体
 *
 * @param font_size 字体大小类型
 * @return 字体指针
 */
const lv_font_t * lv_i18n_get_font(lv_i18n_font_size_t font_size);

/**
 * 设置标签的语言字体
 *
 * @param label 标签对象指针
 * @param font_size 字体大小类型
 */
void lv_i18n_set_label_font(lv_obj_t * label, lv_i18n_font_size_t font_size);

/**
 * 将文本对象的文本设置为翻译文本
 *
 * @param obj 文本对象指针 (如label)
 * @param key 文本键
 */
void lv_i18n_set_text(lv_obj_t * obj, const char * key);

/**
 * 将文本对象的文本设置为格式化的翻译文本
 *
 * @param obj 文本对象指针 (如label)
 * @param key 文本键
 * @param ... 格式化参数
 */
void lv_i18n_set_text_fmt(lv_obj_t * obj, const char * key, ...);

/**
 * 刷新所有UI控件的文本
 * 注意：只刷新带有#前缀标记的文本对象
 */
void lv_i18n_refresh_ui(void);

/**
 * 获取语言名称
 *
 * @param lang_id 语言ID
 * @return 语言名称
 */
const char * lv_i18n_get_language_name(lv_i18n_lang_t lang_id);

/**
 * 创建一个多语言标签
 *
 * @param parent 父对象
 * @param key 文本键
 * @param font_size 字体大小
 * @return 创建的标签对象
 */
lv_obj_t * lv_i18n_label_create(lv_obj_t * parent, const char * key, lv_i18n_font_size_t font_size);

/**********************
 *      MACROS
 **********************/

/**
 * 定义翻译文本
 *
 * 示例: LV_I18N_TEXT("HELLO", "Hello World!")
 */
#define LV_I18N_TEXT(key, value) {key, value}

/**
 * 标记翻译的结束
 */
#define LV_I18N_TEXT_END {NULL, NULL}

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* LV_I18N_H */
