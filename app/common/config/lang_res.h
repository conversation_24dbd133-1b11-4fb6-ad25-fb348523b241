/**
 * @file lang_res.h
 * @brief 语言资源头文件
 */

#ifndef LANG_RES_H
#define LANG_RES_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "lvgl/lvgl.h"

/*********************
 *      MACROS
 *********************/

/**
 * 语言ID枚举
 */
typedef enum {
    LV_LANG_EN = 0,  /* 英语 */
    LV_LANG_ZH,      /* 中文 */
    LV_LANG_CNT      /* 语言计数 */
} lv_lang_t;


#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* LANG_RES_H */
