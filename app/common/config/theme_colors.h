/**
 * @file theme_colors.h
 * @brief 主题颜色常量定义
 */

#ifndef THEME_COLORS_H
#define THEME_COLORS_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "lvgl/lvgl.h"

/*********************
 *      DEFINES
 *********************/

/* 默认主题颜色 */
#define THEME_DEFAULT_PRIMARY           ((lv_color_t){{0xdb, 0x98, 0x34, 0xff}})  /* #3498db */
#define THEME_DEFAULT_SECONDARY         ((lv_color_t){{0xb9, 0x80, 0x29, 0xff}})  /* #2980b9 */
#define THEME_DEFAULT_BACKGROUND        ((lv_color_t){{0xf5, 0xf5, 0xf5, 0xff}})  /* #f5f5f5 */
#define THEME_DEFAULT_SURFACE           ((lv_color_t){{0xff, 0xff, 0xff, 0xff}})  /* #ffffff */
#define THEME_DEFAULT_TEXT_PRIMARY      ((lv_color_t){{0x33, 0x33, 0x33, 0xff}})  /* #333333 */
#define THEME_DEFAULT_TEXT_SECONDARY    ((lv_color_t){{0x66, 0x66, 0x66, 0xff}})  /* #666666 */
#define THEME_DEFAULT_ACCENT            ((lv_color_t){{0x71, 0xcc, 0x2e, 0xff}})  /* #2ecc71 */
#define THEME_DEFAULT_ERROR             ((lv_color_t){{0x3c, 0x4c, 0xe7, 0xff}})  /* #e74c3c */
#define THEME_DEFAULT_SUCCESS           ((lv_color_t){{0x71, 0xcc, 0x2e, 0xff}})  /* #2ecc71 */
#define THEME_DEFAULT_WARNING           ((lv_color_t){{0x12, 0x9c, 0xf3, 0xff}})  /* #f39c12 */

/* 深色主题颜色 */
#define THEME_DARK_PRIMARY              ((lv_color_t){{0xb9, 0x80, 0x29, 0xff}})  /* #2980b9 */
#define THEME_DARK_SECONDARY            ((lv_color_t){{0xdb, 0x98, 0x34, 0xff}})  /* #3498db */
#define THEME_DARK_BACKGROUND           ((lv_color_t){{0x12, 0x12, 0x12, 0xff}})  /* #121212 */
#define THEME_DARK_SURFACE              ((lv_color_t){{0x1e, 0x1e, 0x1e, 0xff}})  /* #1e1e1e */
#define THEME_DARK_TEXT_PRIMARY         ((lv_color_t){{0xff, 0xff, 0xff, 0xff}})  /* #ffffff */
#define THEME_DARK_TEXT_SECONDARY       ((lv_color_t){{0xcc, 0xcc, 0xcc, 0xff}})  /* #cccccc */
#define THEME_DARK_ACCENT               ((lv_color_t){{0x71, 0xcc, 0x2e, 0xff}})  /* #2ecc71 */
#define THEME_DARK_ERROR                ((lv_color_t){{0x3c, 0x4c, 0xe7, 0xff}})  /* #e74c3c */
#define THEME_DARK_SUCCESS              ((lv_color_t){{0x71, 0xcc, 0x2e, 0xff}})  /* #2ecc71 */
#define THEME_DARK_WARNING              ((lv_color_t){{0x12, 0x9c, 0xf3, 0xff}})  /* #f39c12 */

/* 蓝色主题颜色 */
#define THEME_BLUE_PRIMARY              ((lv_color_t){{0xe5, 0x88, 0x1e, 0xff}})  /* #1e88e5 */
#define THEME_BLUE_SECONDARY            ((lv_color_t){{0xa1, 0x47, 0x0d, 0xff}})  /* #0d47a1 */
#define THEME_BLUE_BACKGROUND           ((lv_color_t){{0xfd, 0xf2, 0xe3, 0xff}})  /* #e3f2fd */
#define THEME_BLUE_SURFACE              ((lv_color_t){{0xff, 0xff, 0xff, 0xff}})  /* #ffffff */
#define THEME_BLUE_TEXT_PRIMARY         ((lv_color_t){{0x33, 0x33, 0x33, 0xff}})  /* #333333 */
#define THEME_BLUE_TEXT_SECONDARY       ((lv_color_t){{0x66, 0x66, 0x66, 0xff}})  /* #666666 */
#define THEME_BLUE_ACCENT               ((lv_color_t){{0xff, 0xb0, 0x00, 0xff}})  /* #00b0ff */
#define THEME_BLUE_ERROR                ((lv_color_t){{0x3c, 0x4c, 0xe7, 0xff}})  /* #e74c3c */
#define THEME_BLUE_SUCCESS              ((lv_color_t){{0x71, 0xcc, 0x2e, 0xff}})  /* #2ecc71 */
#define THEME_BLUE_WARNING              ((lv_color_t){{0x12, 0x9c, 0xf3, 0xff}})  /* #f39c12 */

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* THEME_COLORS_H */ 