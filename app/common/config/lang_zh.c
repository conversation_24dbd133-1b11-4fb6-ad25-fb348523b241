/**
 * @file lang_zh.c
 * @brief 中文语言资源
 */

/*********************
 *      INCLUDES
 *********************/
#include "../i18n/lv_i18n.h"

/*********************
 *      MACROS
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *   GLOBAL VARIABLES
 **********************/

/**
 * 中文语言资源包
 */
const lv_i18n_lang_pack_t lang_zh_pack[] = {
  /* 欢迎和导航文本 */
  LV_I18N_TEXT("WELCOME", "欢迎"),
  LV_I18N_TEXT("SETTING", "设置"),
  LV_I18N_TEXT("HOME", "主页"),
  LV_I18N_TEXT("HICAR", "Hicar"),
  LV_I18N_TEXT("YILIAN", "亿连"),
  LV_I18N_TEXT("PLAYER", "播放器"),

  /* 播放控制文本 */
  LV_I18N_TEXT("PLAY", "播放"),
  LV_I18N_TEXT("PAUSE", "暂停"),
  LV_I18N_TEXT("STOP", "停止"),
  LV_I18N_TEXT("NEXT", "下一曲"),
  LV_I18N_TEXT("PREVIOUS", "上一曲"),

  /* 系统设置文本 */
  LV_I18N_TEXT("VOLUME", "音量"),
  LV_I18N_TEXT("SYSTEM", "系统"),
  LV_I18N_TEXT("VEHICLE", "车辆"),
  LV_I18N_TEXT("OTHER", "其他"),
  LV_I18N_TEXT("DATE", "日期"),
  LV_I18N_TEXT("UNIT", "单位"),
  LV_I18N_TEXT("LIGHT", "灯光"),
  LV_I18N_TEXT("WIFI", "Wifi"),
  LV_I18N_TEXT("LANGUAGE", "语言"),
  LV_I18N_TEXT("THEME", "主题"),
  LV_I18N_TEXT("BLUETOOTH", "蓝牙"),

  /* 安全和连接文本 */
  LV_I18N_TEXT("UNLOCK", "解锁方式"),
  LV_I18N_TEXT("LOCATION", "车辆定位"),
  LV_I18N_TEXT("PASSWORD", "密码"),
  LV_I18N_TEXT("STA_ODO", "%05dkm"),
  LV_I18N_TEXT("ODO", "总里程：  %.2f km"),
  LV_I18N_TEXT("SUB_MILEAGE", "小计里程：  %.2f km"),
  LV_I18N_TEXT("SW", "软件版本"),
  LV_I18N_TEXT("HW", "硬件版本"),
  LV_I18N_TEXT("SW_INFO", "SW: V%s"),
  LV_I18N_TEXT("HW_INFO", "HW: V%s"),
  LV_I18N_TEXT("RESET", "恢复出厂设置"),
  LV_I18N_TEXT("EXECUTE", "执行"),

  /* 单位和模式文本 */
  LV_I18N_TEXT("KM_H", "公里/小时"),
  LV_I18N_TEXT("SPORT", "运动模式"),
  LV_I18N_TEXT("NORMAL", "正常模式"),
  LV_I18N_TEXT("SUCCESS", "成功"),
  LV_I18N_TEXT("FAIL", "失败"),
  LV_I18N_TEXT("REFRESH", "刷新"),
  LV_I18N_TEXT("CONFIRM", "确认"),
  LV_I18N_TEXT("SHUTDOWN", "关机"),
  LV_I18N_TEXT("CANCEL", "取消"),
  LV_I18N_TEXT("ON", "开启"),
  LV_I18N_TEXT("OFF", "关闭"),

  /* 连接相关文本 */
  LV_I18N_TEXT("CONNECT", "连接"),
  LV_I18N_TEXT("CONNECTED", "已连接"),
  LV_I18N_TEXT("CONNECT_MSG", "确认与%s连接？"),
  LV_I18N_TEXT("DISCONNECT", "断开"),
  LV_I18N_TEXT("DISCONNECT_MSG", "确认与%s断开？"),
  LV_I18N_TEXT("PAIRING", "配对"),
  LV_I18N_TEXT("PAIRED", "已配对"),
  LV_I18N_TEXT("UNPAIRED", "未配对"),
  LV_I18N_TEXT("PAIRING_MSG", "确认与%s进行配对？"),\
  LV_I18N_TEXT("PLEASE", "请输入"),
  LV_I18N_TEXT("PLEASE_ENTER", "请输入%s密码"),
  LV_I18N_TEXT("ENTER_ERROR", "%s密码错误，请重试"),
  LV_I18N_TEXT("NOT_CONNECT", "%s还没有连接"),
  LV_I18N_TEXT("CONNECT_SUCCESS", "%s连接成功"),
  LV_I18N_TEXT("CONNECT_FAIL", "%s连接错误"),
  LV_I18N_TEXT("HICAR_NOT_CONNECT", "Hicar未连接"),

  /* 状态文本 */
  LV_I18N_TEXT("CHARGING", "充电中"),
  LV_I18N_TEXT("PITCH_ANGLE", "俯仰角：%.2f°"),
  LV_I18N_TEXT("ROLL_ANGLE", "侧倾角：%.2f°"),
  LV_I18N_TEXT("ACCELERATION", "加速度：%.2fg"),

  /* 连接2字符 */
  LV_I18N_TEXT("LINK_2", "%s%s"),

  /* 实时状态 */
  LV_I18N_TEXT("POWER", "%02dKw/h %dKm"),
  LV_I18N_TEXT("TEMPERATURE", "%.1f°C"),
  LV_I18N_TEXT("TRIP", "%d:%02dH/%dKm"),

  /* 结束标记 */
  LV_I18N_TEXT_END
};