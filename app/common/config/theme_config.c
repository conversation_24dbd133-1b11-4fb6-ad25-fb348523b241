/**
 * @file theme_config.c
 * @brief 主题配置源文件
 */

/*********************
 *      INCLUDES
 *********************/
#include "theme_config.h"
#include "../theme/lv_theme_mgr.h"
#include "theme_colors.h"

/*********************
 *      MACROS
 *********************/

/* 图片资源前缀 */
#define IMG_PREFIX_DEFAULT ""
#define IMG_PREFIX_DARK "dark_"
#define IMG_PREFIX_BLUE "blue_"

/**********************
 *  STATIC PROTOTYPES
 **********************/

/**********************
 *  STATIC VARIABLES
 **********************/

/**********************
 *   GLOBAL VARIABLES
 **********************/

/**
 * 主题配置数组
 */
const lv_theme_mgr_theme_config_t theme_configs[LV_THEME_CNT] = {
    /* 默认浅色主题 */
    {
        .theme_name = "默认",
        .colors = LV_THEME_MGR_COLORS(
            THEME_DEFAULT_PRIMARY,        /* primary */
            THEME_DEFAULT_SECONDARY,      /* secondary */
            THEME_DEFAULT_BACKGROUND,     /* background */
            THEME_DEFAULT_SURFACE,        /* surface */
            THEME_DEFAULT_TEXT_PRIMARY,   /* text_primary */
            THEME_DEFAULT_TEXT_SECONDARY, /* text_secondary */
            THEME_DEFAULT_ACCENT,         /* accent */
            THEME_DEFAULT_ERROR,          /* error */
            THEME_DEFAULT_SUCCESS,        /* success */
            THEME_DEFAULT_WARNING         /* warning */
        ),
        .img_prefix = LV_THEME_MGR_IMG_PREFIX(IMG_PREFIX_DEFAULT, false),
        .lvgl_theme = NULL,
        .user_data = NULL
    },
    
    /* 深色主题 */
    {
        .theme_name = "暗黑",
        .colors = LV_THEME_MGR_COLORS(
            THEME_DARK_PRIMARY,        /* primary */
            THEME_DARK_SECONDARY,      /* secondary */
            THEME_DARK_BACKGROUND,     /* background */
            THEME_DARK_SURFACE,        /* surface */
            THEME_DARK_TEXT_PRIMARY,   /* text_primary */
            THEME_DARK_TEXT_SECONDARY, /* text_secondary */
            THEME_DARK_ACCENT,         /* accent */
            THEME_DARK_ERROR,          /* error */
            THEME_DARK_SUCCESS,        /* success */
            THEME_DARK_WARNING         /* warning */
        ),
        .img_prefix = LV_THEME_MGR_IMG_PREFIX(IMG_PREFIX_DARK, true),
        .lvgl_theme = NULL,
        .user_data = NULL
    }
}; 