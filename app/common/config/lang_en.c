/**
 * @file lang_en.c
 * @brief 英语语言资源
 */

/*********************
 *      INCLUDES
 *********************/
#include "../i18n/lv_i18n.h"

/*********************
 *      MACROS
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *   GLOBAL VARIABLES
 **********************/

/**
 * 英语语言资源包
 */
const lv_i18n_lang_pack_t lang_en_pack[] = {
  /* Welcome and navigation text */
  LV_I18N_TEXT("WELCOME", "Welcome"),
  LV_I18N_TEXT("SETTING", "Settings"),
  LV_I18N_TEXT("HOME", "Home"),
  LV_I18N_TEXT("HICAR", "Hicar"),
  LV_I18N_TEXT("YILIAN", "Yilian"),
  LV_I18N_TEXT("PLAYER", "Player"),

  /* Playback control text */
  LV_I18N_TEXT("PLAY", "Play"),
  LV_I18N_TEXT("PAUSE", "Pause"),
  LV_I18N_TEXT("STOP", "Stop"),
  LV_I18N_TEXT("NEXT", "Next"),
  LV_I18N_TEXT("PREVIOUS", "Previous"),

  /* System settings text */
  LV_I18N_TEXT("VOLUME", "Volume"),
  LV_I18N_TEXT("SYSTEM", "System"),
  LV_I18N_TEXT("VEHICLE", "Vehicle"),
  LV_I18N_TEXT("OTHER", "Other"),
  LV_I18N_TEXT("DATE", "Date"),
  LV_I18N_TEXT("UNIT", "Unit"),
  LV_I18N_TEXT("LIGHT", "Light"),
  LV_I18N_TEXT("WIFI", "Wifi"),
  LV_I18N_TEXT("LANGUAGE", "Language"),
  LV_I18N_TEXT("THEME", "Theme"),
  LV_I18N_TEXT("BLUETOOTH", "Bluetooth"),

  /* Safety and connection text */
  LV_I18N_TEXT("UNLOCK", "Unlock method"),
  LV_I18N_TEXT("LOCATION", "Vehicle location"),
  LV_I18N_TEXT("PASSWORD", "Password"),
  LV_I18N_TEXT("ODO", "Total mileage: %.2f km"),
  LV_I18N_TEXT("SUB_MILEAGE", "Sub mileage: %.2f km"),
  LV_I18N_TEXT("SW", "Software version"),
  LV_I18N_TEXT("HW", "Hardware version"),
  LV_I18N_TEXT("SW_INFO", "SW: %s"),
  LV_I18N_TEXT("HW_INFO", "HW: %s"),
  LV_I18N_TEXT("RESET", "Factory reset"),
  LV_I18N_TEXT("EXECUTE", "Execute"),

  /* Unit and mode text */
  LV_I18N_TEXT("KM_H", "Kilometers/hour"),
  LV_I18N_TEXT("SPORT", "Sport mode"),
  LV_I18N_TEXT("NORMAL", "Normal mode"),
  LV_I18N_TEXT("SUCCESS", "Success"),
  LV_I18N_TEXT("FAIL", "Fail"),
  LV_I18N_TEXT("REFRESH", "Refresh"),
  LV_I18N_TEXT("CONFIRM", "Confirm"),
  LV_I18N_TEXT("SHUTDOWN", "Shut down"),
  LV_I18N_TEXT("CANCEL", "Cancel"),
  LV_I18N_TEXT("ON", "On"),
  LV_I18N_TEXT("OFF", "Off"),

  /* Connection related text */
  LV_I18N_TEXT("CONNECT", "Connect"),
  LV_I18N_TEXT("CONNECT_MSG", "Confirm to connect with %s?"),
  LV_I18N_TEXT("DISCONNECT", "Disconnect"),
  LV_I18N_TEXT("DISCONNECT_MSG", "Confirm to disconnect with %s?"),
  LV_I18N_TEXT("PAIRING", "Pairing"),
  LV_I18N_TEXT("PAIRING_MSG", "Confirm to pair with %s?"),
  LV_I18N_TEXT("PLEASE_ENTER", "Please enter Wifi password"),
  LV_I18N_TEXT("ENTER_ERROR", "Password error, please try again"),
  LV_I18N_TEXT("HICAR_NOT_CONNECT", "Hicar not connect"),

  /* Status text */
  LV_I18N_TEXT("CHARGING", "Charging"),
  LV_I18N_TEXT("PITCH_ANGLE", "Pitch angle"),
  LV_I18N_TEXT("ROLL_ANGLE", "Roll angle"),
  LV_I18N_TEXT("ACCELERATION", "Acceleration"),

  /* End marker */
  LV_I18N_TEXT_END
};