/**
 * @file theme_config.h
 * @brief 主题配置头文件
 */

#ifndef THEME_CONFIG_H
#define THEME_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "lvgl/lvgl.h"

/*********************
 *      MACROS
 *********************/

/**
 * 主题ID枚举
 */
typedef enum {
    LV_THEME_DEFAULT = 0,  /* 默认浅色主题 */
    LV_THEME_DARK,         /* 深色主题 */
    LV_THEME_BLUE,         /* 蓝色主题 */
    LV_THEME_GREEN,        /* 绿色主题 */
    LV_THEME_CNT           /* 主题计数 */
} lv_app_theme_t;

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* THEME_CONFIG_H */ 