/**
 * @file lang_config.c
 * @brief 语言配置文件
 */

/*********************
 *      INCLUDES
 *********************/
#include "lang_res.h"
#include "../i18n/lv_i18n.h"
#include "../fonts/PuHui.h" /* 中文字库 */

/*********************
 *      MACROS
 *********************/

/* 声明外部字体 */
LV_FONT_DECLARE(lv_font_montserrat_12);
LV_FONT_DECLARE(lv_font_montserrat_16);
LV_FONT_DECLARE(lv_font_montserrat_22);
// LV_FONT_DECLARE(PuHui18);  /* 中日韩字体 */

/**********************
 *   EXTERNAL VARIABLES
 **********************/

/* 语言资源包外部引用 */
extern const lv_i18n_lang_pack_t lang_en_pack[];
extern const lv_i18n_lang_pack_t lang_zh_pack[];

/**********************
 *   GLOBAL VARIABLES
 **********************/

/**
 * 语言配置
 */
const lv_i18n_language_t lv_i18n_language_pack[LV_LANG_CNT] = {
    /* 英语配置 */
    {
        .language_name = "English",
        .lang_pack = lang_en_pack,
        .fonts = {
            .small_font = &PuHui16,
            .normal_font = &PuHui16,
            .large_font = &PuHui16
        }
    },

    /* 中文配置 */
    {
        .language_name = "中文",
        .lang_pack = lang_zh_pack,
        .fonts = {
            .small_font = &PuHui16,
            .normal_font = &PuHui16,
            .large_font = &PuHui16
        }
    }
};
