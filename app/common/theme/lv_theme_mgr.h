/**
 * @file lv_theme_mgr.h
 * @brief LVGL主题管理库
 *
 * 提供LVGL应用程序的主题管理功能，包括颜色、图片资源前缀等核心功能切换。
 */

#ifndef LV_THEME_MGR_H
#define LV_THEME_MGR_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "lvgl/lvgl.h"

/*********************
 *      DEFINES
 *********************/

/* 设置最大主题数量，可在lv_conf.h中覆盖 */
#ifndef LV_THEME_MGR_THEME_CNT
#define LV_THEME_MGR_THEME_CNT 8
#endif

/* 设置最大资源前缀长度，可在lv_conf.h中覆盖 */
#ifndef LV_THEME_MGR_PREFIX_LEN
#define LV_THEME_MGR_PREFIX_LEN 16
#endif

/* 是否启用调试日志，可在lv_conf.h中覆盖 */
#ifndef LV_THEME_MGR_DEBUG_ENABLE
#define LV_THEME_MGR_DEBUG_ENABLE 0
#endif

/**********************
 *      TYPEDEFS
 **********************/

/**
 * 主题ID类型
 */
typedef uint8_t lv_theme_mgr_theme_t;

/**
 * 主题颜色配置
 */
typedef struct {
    lv_color_t primary;          /* 主色 */
    lv_color_t secondary;        /* 次色 */
    lv_color_t background;       /* 背景色 */
    lv_color_t surface;          /* 前景色 */
    lv_color_t text_primary;     /* 主文本色 */
    lv_color_t text_secondary;   /* 次文本色 */
    lv_color_t accent;           /* 强调色 */
    lv_color_t error;            /* 错误色 */
    lv_color_t success;          /* 成功色 */
    lv_color_t warning;          /* 警告色 */
} lv_theme_mgr_colors_t;

/**
 * 图片资源前缀结构
 */
typedef struct {
    char prefix[LV_THEME_MGR_PREFIX_LEN]; /* 图片资源前缀 */
    bool use_prefix;                      /* 是否使用前缀 */
} lv_theme_mgr_img_prefix_t;

/**
 * 主题配置
 */
typedef struct {
    const char * theme_name;                      /* 主题名称 */
    lv_theme_mgr_colors_t colors;                /* 主题颜色 */
    lv_theme_mgr_img_prefix_t img_prefix;        /* 图片资源前缀 */
    lv_theme_t * lvgl_theme;                     /* LVGL主题 */
    const void * user_data;                      /* 用户自定义数据 */
} lv_theme_mgr_theme_config_t;

/**
 * 主题切换回调函数类型
 * @param old_theme 旧主题ID
 * @param new_theme 新主题ID
 */
typedef void (*lv_theme_mgr_cb_t)(lv_theme_mgr_theme_t old_theme, lv_theme_mgr_theme_t new_theme);

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**
 * 初始化主题管理器
 *
 * @param themes 主题配置数组
 * @param default_theme 默认主题ID
 * @return LV_RES_OK: 初始化成功; LV_RES_INV: 初始化失败
 */
lv_res_t lv_theme_mgr_init(const lv_theme_mgr_theme_config_t * themes, lv_theme_mgr_theme_t default_theme);

/**
 * 获取当前主题ID
 *
 * @return 当前主题ID
 */
lv_theme_mgr_theme_t lv_theme_mgr_get_current_theme(void);

/**
 * 切换到指定主题
 *
 * @param theme_id 目标主题ID
 * @return LV_RES_OK: 切换成功; LV_RES_INV: 切换失败
 */
lv_res_t lv_theme_mgr_set_theme(lv_theme_mgr_theme_t theme_id);

/**
 * 注册主题切换回调函数
 *
 * @param cb 回调函数指针
 */
void lv_theme_mgr_register_callback(lv_theme_mgr_cb_t cb);

/**
 * 获取当前主题的颜色配置
 *
 * @return 颜色配置指针
 */
const lv_theme_mgr_colors_t * lv_theme_mgr_get_colors(void);

/**
 * 获取指定主题的颜色配置
 *
 * @param theme_id 主题ID
 * @return 颜色配置指针，若主题ID无效则返回NULL
 */
const lv_theme_mgr_colors_t * lv_theme_mgr_get_theme_colors(lv_theme_mgr_theme_t theme_id);

/**
 * 获取当前主题的主色
 *
 * @return 主色
 */
lv_color_t lv_theme_mgr_get_primary_color(void);

/**
 * 获取当前主题的次色
 *
 * @return 次色
 */
lv_color_t lv_theme_mgr_get_secondary_color(void);

/**
 * 获取当前主题的背景色
 *
 * @return 背景色
 */
lv_color_t lv_theme_mgr_get_background_color(void);

/**
 * 获取当前主题的图片资源前缀
 *
 * @return 图片资源前缀，若不使用前缀则返回空字符串
 */
const char * lv_theme_mgr_get_img_prefix(void);

/**
 * 获取当前主题的LVGL主题
 *
 * @return LVGL主题指针
 */
lv_theme_t * lv_theme_mgr_get_lvgl_theme(void);

/**
 * 获取当前主题的名称
 *
 * @return 主题名称
 */
const char * lv_theme_mgr_get_theme_name(void);

/**
 * 获取当前主题的用户自定义数据
 *
 * @return 用户自定义数据指针
 */
const void * lv_theme_mgr_get_user_data(void);

/**
 * 根据图片资源名称获取完整的图片资源路径
 *
 * @param img_name 图片资源名称
 * @param buf 输出缓冲区
 * @param buf_len 缓冲区长度
 * @return 完整的图片资源路径
 */
const char * lv_theme_mgr_get_img_path(const char * img_name, char * buf, size_t buf_len);

/**
 * 为对象设置主题颜色
 *
 * @param obj 对象指针
 * @param primary 是否使用主色，否则使用次色
 */
void lv_theme_mgr_apply_color(lv_obj_t * obj, bool primary);

/**
 * 创建带主题颜色的按钮
 *
 * @param parent 父对象
 * @param primary 是否使用主色，否则使用次色
 * @return 创建的按钮对象
 */
lv_obj_t * lv_theme_mgr_btn_create(lv_obj_t * parent, bool primary);

/**
 * 创建带主题图片的图片对象
 *
 * @param parent 父对象
 * @param img_name 图片资源名称（不包含前缀）
 * @return 创建的图片对象
 */
lv_obj_t * lv_theme_mgr_img_create(lv_obj_t * parent, const char * img_name);

/**********************
 *      MACROS
 **********************/

/**
 * 定义主题颜色
 */
#define LV_THEME_MGR_COLORS(prim, sec, bg, surf, t_prim, t_sec, acc, err, succ, warn) \
    { \
        .primary = prim, \
        .secondary = sec, \
        .background = bg, \
        .surface = surf, \
        .text_primary = t_prim, \
        .text_secondary = t_sec, \
        .accent = acc, \
        .error = err, \
        .success = succ, \
        .warning = warn \
    }

/**
 * 定义图片资源前缀
 */
#define LV_THEME_MGR_IMG_PREFIX(prefix_str, use) \
    { \
        .prefix = prefix_str, \
        .use_prefix = use \
    }

/**
 * 定义不使用图片资源前缀
 */
#define LV_THEME_MGR_NO_PREFIX() \
    { \
        .prefix = "", \
        .use_prefix = false \
    }

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* LV_THEME_MGR_H */
