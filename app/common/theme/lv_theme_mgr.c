/**
 * @file lv_theme_mgr.c
 * @brief LVGL主题管理库实现
 */

/*********************
 *      INCLUDES
 *********************/
#include "lv_theme_mgr.h"
#include <string.h>
#include <stdio.h>

/*********************
 *      DEFINES
 *********************/

#if LV_THEME_MGR_DEBUG_ENABLE
#define LV_THEME_MGR_DEBUG(fmt, ...) LV_LOG_USER("[THEME] " fmt, ##__VA_ARGS__)
#else
#define LV_THEME_MGR_DEBUG(fmt, ...)
#endif

/* 图片路径缓冲区大小 */
#define LV_THEME_MGR_IMG_PATH_BUF_SIZE 64

/**********************
 *  STATIC PROTOTYPES
 **********************/
static void _apply_lvgl_theme(lv_theme_t * theme);
static void _apply_theme_to_screen(void);
static void _dump_theme_config(const lv_theme_mgr_theme_config_t * theme_config);

/**********************
 *  STATIC VARIABLES
 **********************/
static lv_theme_mgr_theme_t s_current_theme;                           /* 当前主题ID */
static char s_img_path_buf[LV_THEME_MGR_IMG_PATH_BUF_SIZE];            /* 图片路径缓冲区 */
static const lv_theme_mgr_theme_config_t * s_themes;                   /* 主题配置数组 */
static uint8_t s_theme_cnt;                                            /* 主题数量 */
static bool s_is_initialized = false;                                  /* 初始化标志 */
static lv_theme_mgr_cb_t s_theme_change_cb = NULL;                     /* 主题切换回调 */

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

/**
 * 初始化主题管理器
 * 
 * @param themes 主题配置数组
 * @param default_theme 默认主题ID
 * @return LV_RES_OK: 初始化成功; LV_RES_INV: 初始化失败
 */
lv_res_t lv_theme_mgr_init(const lv_theme_mgr_theme_config_t * themes, lv_theme_mgr_theme_t default_theme)
{
    if (themes == NULL) {
        LV_LOG_ERROR("Theme configs cannot be NULL");
        return LV_RES_INV;
    }
    
    /* 计算主题数量 */
    s_theme_cnt = 0;
    while (themes[s_theme_cnt].theme_name != NULL && s_theme_cnt < LV_THEME_MGR_THEME_CNT) {
        s_theme_cnt++;
    }
    
    if (s_theme_cnt == 0) {
        LV_LOG_ERROR("No valid themes found");
        return LV_RES_INV;
    }
    
    if (default_theme >= s_theme_cnt) {
        LV_LOG_ERROR("Invalid default theme ID");
        return LV_RES_INV;
    }
    
    /* 保存主题配置 */
    s_themes = themes;
    s_current_theme = default_theme;
    s_is_initialized = true;
    
    LV_THEME_MGR_DEBUG("主题管理器初始化完成，默认主题: %s (ID: %d)", 
                      themes[default_theme].theme_name, default_theme);
    
    /* 输出主题配置信息 */
    _dump_theme_config(&themes[default_theme]);
    
    /* 应用LVGL主题 */
    _apply_lvgl_theme(themes[default_theme].lvgl_theme);
    
    /* 应用主题到当前屏幕 */
    _apply_theme_to_screen();
    
    return LV_RES_OK;
}

/**
 * 获取当前主题ID
 * 
 * @return 当前主题ID
 */
lv_theme_mgr_theme_t lv_theme_mgr_get_current_theme(void)
{
    return s_current_theme;
}

/**
 * 切换到指定主题
 * 
 * @param theme_id 目标主题ID
 * @return LV_RES_OK: 切换成功; LV_RES_INV: 切换失败
 */
lv_res_t lv_theme_mgr_set_theme(lv_theme_mgr_theme_t theme_id)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("Theme manager not initialized");
        return LV_RES_INV;
    }
    
    if (theme_id >= s_theme_cnt) {
        LV_LOG_ERROR("Invalid theme ID: %d", theme_id);
        return LV_RES_INV;
    }
    
    if (theme_id == s_current_theme) {
        /* 已经是当前主题，无需切换 */
        return LV_RES_OK;
    }
    
    LV_THEME_MGR_DEBUG("切换主题: %s -> %s (ID: %d -> %d)", 
                      s_themes[s_current_theme].theme_name, 
                      s_themes[theme_id].theme_name,
                      s_current_theme, theme_id);
    
    /* 保存旧主题ID用于回调 */
    lv_theme_mgr_theme_t old_theme = s_current_theme;
    
    /* 更新当前主题 */
    s_current_theme = theme_id;
    
    /* 输出主题配置信息 */
    _dump_theme_config(&s_themes[theme_id]);
    
    /* 应用LVGL主题 */
    _apply_lvgl_theme(s_themes[theme_id].lvgl_theme);
    
    /* 应用主题到当前屏幕 */
    _apply_theme_to_screen();
    
    /* 调用主题切换回调 */
    if (s_theme_change_cb != NULL) {
        s_theme_change_cb(old_theme, theme_id);
    }
    
    return LV_RES_OK;
}

/**
 * 注册主题切换回调函数
 * 
 * @param cb 回调函数指针
 */
void lv_theme_mgr_register_callback(lv_theme_mgr_cb_t cb)
{
    s_theme_change_cb = cb;
}

/**
 * 获取当前主题的颜色配置
 * 
 * @return 颜色配置指针
 */
const lv_theme_mgr_colors_t * lv_theme_mgr_get_colors(void)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("Theme manager not initialized");
        return NULL;
    }
    
    return &s_themes[s_current_theme].colors;
}

/**
 * 获取指定主题的颜色配置
 * 
 * @param theme_id 主题ID
 * @return 颜色配置指针，若主题ID无效则返回NULL
 */
const lv_theme_mgr_colors_t * lv_theme_mgr_get_theme_colors(lv_theme_mgr_theme_t theme_id)
{
    if (!s_is_initialized || theme_id >= s_theme_cnt) {
        LV_LOG_ERROR("Invalid theme ID or theme manager not initialized");
        return NULL;
    }
    
    return &s_themes[theme_id].colors;
}

/**
 * 获取当前主题的主色
 * 
 * @return 主色
 */
lv_color_t lv_theme_mgr_get_primary_color(void)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("Theme manager not initialized");
        return lv_color_black();
    }
    
    return s_themes[s_current_theme].colors.primary;
}

/**
 * 获取当前主题的次色
 * 
 * @return 次色
 */
lv_color_t lv_theme_mgr_get_secondary_color(void)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("Theme manager not initialized");
        return lv_color_black();
    }
    
    return s_themes[s_current_theme].colors.secondary;
}

/**
 * 获取当前主题的背景色
 * 
 * @return 背景色
 */
lv_color_t lv_theme_mgr_get_background_color(void)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("Theme manager not initialized");
        return lv_color_black();
    }
    
    return s_themes[s_current_theme].colors.background;
}

/**
 * 获取当前主题的图片资源前缀
 * 
 * @return 图片资源前缀，若不使用前缀则返回空字符串
 */
const char * lv_theme_mgr_get_img_prefix(void)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("Theme manager not initialized");
        return "";
    }
    
    const lv_theme_mgr_img_prefix_t * prefix = &s_themes[s_current_theme].img_prefix;
    
    if (!prefix->use_prefix) {
        return "";
    }
    
    return prefix->prefix;
}

/**
 * 获取当前主题的LVGL主题
 * 
 * @return LVGL主题指针
 */
lv_theme_t * lv_theme_mgr_get_lvgl_theme(void)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("Theme manager not initialized");
        return NULL;
    }
    
    return s_themes[s_current_theme].lvgl_theme;
}

/**
 * 获取当前主题的名称
 * 
 * @return 主题名称
 */
const char * lv_theme_mgr_get_theme_name(void)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("Theme manager not initialized");
        return "Unknown";
    }
    
    return s_themes[s_current_theme].theme_name;
}

/**
 * 获取当前主题的用户自定义数据
 * 
 * @return 用户自定义数据指针
 */
const void * lv_theme_mgr_get_user_data(void)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("Theme manager not initialized");
        return NULL;
    }
    
    return s_themes[s_current_theme].user_data;
}

/**
 * 根据图片资源名称获取完整的图片资源路径
 * 
 * @param img_name 图片资源名称
 * @param buf 输出缓冲区
 * @param buf_len 缓冲区长度
 * @return 完整的图片资源路径
 */
const char * lv_theme_mgr_get_img_path(const char * img_name, char * buf, size_t buf_len)
{
    if (!s_is_initialized || img_name == NULL) {
        if (buf && buf_len > 0) {
            buf[0] = '\0';
        }
        return img_name;
    }
    
    const lv_theme_mgr_img_prefix_t * prefix = &s_themes[s_current_theme].img_prefix;
    
    if (!prefix->use_prefix) {
        /* 不使用前缀，直接返回原始名称 */
        if (buf && buf_len > 0) {
            strncpy(buf, img_name, buf_len - 1);
            buf[buf_len - 1] = '\0';
            return buf;
        }
        return img_name;
    }
    
    /* 使用内部缓冲区或提供的缓冲区 */
    char * target_buf = buf ? buf : s_img_path_buf;
    size_t target_len = buf ? buf_len : sizeof(s_img_path_buf);
    
    /* 拼接前缀和图片名称 */
    snprintf(target_buf, target_len, "%s%s", prefix->prefix, img_name);
    
    LV_THEME_MGR_DEBUG("图片路径转换: %s -> %s", img_name, target_buf);
    
    return target_buf;
}

/**
 * 为对象设置主题颜色
 * 
 * @param obj 对象指针
 * @param primary 是否使用主色，否则使用次色
 */
void lv_theme_mgr_apply_color(lv_obj_t * obj, bool primary)
{
    if (!s_is_initialized || obj == NULL) {
        return;
    }
    
    const lv_theme_mgr_colors_t * colors = &s_themes[s_current_theme].colors;
    
    lv_color_t bg_color = primary ? colors->primary : colors->secondary;
    lv_color_t text_color = primary ? colors->text_primary : colors->text_secondary;
    
    /* 设置背景色 */
    lv_obj_set_style_bg_color(obj, bg_color, LV_PART_MAIN);
    
    /* 设置文本颜色 */
    lv_obj_set_style_text_color(obj, text_color, LV_PART_MAIN);
    
    /* 如果是按钮，设置边框颜色 */
    if (lv_obj_has_class(obj, &lv_btn_class)) {
        lv_obj_set_style_border_color(obj, lv_color_darken(bg_color, 20), LV_PART_MAIN);
    }
}

/**
 * 创建带主题颜色的按钮
 * 
 * @param parent 父对象
 * @param primary 是否使用主色，否则使用次色
 * @return 创建的按钮对象
 */
lv_obj_t * lv_theme_mgr_btn_create(lv_obj_t * parent, bool primary)
{
    lv_obj_t * btn = lv_btn_create(parent);
    if (btn == NULL) {
        return NULL;
    }
    
    /* 应用主题颜色 */
    lv_theme_mgr_apply_color(btn, primary);
    
    return btn;
}

/**
 * 创建带主题图片的图片对象
 * 
 * @param parent 父对象
 * @param img_name 图片资源名称（不包含前缀）
 * @return 创建的图片对象
 */
lv_obj_t * lv_theme_mgr_img_create(lv_obj_t * parent, const char * img_name)
{
    lv_obj_t * img = lv_img_create(parent);
    if (img == NULL || img_name == NULL) {
        return img;
    }
    
    /* 获取完整图片路径 */
    const char * img_path = lv_theme_mgr_get_img_path(img_name, NULL, 0);
    
    /* 设置图片 */
    lv_img_set_src(img, img_path);
    
    return img;
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

/**
 * 应用LVGL主题
 */
static void _apply_lvgl_theme(lv_theme_t * theme)
{
    if (theme == NULL) {
        LV_THEME_MGR_DEBUG("LVGL主题为NULL，不应用");
        return;
    }
    
    LV_THEME_MGR_DEBUG("应用LVGL主题: %p", theme);
    
    /* 设置当前活动主题 */
    lv_disp_t * disp = lv_disp_get_default();
    if (disp) {
        lv_disp_set_theme(disp, theme);
    }
}

/**
 * 应用主题到当前屏幕
 */
static void _apply_theme_to_screen(void)
{
    lv_obj_t * scr = lv_scr_act();
    if (scr == NULL) {
        LV_THEME_MGR_DEBUG("无活动屏幕，不应用主题");
        return;
    }
    
    LV_THEME_MGR_DEBUG("应用主题到当前屏幕");
    
    /* 获取当前主题颜色 */
    const lv_theme_mgr_colors_t * colors = &s_themes[s_current_theme].colors;
    
    /* 设置背景色 */
    lv_obj_set_style_bg_color(scr, colors->background, LV_PART_MAIN);
    
    /* 设置文本颜色 */
    lv_obj_set_style_text_color(scr, colors->text_primary, LV_PART_MAIN);
}

/**
 * 输出主题配置信息
 */
static void _dump_theme_config(const lv_theme_mgr_theme_config_t * theme_config)
{
    if (!theme_config) return;
    
    LV_THEME_MGR_DEBUG("主题配置信息:");
    LV_THEME_MGR_DEBUG("  名称: %s", theme_config->theme_name);
    
    LV_THEME_MGR_DEBUG("  颜色:");
    LV_THEME_MGR_DEBUG("    主色: #%06X", lv_color_to32(theme_config->colors.primary) & 0xFFFFFF);
    LV_THEME_MGR_DEBUG("    次色: #%06X", lv_color_to32(theme_config->colors.secondary) & 0xFFFFFF);
    LV_THEME_MGR_DEBUG("    背景色: #%06X", lv_color_to32(theme_config->colors.background) & 0xFFFFFF);
    
    LV_THEME_MGR_DEBUG("  图片前缀:");
    LV_THEME_MGR_DEBUG("    使用前缀: %s", theme_config->img_prefix.use_prefix ? "是" : "否");
    if (theme_config->img_prefix.use_prefix) {
        LV_THEME_MGR_DEBUG("    前缀: %s", theme_config->img_prefix.prefix);
    }
    
    LV_THEME_MGR_DEBUG("  LVGL主题: %p", theme_config->lvgl_theme);
    LV_THEME_MGR_DEBUG("  用户数据: %p", theme_config->user_data);
} 