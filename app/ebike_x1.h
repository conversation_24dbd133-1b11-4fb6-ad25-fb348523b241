/**
 * @file ebike_x1.h
 */

#ifndef EBIKE_X1_H
#define EBIKE_X1_H

#include <stdio.h>
#include <stdlib.h>
#include "lvgl/lvgl.h"

// UI
#include "ui/ui.h"
#include "ui/ui_helpers.h"

// event_bus事件系统，统一引入，减少重复代码
#include "event_bus/include/eb_core.h"
#include "event_bus/include/eb_types.h"

// 多语言支持
#include "common/i18n/lv_i18n.h"
#include "common/config/lang_res.h"
#include "date_utils.h"

// 全局字体
#include "common/fonts/PuHui.h"

#ifdef __cplusplus
extern "C"
{
#endif

// 修复ui报错问题
extern lv_obj_t *ui_imgset_;

/* 引入外部化多语言模块 */
extern const lv_i18n_language_t lv_i18n_language_pack[];

// 全局变量
extern lv_style_t g_style;
extern uint32_t g_time_ms;
extern uint32_t g_time_second;
extern date_time_t g_date_time;

// APP
void app_init(void);
void app_handler(void);
void app_deinit(void);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif