#include "date_utils.h"

/**
 * @brief 日期工具函数
 */
void sys_date_init(void)
{
    date_time_t datetime;
    time_t timestamp;
    char time_str[64];
    date_error_t ret;

    printf("===== 日期初始化 =====\n");

    /* 检查时间是否有效 */
    printf("系统时间是否有效: %s\n", date_is_time_valid() ? "是" : "否");

    /* 如果时间无效，尝试自动校正 */
    if (!date_is_time_valid()) {
        printf("系统时间无效，尝试自动校正...\n");
        ret = date_auto_correct();
        if (ret == DATE_ERROR_NONE) {
            printf("时间自动校正成功\n");
        } else {
            printf("时间自动校正失败，错误码: %d\n", ret);
        }
    }

    /* 获取当前时间 */
    ret = date_get_time(&datetime);
    if (ret == DATE_ERROR_NONE) {
        printf("当前时间: %04d-%02d-%02d %02d:%02d:%02d\n",
               datetime.year, datetime.month, datetime.day,
               datetime.hour, datetime.minute, datetime.second);
    } else {
        printf("获取时间失败，错误码: %d\n", ret);
    }

    /* 获取时间戳 */
    ret = date_get_timestamp(&timestamp);
    if (ret == DATE_ERROR_NONE) {
        printf("当前时间戳: %ld\n", (long)timestamp);
    } else {
        printf("获取时间戳失败，错误码: %d\n", ret);
    }

    /* 时间戳转日期时间 */
    ret = date_timestamp_to_datetime(timestamp, &datetime);
    if (ret == DATE_ERROR_NONE) {
        printf("时间戳转换: %04d-%02d-%02d %02d:%02d:%02d\n",
               datetime.year, datetime.month, datetime.day,
               datetime.hour, datetime.minute, datetime.second);
    } else {
        printf("时间戳转换失败，错误码: %d\n", ret);
    }

    /* 格式化时间 */
    ret = date_format_time(&datetime, "%Y年%m月%d日 %H时%M分%S秒", time_str, sizeof(time_str));
    if (ret == DATE_ERROR_NONE) {
        printf("格式化时间: %s\n", time_str);
    } else {
        printf("格式化时间失败，错误码: %d\n", ret);
    }

    /* 网络时间同步（需要网络连接） */
    printf("尝试从网络同步时间...\n");
    ret = date_sync_from_network("ntp.aliyun.com", &datetime);
    if (ret == DATE_ERROR_NONE) {
        printf("网络时间同步成功: %04d-%02d-%02d %02d:%02d:%02d\n",
               datetime.year, datetime.month, datetime.day,
               datetime.hour, datetime.minute, datetime.second);
    } else {
        printf("网络时间同步失败，错误码: %d\n", ret);
    }

    /* 设置时区（需要root权限） */
    if (geteuid() == 0) {
        ret = date_set_timezone("Asia/Shanghai");
        if (ret == DATE_ERROR_NONE) {
            printf("时区设置成功\n");
        } else {
            printf("时区设置失败，错误码: %d\n", ret);
        }
    } else {
        printf("设置时区需要root权限，跳过\n");
    }

    /* 设置系统时间（需要root权限） */
    if (geteuid() == 0) {
        /* 将当前时间加一小时 */
        datetime.hour = (datetime.hour + 1) % 24;
        ret = date_set_time(&datetime);
        if (ret == DATE_ERROR_NONE) {
            printf("时间设置成功\n");
        } else {
            printf("时间设置失败，错误码: %d\n", ret);
        }
    } else {
        printf("设置系统时间需要root权限，跳过\n");
    }
}