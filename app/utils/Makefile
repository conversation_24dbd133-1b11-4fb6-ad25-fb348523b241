###############################################################################
# 屏幕控制示例程序编译脚本
###############################################################################

# 使用ARM交叉编译器
CC = arm-linux-gnueabi-gcc
STRIP = arm-linux-gnueabi-strip
CFLAGS = -Wall -Wextra -O2 -g
INCLUDES = -I./utils
LDFLAGS = -lpthread -lrt

# 目标可执行文件
TARGET = screen_demo

# 源文件
SRCS = screen_utils.c screen_demo.c

# 目标文件
OBJS = $(SRCS:.c=.o)

# 默认目标
all: $(TARGET)

# 链接规则
$(TARGET): $(OBJS)
	$(CC) $(CFLAGS) $(INCLUDES) -o $@ $^ $(LDFLAGS)
	$(STRIP) $@

# 通用编译规则
%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理中间文件和可执行文件
clean:
	rm -f $(OBJS) $(TARGET)

.PHONY: all clean 