/**
 * @file screen_demo.c
 * @brief 屏幕开关控制示例程序
 * @version 1.0.0
 * @date 2025-06-18 00:02
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include "screen_utils.h"

// 测试运行标志
static int g_running = 1;

/**
 * @brief 信号处理函数
 */
static void signal_handler(int sig) {
    printf("接收到信号 %d，准备退出...\n", sig);
    g_running = 0;
}

/**
 * @brief 主函数
 */
int main(int argc, char *argv[]) {
    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    printf("屏幕控制演示程序启动\n");
    printf("---------------------------\n");
    printf("使用方法:\n");
    printf("1 - 打开屏幕\n");
    printf("0 - 关闭屏幕\n");
    printf("t - 切换屏幕状态\n");
    printf("s - 显示屏幕当前状态\n");
    printf("q - 退出程序\n");
    printf("---------------------------\n");
    
    // 初始化，默认屏幕为打开状态
    printf("初始化屏幕...\n");
    screen_turn_on();
    
    // 主循环
    while (g_running) {
        printf("\n请输入命令: ");
        fflush(stdout);
        
        // 读取用户输入
        int ch = getchar();
        while (getchar() != '\n'); // 清除输入缓冲区
        
        // 处理用户输入
        switch (ch) {
            case '1': // 打开屏幕
                printf("正在打开屏幕...\n");
                if (screen_turn_on() == 0) {
                    printf("屏幕已打开\n");
                } else {
                    printf("打开屏幕失败\n");
                }
                break;
                
            case '0': // 关闭屏幕
                printf("正在关闭屏幕...\n");
                if (screen_turn_off() == 0) {
                    printf("屏幕已关闭\n");
                } else {
                    printf("关闭屏幕失败\n");
                }
                break;
                
            case 't': // 切换屏幕状态
                printf("正在切换屏幕状态...\n");
                if (screen_toggle() == 0) {
                    screen_state_t state;
                    screen_get_state(&state);
                    printf("屏幕状态已切换为: %s\n", (state == SCREEN_STATE_ON) ? "开启" : "关闭");
                } else {
                    printf("切换屏幕状态失败\n");
                }
                break;
                
            case 's': // 显示屏幕当前状态
                {
                    screen_state_t state;
                    if (screen_get_state(&state) == 0) {
                        printf("屏幕当前状态: %s\n", (state == SCREEN_STATE_ON) ? "开启" : "关闭");
                    } else {
                        printf("获取屏幕状态失败\n");
                    }
                }
                break;
                
            case 'q': // 退出程序
                g_running = 0;
                printf("准备退出程序...\n");
                break;
                
            default:
                printf("无效的命令，请重新输入\n");
                break;
        }
    }
    
    // 退出前确保屏幕是打开的
    printf("程序退出，确保屏幕开启...\n");
    screen_turn_on();
    
    printf("屏幕控制演示程序结束\n");
    return 0;
} 