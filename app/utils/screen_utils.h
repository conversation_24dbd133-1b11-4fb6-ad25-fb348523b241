/**
 * @file screen_utils.h
 * @brief 屏幕控制工具函数头文件
 * @version 1.0.0
 * @date 2025-06-18 00:02
 */

#ifndef SCREEN_UTILS_H
#define SCREEN_UTILS_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 屏幕状态枚举
 */
typedef enum {
    SCREEN_STATE_OFF = 0,  /**< 屏幕关闭 */
    SCREEN_STATE_ON = 1    /**< 屏幕打开 */
} screen_state_t;

/**
 * @brief 设置屏幕状态
 * @param state 屏幕状态，SCREEN_STATE_ON为开，SCREEN_STATE_OFF为关
 * @return 0:成功 其他:失败
 */
int screen_set_state(screen_state_t state);

/**
 * @brief 打开屏幕
 * @return 0:成功 其他:失败
 */
int screen_turn_on(void);

/**
 * @brief 关闭屏幕
 * @return 0:成功 其他:失败
 */
int screen_turn_off(void);

/**
 * @brief 切换屏幕状态
 * @return 0:成功 其他:失败
 */
int screen_toggle(void);

/**
 * @brief 获取屏幕当前状态
 * @param state 用于存储状态的指针
 * @return 0:成功 其他:失败
 */
int screen_get_state(screen_state_t *state);

#ifdef __cplusplus
}
#endif

#endif /* SCREEN_UTILS_H */ 