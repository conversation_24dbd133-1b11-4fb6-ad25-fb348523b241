/**
 * @file screen_utils.c
 * @brief 屏幕控制工具函数实现
 * @version 1.0.0
 * @date 2025-06-18 00:02
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sys/wait.h>
#include "screen_utils.h"

// 保存当前屏幕状态
static screen_state_t g_current_screen_state = SCREEN_STATE_ON;

/**
 * @brief 执行系统命令
 * @param command 要执行的命令
 * @return 命令执行结果，0表示成功，非0表示失败
 */
static int execute_command(const char *command) {
    int status;
    
    // 打印调试信息
    printf("执行命令: %s\n", command);
    
    // 执行命令
    status = system(command);
    
    if (status == -1) {
        perror("system");
        return -1;
    }
    
    // 检查命令执行结果
    if (WIFEXITED(status)) {
        int exit_status = WEXITSTATUS(status);
        printf("命令执行完成，退出状态: %d\n", exit_status);
        return exit_status;
    } else {
        printf("命令执行异常\n");
        return -1;
    }
}

/**
 * @brief 设置屏幕状态
 */
int screen_set_state(screen_state_t state) {
    int ret;
    
    if (state == SCREEN_STATE_ON) {
        // 开屏命令: gpio_test -s PD12 1 1 1 1
        ret = execute_command("gpio_test -s PD12 1 1 1 1");
        if (ret == 0) {
            g_current_screen_state = SCREEN_STATE_ON;
        }
    } else {
        // 关屏命令: gpio_test -s PD12 1 0 1 1
        ret = execute_command("gpio_test -s PD12 1 0 1 1");
        if (ret == 0) {
            g_current_screen_state = SCREEN_STATE_OFF;
        }
    }
    
    return ret;
}

/**
 * @brief 打开屏幕
 */
int screen_turn_on(void) {
    return screen_set_state(SCREEN_STATE_ON);
}

/**
 * @brief 关闭屏幕
 */
int screen_turn_off(void) {
    return screen_set_state(SCREEN_STATE_OFF);
}

/**
 * @brief 切换屏幕状态
 */
int screen_toggle(void) {
    if (g_current_screen_state == SCREEN_STATE_ON) {
        return screen_turn_off();
    } else {
        return screen_turn_on();
    }
}

/**
 * @brief 获取屏幕当前状态
 */
int screen_get_state(screen_state_t *state) {
    if (!state) {
        return -1;
    }
    
    *state = g_current_screen_state;
    return 0;
} 