/**
 * @file uart_api.h
 * @brief UART通信协议公共API头文件，隐藏内部实现细节
 * @version 1.0.1
 * @date 2025-07-30 17:19
 */

#ifndef UART_API_H
#define UART_API_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief UART协议句柄(不透明指针)
 */
typedef struct uart_protocol_context* uart_protocol_handle_t;

/**
 * @brief 回调函数类型，用于接收处理UART消息
 * @param id 消息ID
 * @param data 消息数据
 * @param data_len 数据长度
 * @param user_data 用户数据指针
 */
typedef void (*uart_message_callback_t)(uint8_t id, const uint8_t *data, uint16_t data_len, void *user_data);

/**
 * @brief 消息ID定义
 */
typedef enum {
    UART_MSG_POWER_SYNC = 0x01,      /**< 上电同步信息 */
    UART_MSG_UPGRADE = 0x02,         /**< 升级 */
    UART_MSG_HEARTBEAT = 0x03,       /**< 心跳包 */
    UART_MSG_FACTORY_RESET = 0x04,   /**< 工厂复位 */
    UART_MSG_MCU_VERSION = 0x05,     /**< MCU版本号 */
    UART_MSG_LIGHT_SCREEN_CTRL = 0x56, /**< 灯光和屏幕控制 */
    UART_MSG_SCREEN_CTRL = 0x80,     /**< 屏幕控制(已弃用, 使用UART_MSG_LIGHT_SCREEN_CTRL) */
    UART_MSG_SET_TIME = 0x81,        /**< 设置时间 */
    UART_MSG_CLEAR_MILEAGE = 0x82,   /**< 清除里程 */
    UART_MSG_SET_TOTAL_MILE = 0x83,  /**< 设置总里程 */
    UART_MSG_SPEED = 0x60,           /**< 车速 */
    UART_MSG_RPM = 0x61,             /**< 转速 */
    UART_MSG_GEAR = 0x62,            /**< 挡位 */
    UART_MSG_LIGHT = 0x63,           /**< 灯光、警示灯(已弃用，使用UART_MSG_LIGHT_SCREEN_CTRL) */
    UART_MSG_REMAIN_RANGE = 0x64,    /**< 剩余续航里程 */
    UART_MSG_MILEAGE = 0x65,         /**< 总里程和小计里程 */
    UART_MSG_TIME = 0x66,            /**< 时间 */
    UART_MSG_CONTROLLER_STATUS = 0x67, /**< 中控控制器状态 */
    UART_MSG_ACK = 0x55              /**< 应答位 */
} uart_message_id_t;

/**
 * @brief 灯光控制命令
 */
typedef enum {
    LIGHT_CTRL_DOUBLE_FLASH_ON = 0x01,  /**< 打开双闪 */
    LIGHT_CTRL_DOUBLE_FLASH_OFF = 0x02, /**< 关闭双闪 */
    LIGHT_CTRL_LEFT_TURN_ON = 0x03,     /**< 打开左转向 */
    LIGHT_CTRL_RIGHT_TURN_ON = 0x04,    /**< 打开右转向 */
    LIGHT_CTRL_TURN_OFF = 0x05,         /**< 关闭转向 */
    LIGHT_CTRL_HEADLIGHT_ON = 0x06      /**< 打开车灯 */
} light_control_cmd_t;

/**
 * @brief 屏幕控制命令
 */
typedef enum {
    SCREEN_CTRL_OFF = 0x01,         /**< 关闭屏幕 */
    SCREEN_CTRL_ON = 0x02           /**< 打开屏幕 */
} screen_control_cmd_t;

/**
 * @brief 错误码定义
 */
typedef enum {
    UART_ERROR_NONE = 0,            /**< 无错误 */
    UART_ERROR_INVALID_PARAM = -1,  /**< 无效参数 */
    UART_ERROR_DEVICE_ERROR = -2,   /**< 设备错误 */
    UART_ERROR_SEND_FAILED = -3,    /**< 发送失败 */
    UART_ERROR_MEMORY = -4,         /**< 内存错误 */
    UART_ERROR_ALREADY_INIT = -5,   /**< 已初始化 */
    UART_ERROR_NOT_INIT = -6        /**< 未初始化 */
} uart_error_t;

/**
 * @brief 初始化UART协议栈
 * @param device_path 串口设备路径，如"/dev/ttyS0"
 * @param baudrate 波特率
 * @param callback 消息回调函数
 * @param user_data 用户数据指针
 * @return 成功返回UART协议句柄，失败返回NULL
 */
uart_protocol_handle_t uart_protocol_init(const char *device_path, int baudrate,
                                          uart_message_callback_t callback, void *user_data);

/**
 * @brief 销毁UART协议栈
 * @param handle UART协议句柄
 */
void uart_protocol_deinit(uart_protocol_handle_t handle);

/**
 * @brief 处理接收到的数据
 * @param handle UART协议句柄
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 * 注：该函数应在接收线程中调用，定期检查并处理接收到的数据
 */
uart_error_t uart_protocol_process(uart_protocol_handle_t handle);

/**
 * @brief 计算校验和
 * @param data 数据缓冲区
 * @param length 数据长度
 * @return 校验和
 */
uint16_t uart_protocol_calc_checksum(const uint8_t *data, uint16_t length);

/**
 * @brief 发送电源同步消息
 * @param handle UART协议句柄
 * @param need_ack 是否需要应答
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_power_sync_msg(uart_protocol_handle_t handle, uint8_t need_ack);

/**
 * @brief 发送心跳包
 * @param handle UART协议句柄
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_heartbeat_msg(uart_protocol_handle_t handle);

/**
 * @brief 发送MCU版本号
 * @param handle UART协议句柄
 * @param version 版本字符串
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_mcu_version_msg(uart_protocol_handle_t handle, const char *version);

/**
 * @brief 发送车速信息
 * @param handle UART协议句柄
 * @param speed 车速值(km/h)
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_speed_msg(uart_protocol_handle_t handle, uint8_t speed);

/**
 * @brief 发送转速信息
 * @param handle UART协议句柄
 * @param rpm 转速值(RPM)
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_rpm_msg(uart_protocol_handle_t handle, uint16_t rpm);

/**
 * @brief 发送挡位信息
 * @param handle UART协议句柄
 * @param gear 挡位值
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_gear_msg(uart_protocol_handle_t handle, uint8_t gear);

/**
 * @brief 发送灯光控制命令
 * @param handle UART协议句柄
 * @param need_ack 是否需要应答
 * @param ctrl 控制值(参见light_control_cmd_t定义)
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_light_ctrl_msg(uart_protocol_handle_t handle, uint8_t need_ack, light_control_cmd_t ctrl);

/**
 * @brief 发送剩余续航里程
 * @param handle UART协议句柄
 * @param range 续航里程(km)
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_remain_range_msg(uart_protocol_handle_t handle, uint8_t range);

/**
 * @brief 发送里程信息
 * @param handle UART协议句柄
 * @param total_mileage 总里程(km)
 * @param trip_mileage 小计里程(km)
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_mileage_msg(uart_protocol_handle_t handle, uint32_t total_mileage, uint32_t trip_mileage);

/**
 * @brief 发送工厂复位命令
 * @param handle UART协议句柄
 * @param need_ack 是否需要应答
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_factory_reset_msg(uart_protocol_handle_t handle, uint8_t need_ack);

/**
 * @brief 发送灯光和屏幕控制命令
 * @param handle UART协议句柄
 * @param need_ack 是否需要应答
 * @param light_ctrl 灯光控制值(参见light_control_cmd_t定义)
 * @param screen_ctrl 屏幕控制值(参见screen_control_cmd_t定义)
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_light_screen_ctrl_msg(uart_protocol_handle_t handle, uint8_t need_ack, 
                                                     light_control_cmd_t light_ctrl, screen_control_cmd_t screen_ctrl);

/**
 * @brief 发送时间信息
 * @param handle UART协议句柄
 * @param year 年(从2000年开始)
 * @param month 月
 * @param day 日
 * @param hour 时
 * @param minute 分
 * @param second 秒
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_time_msg(uart_protocol_handle_t handle, uint8_t year, uint8_t month, uint8_t day,
                                        uint8_t hour, uint8_t minute, uint8_t second);

/**
 * @brief 发送中控控制器状态信息(原始数据版本)
 * @param handle UART协议句柄
 * @param controller_data 中控控制器状态数据(18字节)
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_controller_status_msg(uart_protocol_handle_t handle, const uint8_t *controller_data);

/**
 * @brief 发送中控控制器状态信息(结构体版本)
 * @param handle UART协议句柄
 * @param status 中控控制器状态结构体指针(18字节)
 * @return 成功返回UART_ERROR_NONE，失败返回对应错误码
 */
uart_error_t uart_protocol_send_controller_status_struct_msg(uart_protocol_handle_t handle, const void *status);

#ifdef __cplusplus
}
#endif

#endif /* UART_API_H */