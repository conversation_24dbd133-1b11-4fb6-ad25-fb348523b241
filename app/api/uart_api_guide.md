# UART API 使用指南

## 概述

UART API 提供了一套简单易用的接口，用于与设备通过串口进行通信，而无需了解底层协议的细节。该 API 使用不透明指针技术，将协议的实现细节完全隐藏在内部，仅向外提供友好的接口。

## 主要特性

- 简单的初始化和清理函数
- 基于回调的消息处理机制
- 完整的消息发送接口
- 线程安全的设计
- 自动处理协议的帧格式和校验

## 快速入门

### 1. 包含头文件

```c
#include "app/include/uart_api.h"
```

### 2. 定义消息回调函数

```c
void message_handler(uint8_t id, const uint8_t *data, uint16_t data_len, void *user_data)
{
    printf("收到消息: ID=0x%02X, 长度=%u\n", id, data_len);
    
    // 根据消息类型处理
    switch (id) {
        case UART_MSG_SPEED:
            printf("车速: %u km/h\n", data[0]);
            break;
        // 其他消息类型...
    }
}
```

### 3. 初始化 UART 协议

```c
// 初始化 UART 协议，连接到设备，并注册回调
uart_protocol_handle_t handle = uart_protocol_init("/dev/ttyS1", 115200, message_handler, NULL);
if (!handle) {
    printf("初始化失败\n");
    return -1;
}
```

### 4. 发送消息

```c
// 发送车速信息
uart_protocol_send_speed_msg(handle, 30);

// 发送灯光控制命令
uart_protocol_send_light_ctrl_msg(handle, 1, LIGHT_CTRL_HEADLIGHT_ON);

// 发送转速信息
uart_protocol_send_rpm_msg(handle, 3500);
```

### 5. 处理接收数据

在主循环中调用处理函数：

```c
while (running) {
    // 处理接收到的数据
    uart_protocol_process(handle);
    
    // 避免占用过多 CPU
    usleep(10000); // 10ms
}
```

### 6. 清理资源

```c
// 释放资源
uart_protocol_deinit(handle);
```

## API 参考

### 数据类型

#### uart_protocol_handle_t

UART 协议句柄，用于所有操作。

#### uart_message_id_t

消息类型枚举，定义了所有支持的消息类型。

#### light_control_cmd_t

灯光控制命令枚举，定义了所有支持的灯光控制命令。

#### screen_control_cmd_t

屏幕控制命令枚举，定义了所有支持的屏幕控制命令。

#### uart_error_t

错误代码枚举，定义了所有可能的错误类型。

### 函数

#### 初始化与清理

- `uart_protocol_handle_t uart_protocol_init(const char *device_path, int baudrate, uart_message_callback_t callback, void *user_data)`：初始化 UART 协议栈。
- `void uart_protocol_deinit(uart_protocol_handle_t handle)`：销毁 UART 协议栈。
- `uart_error_t uart_protocol_process(uart_protocol_handle_t handle)`：处理接收到的数据。

#### 消息发送

- `uart_error_t uart_protocol_send_power_sync_msg(uart_protocol_handle_t handle, uint8_t need_ack)`：发送电源同步消息。
- `uart_error_t uart_protocol_send_heartbeat_msg(uart_protocol_handle_t handle)`：发送心跳包。
- `uart_error_t uart_protocol_send_mcu_version_msg(uart_protocol_handle_t handle, const char *version)`：发送 MCU 版本号。
- `uart_error_t uart_protocol_send_speed_msg(uart_protocol_handle_t handle, uint8_t speed)`：发送车速信息。
- `uart_error_t uart_protocol_send_rpm_msg(uart_protocol_handle_t handle, uint16_t rpm)`：发送转速信息。
- `uart_error_t uart_protocol_send_gear_msg(uart_protocol_handle_t handle, uint8_t gear)`：发送挡位信息。
- `uart_error_t uart_protocol_send_light_ctrl_msg(uart_protocol_handle_t handle, uint8_t need_ack, light_control_cmd_t ctrl)`：发送灯光控制命令。
- `uart_error_t uart_protocol_send_remain_range_msg(uart_protocol_handle_t handle, uint8_t range)`：发送剩余续航里程。
- `uart_error_t uart_protocol_send_mileage_msg(uart_protocol_handle_t handle, uint32_t total_mileage, uint32_t trip_mileage)`：发送里程信息。
- `uart_error_t uart_protocol_send_factory_reset_msg(uart_protocol_handle_t handle, uint8_t need_ack)`：发送工厂复位命令。
- `uart_error_t uart_protocol_send_light_screen_ctrl_msg(uart_protocol_handle_t handle, uint8_t need_ack, light_control_cmd_t light_ctrl, screen_control_cmd_t screen_ctrl)`：发送灯光和屏幕控制命令。
- `uart_error_t uart_protocol_send_time_msg(uart_protocol_handle_t handle, uint8_t year, uint8_t month, uint8_t day, uint8_t hour, uint8_t minute, uint8_t second)`：发送时间信息。

## 注意事项

1. 必须调用 `uart_protocol_init()` 初始化协议栈后才能使用其他功能。
2. 不要直接访问句柄内部的数据结构，这些结构是不透明的，仅供内部使用。
3. 在多线程环境中使用时，API 函数已经内部实现了线程安全。
4. 所有消息发送函数返回 `UART_ERROR_NONE` 表示成功，其他错误码表示失败。
5. 在程序结束前必须调用 `uart_protocol_deinit()` 释放资源。 