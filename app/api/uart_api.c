/**
 * @file uart_api.c
 * @brief UART通信协议公共API实现
 * @version 1.0.1
 * @date 2025-07-30 17:19
 */

#include "uart_api.h"
#include "uart_protocol.h"
#include "serial_utils.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h>

/**
 * @brief UART协议上下文结构
 */
struct uart_protocol_context {
    int fd;                         /**< 串口文件描述符 */
    serial_handle_t *serial;        /**< 串口句柄 */
    uart_message_callback_t callback; /**< 消息回调函数 */
    void *user_data;                /**< 用户数据指针 */
    pthread_t rx_thread;            /**< 接收线程 */
    int running;                    /**< 运行标志 */
    pthread_mutex_t mutex;          /**< 互斥锁 */
    uint8_t rx_buffer[1024];        /**< 接收缓冲区 */
    int rx_buffer_len;              /**< 接收缓冲区数据长度 */
};

/**
 * @brief 帧解析回调函数
 */
static void frame_callback_wrapper(uint8_t id, uint8_t ack, const uint8_t *data, uint8_t data_len, void *user_data)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)user_data;
    if (ctx && ctx->callback) {
        ctx->callback(id, data, data_len, ctx->user_data);
    }
}

/**
 * @brief 接收线程函数
 */
static void *rx_thread_func(void *arg)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)arg;
    uint8_t buffer[1024];
    
    while (ctx->running) {
        // 读取串口数据
        int read_len = 0;
        serial_error_t ret = serial_read(ctx->serial, (char *)buffer, sizeof(buffer), &read_len, 100);
        
        if (ret == SERIAL_ERROR_NONE && read_len > 0) {
            // 解析数据帧
            uart_protocol_parse_frame(buffer, read_len, frame_callback_wrapper, ctx);
        }
        
        // 短暂休眠，避免CPU占用过高
        usleep(10000); // 10ms
    }
    
    return NULL;
}

/**
 * @brief 初始化UART协议栈
 */
uart_protocol_handle_t uart_protocol_init(const char *device_path, int baudrate,
                                        uart_message_callback_t callback, void *user_data)
{
    if (!device_path || !callback) {
        return NULL;
    }
    
    // 分配上下文结构
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)malloc(sizeof(struct uart_protocol_context));
    if (!ctx) {
        return NULL;
    }
    
    // 初始化上下文
    memset(ctx, 0, sizeof(struct uart_protocol_context));
    ctx->callback = callback;
    ctx->user_data = user_data;
    
    // 初始化互斥锁
    if (pthread_mutex_init(&ctx->mutex, NULL) != 0) {
        free(ctx);
        return NULL;
    }
    
    // 打开串口
    serial_error_t ret = serial_init(device_path, baudrate, 8, 'N', 1, 0, &ctx->serial);
    if (ret != SERIAL_ERROR_NONE || !ctx->serial) {
        pthread_mutex_destroy(&ctx->mutex);
        free(ctx);
        return NULL;
    }
    
    ctx->fd = ctx->serial->fd;
    
    // 启动接收线程
    ctx->running = 1;
    if (pthread_create(&ctx->rx_thread, NULL, rx_thread_func, ctx) != 0) {
        serial_close(ctx->serial);
        pthread_mutex_destroy(&ctx->mutex);
        free(ctx);
        return NULL;
    }
    
    return ctx;
}

/**
 * @brief 销毁UART协议栈
 */
void uart_protocol_deinit(uart_protocol_handle_t handle)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return;
    }
    
    // 停止接收线程
    ctx->running = 0;
    pthread_join(ctx->rx_thread, NULL);
    
    // 关闭串口
    serial_close(ctx->serial);
    
    // 销毁互斥锁
    pthread_mutex_destroy(&ctx->mutex);
    
    // 释放上下文
    free(ctx);
}

/**
 * @brief 处理接收到的数据
 */
uart_error_t uart_protocol_process(uart_protocol_handle_t handle)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    // 接收线程会自动处理数据，这里不需要额外操作
    return UART_ERROR_NONE;
}

/**
 * @brief 计算校验和
 */
uint16_t uart_protocol_calc_checksum(const uint8_t *data, uint16_t length)
{
    return uart_protocol_calculate_checksum(data, length);
}

/**
 * @brief 发送电源同步消息
 */
uart_error_t uart_protocol_send_power_sync_msg(uart_protocol_handle_t handle, uint8_t need_ack)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_power_sync(ctx->serial, need_ack);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送心跳包
 */
uart_error_t uart_protocol_send_heartbeat_msg(uart_protocol_handle_t handle)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_heartbeat(ctx->serial);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送MCU版本号
 */
uart_error_t uart_protocol_send_mcu_version_msg(uart_protocol_handle_t handle, const char *version)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx || !version) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_mcu_version(ctx->serial, version);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送车速信息
 */
uart_error_t uart_protocol_send_speed_msg(uart_protocol_handle_t handle, uint8_t speed)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_speed(ctx->serial, speed);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送转速信息
 */
uart_error_t uart_protocol_send_rpm_msg(uart_protocol_handle_t handle, uint16_t rpm)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_rpm(ctx->serial, rpm);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送挡位信息
 */
uart_error_t uart_protocol_send_gear_msg(uart_protocol_handle_t handle, uint8_t gear)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_gear(ctx->serial, gear);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送灯光控制命令
 */
uart_error_t uart_protocol_send_light_ctrl_msg(uart_protocol_handle_t handle, uint8_t need_ack, light_control_cmd_t ctrl)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_light_ctrl(ctx->serial, need_ack, (uint8_t)ctrl);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送剩余续航里程
 */
uart_error_t uart_protocol_send_remain_range_msg(uart_protocol_handle_t handle, uint8_t range)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_remain_range(ctx->serial, range);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送里程信息
 */
uart_error_t uart_protocol_send_mileage_msg(uart_protocol_handle_t handle, uint32_t total_mileage, uint32_t trip_mileage)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_mileage(ctx->serial, total_mileage, trip_mileage);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送工厂复位命令
 */
uart_error_t uart_protocol_send_factory_reset_msg(uart_protocol_handle_t handle, uint8_t need_ack)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_factory_reset(ctx->serial, need_ack);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送灯光和屏幕控制命令
 */
uart_error_t uart_protocol_send_light_screen_ctrl_msg(uart_protocol_handle_t handle, uint8_t need_ack, 
                                                     light_control_cmd_t light_ctrl, screen_control_cmd_t screen_ctrl)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_light_screen_ctrl(ctx->serial, need_ack, (uint8_t)light_ctrl, (uint8_t)screen_ctrl);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送时间信息
 */
uart_error_t uart_protocol_send_time_msg(uart_protocol_handle_t handle, uint8_t year, uint8_t month, uint8_t day,
                                        uint8_t hour, uint8_t minute, uint8_t second)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx) {
        return UART_ERROR_INVALID_PARAM;
    }
    
    int ret = uart_protocol_send_time(ctx->serial, year, month, day, hour, minute, second);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送中控控制器状态信息(原始数据版本)
 */
uart_error_t uart_protocol_send_controller_status_msg(uart_protocol_handle_t handle, const uint8_t *controller_data)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx || !controller_data) {
        return UART_ERROR_INVALID_PARAM;
    }

    int ret = uart_protocol_send_controller_status(ctx->serial, controller_data);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}

/**
 * @brief 发送中控控制器状态信息(结构体版本)
 */
uart_error_t uart_protocol_send_controller_status_struct_msg(uart_protocol_handle_t handle, const void *status)
{
    struct uart_protocol_context *ctx = (struct uart_protocol_context *)handle;
    if (!ctx || !status) {
        return UART_ERROR_INVALID_PARAM;
    }

    int ret = uart_protocol_send_controller_status_struct(ctx->serial, status);
    return (ret > 0) ? UART_ERROR_NONE : UART_ERROR_SEND_FAILED;
}