/**
 * @file plug_hicar.h
 * @brief HiCar插件头文件（精简版）
 * @date 2025-07-30 15:05 GMT+8 (重构: 2025-07-30 16:50 GMT+8)
 * <AUTHOR> (全栈开发者)
 * @note 精简重构版本，移除app_event依赖
 */

#ifndef PLUG_HICAR_H
#define PLUG_HICAR_H

#ifdef __cplusplus
extern "C" {
#endif

// HiCar命令枚举（精简版）
typedef enum {
    VC_CMD_LIGHT_ON = 1,
    VC_CMD_LIGHT_OFF = 2,
    VC_CMD_LTS_ON = 3,      // 左转向灯开启
    VC_CMD_RTS_ON = 4,      // 右转向灯开启
    VC_CMD_TS_OFF = 5,      // 转向灯关闭
    VC_CMD_FLASH_ON = 6,    // 双闪开启
    VC_CMD_FLASH_OFF = 7,   // 双闪关闭
    VC_CMD_SCREEN_ON = 8,   // 屏幕开启
    VC_CMD_SCREEN_OFF = 9,  // 屏幕关闭
} voice_ctrl_command_t;

// 状态报告回调函数类型
typedef void (*car_state_callback_t)(voice_ctrl_command_t state);

/**
 * @brief 初始化HiCar插件
 * @return 0:成功 其他:失败
 */
int plug_hicar_init(void);

/**
 * @brief 清理HiCar插件
 * @return 0:成功 其他:失败
 */
int plug_hicar_deinit(void);

/**
 * @brief 注册状态报告回调函数
 * @param callback 回调函数指针
 * @return 0:成功 其他:失败
 */
int plug_hicar_register_callback(car_state_callback_t callback);

/**
 * @brief 发送语音控制命令
 * @param cmd 命令类型
 * @return 0:成功 其他:失败
 */
int plug_hicar_send_command(voice_ctrl_command_t cmd);

/**
 * @brief 获取最后一个命令
 * @return 最后一个命令
 */
voice_ctrl_command_t plug_hicar_get_last_command(void);

/**
 * @brief 检查插件是否已初始化
 * @return 1:已初始化 0:未初始化
 */
int plug_hicar_is_initialized(void);

/**
 * @brief 测试HiCar功能
 */
void plug_hicar_test(void);

#ifdef __cplusplus
}
#endif

#endif /* PLUG_HICAR_H */
