/**
 * @file plug_uart.c
 * @brief UART插件实现，基于event_bus事件系统和uart_api
 * @date 2025-07-30 14:45 GMT+8 (重构: 2025-07-30 18:00 GMT+8)
 * <AUTHOR> (全栈开发者)
 * @note 使用app/api/uart_api.c提供的完整UART协议栈
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include "plugin.h"
#include "plug_uart.h"

// event_bus事件系统
#include "event_bus/include/eb_core.h"
#include "event_bus/include/eb_types.h"

// UART API
#include "../api/uart_api.h"

// UART插件私有数据（使用uart_api）
typedef struct {
    uart_protocol_handle_t handle;  // UART协议句柄
    int initialized;                // 初始化标志
    char device_path[64];           // 设备路径
    int baud_rate;                  // 波特率
} uart_plugin_private_t;

static uart_plugin_private_t g_uart_plugin = {0};

// UART数据帧结构（与uart_api兼容）
typedef struct {
    uint8_t id;
    uint8_t data[256];              // 使用标准缓冲区大小
    uint16_t data_len;
} uart_frame_data_t;

// UART消息回调函数（使用uart_api的回调机制）
static void uart_message_callback(uint8_t id, const uint8_t *data, uint16_t data_len, void *user_data) {
    // 检查参数有效性
    if (data_len > 0 && !data) {
        PLUG_UART_DEBUG_PRINT("UART消息回调：无效参数，数据为NULL但长度为%d", data_len);
        return;
    }

    PLUG_UART_DEBUG_PRINT("UART消息回调：ID=0x%02X，长度=%d", id, data_len);

    // 根据不同的命令ID设置不同的事件类型
    uint16_t event_id;
    switch (id) {
        case UART_MSG_GEAR:                         // 挡位也归类为速度信息
        case UART_MSG_REMAIN_RANGE:                 // 剩余续航归类为车辆状态
        case UART_MSG_CONTROLLER_STATUS:            // 中控控制器状态归类为车辆状态
            event_id = EVENT_BIZ_VEHICLE_STATUS; 
            break;
        case UART_MSG_SPEED:                        // 车速归类为速度信息
        case UART_MSG_RPM:                          // 转速也归类为速度信息
            event_id = EVENT_BIZ_SPEED_INFO;     
            break;
        case UART_MSG_LIGHT_SCREEN_CTRL:              // 灯光控制事件
            event_id = EVENT_BIZ_LIGHT_CONTROL;  
            break;
        case UART_MSG_MILEAGE:                        // 里程数据事件
            event_id = EVENT_BIZ_MILEAGE_DATA;   
            break;
        case UART_MSG_TIME:                           // 时间数据事件
            event_id = EVENT_BIZ_TIME_DATA;      
            break;
        case UART_MSG_POWER_SYNC:
        case UART_MSG_HEARTBEAT:
        case UART_MSG_ACK:
        default:
            event_id = EVENT_COMM_DATA_RECEIVED;
            break;
    }

    // 创建事件数据结构体
    uart_frame_data_t *frame_data = malloc(sizeof(uart_frame_data_t));
    if (!frame_data) {
        PLUG_UART_DEBUG_PRINT("UART消息回调：内存分配失败");
        return;
    }

    // 初始化结构体，避免未初始化的内存
    memset(frame_data, 0, sizeof(uart_frame_data_t));

    frame_data->id = id;
    frame_data->data_len = data_len;

    if (data && data_len > 0) {
        // 确保不会越界
        if (data_len > sizeof(frame_data->data)) {
            data_len = sizeof(frame_data->data);
            frame_data->data_len = data_len;
            PLUG_UART_DEBUG_PRINT("UART消息回调：数据长度过长，已截断至%d字节", data_len);
        }
        memcpy(frame_data->data, data, data_len);

        // 专门解析0x67中控控制器状态数据
        if (id == UART_MSG_CONTROLLER_STATUS && data_len >= 18) {
            PLUG_UART_DEBUG_PRINT("=== 中控控制器状态数据解析 ===");
            // 解析控制器状态1 (Data0)
            uint8_t status1 = data[0];
            PLUG_UART_DEBUG_PRINT("控制器状态1: 0x%02X", status1);
            PLUG_UART_DEBUG_PRINT("================================");
        }
    }

    // 使用event_bus事件系统发布事件
    bool ret = eb_publish(event_id, frame_data);
    if (!ret) {
        PLUG_UART_DEBUG_PRINT("事件发布失败：事件ID=0x%04X, UART ID=0x%02X", event_id, id);
        free(frame_data);
    } else {
        // 打印数据内容
        char all_data[sizeof(data) * 5 + 1] = {0};
        for (size_t i = 0; i < data_len && i < sizeof(data); ++i) {
            snprintf(&all_data[i * 5], sizeof(all_data) - i * 5, " 0x%02X", data[i]);
        }
        PLUG_UART_DEBUG_PRINT("事件已发布：事件ID=0x%04X, UART ID=0x%02X ,数据长度=%d:%d, 数据=%s", event_id, id, data_len,sizeof(data) , all_data);
    }
}

// UART插件内部初始化函数
static void uart_plugin_init(void) {
    PLUG_UART_DEBUG_PRINT("UART插件内部初始化");
    // 内部初始化逻辑已在plug_uart_init中完成
}

// UART插件内部清理函数
static void uart_plugin_deinit(void) {
    PLUG_UART_DEBUG_PRINT("UART插件内部清理");

    if (!g_uart_plugin.initialized) {
        return;
    }

    // 清理UART协议栈
    if (g_uart_plugin.handle) {
        uart_protocol_deinit(g_uart_plugin.handle);
        g_uart_plugin.handle = NULL;
    }

    g_uart_plugin.initialized = 0;
}

// 定时处理函数（使用uart_api）
static void uart_plugin_process(void) {
    if (!g_uart_plugin.initialized || !g_uart_plugin.handle) {
        return;
    }

    // 处理UART协议栈
    uart_protocol_process(g_uart_plugin.handle);

    // 定期发送心跳包
    static int heartbeat_counter = 0;
    heartbeat_counter++;

    if (heartbeat_counter >= 100) { // 每100次调用发送一次心跳
        uart_protocol_send_heartbeat_msg(g_uart_plugin.handle);
        heartbeat_counter = 0;
    }
}

// 简化的UART初始化函数
int plug_uart_init(const char *device, int baud_rate) {
    PLUG_UART_DEBUG_PRINT("UART插件初始化：设备=%s, 波特率=%d", device, baud_rate);

    if (!device || baud_rate <= 0) {
        PLUG_UART_DEBUG_PRINT("无效参数");
        return -1;
    }

    // 如果已经初始化，先清理
    if (g_uart_plugin.initialized) {
        plug_uart_deinit();
    }

    // 保存配置
    strncpy(g_uart_plugin.device_path, device, sizeof(g_uart_plugin.device_path) - 1);
    g_uart_plugin.baud_rate = baud_rate;

    // 检查设备文件是否存在
    if (access(device, F_OK) != 0) {
        PLUG_UART_DEBUG_PRINT("设备文件不存在: %s，使用模拟模式", device);
        // 在模拟模式下，我们仍然注册插件，但不初始化实际的UART
        g_uart_plugin.handle = NULL;
        g_uart_plugin.initialized = 1;

        // 注册插件到event_bus事件系统
        if (!eb_plug_register("uart", uart_plugin_init, uart_plugin_deinit)) {
            PLUG_UART_DEBUG_PRINT("UART插件注册失败");
            g_uart_plugin.initialized = 0;
            return -1;
        }

        PLUG_UART_DEBUG_PRINT("UART插件初始化完成（模拟模式）");
        return 0;
    }

    // 初始化UART协议栈
    PLUG_UART_DEBUG_PRINT("正在初始化UART协议栈...");
    fflush(stdout);
    g_uart_plugin.handle = uart_protocol_init(device, baud_rate, uart_message_callback, NULL);
    if (!g_uart_plugin.handle) {
        PLUG_UART_DEBUG_PRINT("UART协议栈初始化失败，使用模拟模式");
        fflush(stdout);
        // 在模拟模式下，我们仍然注册插件，但不初始化实际的UART
        g_uart_plugin.handle = NULL;
        g_uart_plugin.initialized = 1;

        // 注册插件到event_bus事件系统
        if (!eb_plug_register("uart", uart_plugin_init, uart_plugin_deinit)) {
            PLUG_UART_DEBUG_PRINT("UART插件注册失败");
            g_uart_plugin.initialized = 0;
            return -1;
        }

        PLUG_UART_DEBUG_PRINT("UART插件初始化完成（模拟模式）");
        fflush(stdout);
        return 0;
    }
    PLUG_UART_DEBUG_PRINT("UART协议栈初始化成功");
    fflush(stdout);

    g_uart_plugin.initialized = 1;

    // 注册插件到event_bus事件系统
    if (!eb_plug_register("uart", uart_plugin_init, uart_plugin_deinit)) {
        PLUG_UART_DEBUG_PRINT("UART插件注册失败");
        uart_protocol_deinit(g_uart_plugin.handle);
        g_uart_plugin.handle = NULL;
        g_uart_plugin.initialized = 0;
        return -1;
    }

    // 发送上电同步信息
    PLUG_UART_DEBUG_PRINT("准备发送上电同步信息");
    fflush(stdout);

    uart_error_t ret = uart_protocol_send_power_sync_msg(g_uart_plugin.handle, 0);
    PLUG_UART_DEBUG_PRINT("上电同步信息发送调用完成，返回值: %d", ret);
    fflush(stdout);

    if (ret != UART_ERROR_NONE) {
        PLUG_UART_DEBUG_PRINT("上电同步信息发送失败: %d", ret);
        fflush(stdout);
    } else {
        PLUG_UART_DEBUG_PRINT("上电同步信息发送成功");
        fflush(stdout);
    }

    PLUG_UART_DEBUG_PRINT("UART插件初始化完成");
    fflush(stdout);
    return 0;
}

// UART发送函数（使用uart_api）
int plug_uart_send(uint8_t id, uint8_t need_ack, const uint8_t *data, uint8_t data_len) {
    PLUG_UART_DEBUG_PRINT("UART发送数据: ID=0x%02X, 长度=%d", id, data_len);

    if (!g_uart_plugin.initialized || !g_uart_plugin.handle) {
        PLUG_UART_DEBUG_PRINT("UART插件未初始化");
        return -1;
    }

    // 检查数据有效性
    if (data_len > 0 && !data) {
        PLUG_UART_DEBUG_PRINT("无效参数: 数据为NULL但长度为%d", data_len);
        return -1;
    }

    // 根据不同的ID调用不同的API函数
    uart_error_t ret = UART_ERROR_NONE;

    switch (id) {
        case UART_MSG_POWER_SYNC:
            PLUG_UART_DEBUG_PRINT("发送上电同步消息");
            ret = uart_protocol_send_power_sync_msg(g_uart_plugin.handle, need_ack);
            break;
        case UART_MSG_HEARTBEAT:
            PLUG_UART_DEBUG_PRINT("发送心跳包");
            ret = uart_protocol_send_heartbeat_msg(g_uart_plugin.handle);
            break;
        case UART_MSG_GEAR:
            if (data && data_len > 0) {
                PLUG_UART_DEBUG_PRINT("发送挡位消息: %d", data[0]);
                ret = uart_protocol_send_gear_msg(g_uart_plugin.handle, data[0]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_SPEED:
            if (data && data_len > 0) {
                PLUG_UART_DEBUG_PRINT("发送车速消息: %d km/h", data[0]);
                ret = uart_protocol_send_speed_msg(g_uart_plugin.handle, data[0]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_RPM:
            if (data && data_len >= 2) {
                uint16_t rpm = (data[0] << 8) | data[1];  // 高字节在前
                PLUG_UART_DEBUG_PRINT("发送转速消息: %d RPM", rpm);
                ret = uart_protocol_send_rpm_msg(g_uart_plugin.handle, rpm);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_LIGHT_SCREEN_CTRL:
            if (data && data_len > 0) {
                // 灯光控制参数必须存在
                light_control_cmd_t light_ctrl = (data_len >= 1) ? (light_control_cmd_t)data[0] : 0;
                // 屏幕控制参数可选
                screen_control_cmd_t screen_ctrl = (data_len >= 2) ? (screen_control_cmd_t)data[1] : 0;

                PLUG_UART_DEBUG_PRINT("发送灯光和屏幕控制消息: 灯光=%d, 屏幕=%d", light_ctrl, screen_ctrl);
                ret = uart_protocol_send_light_screen_ctrl_msg(g_uart_plugin.handle, need_ack,
                                                             light_ctrl, screen_ctrl);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_REMAIN_RANGE:
            if (data && data_len > 0) {
                PLUG_UART_DEBUG_PRINT("发送剩余续航里程消息: %d km", data[0]);
                ret = uart_protocol_send_remain_range_msg(g_uart_plugin.handle, data[0]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_FACTORY_RESET:
            PLUG_UART_DEBUG_PRINT("发送工厂复位消息");
            ret = uart_protocol_send_factory_reset_msg(g_uart_plugin.handle, need_ack);
            break;
        case UART_MSG_MCU_VERSION:
            if (data && data_len > 0) {
                // 确保字符串以'\0'结尾
                char *version = (char *)malloc(data_len + 1);
                if (version) {
                    memcpy(version, data, data_len);
                    version[data_len] = '\0';
                    PLUG_UART_DEBUG_PRINT("发送MCU版本消息: %s", version);
                    ret = uart_protocol_send_mcu_version_msg(g_uart_plugin.handle, version);
                    free(version);
                } else {
                    ret = UART_ERROR_MEMORY;
                }
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        default:
            PLUG_UART_DEBUG_PRINT("未支持的消息ID: %02X", id);
            return -1;
    }

    PLUG_UART_DEBUG_PRINT("UART发送结果: %d", ret);
    return (ret == UART_ERROR_NONE) ? data_len : -1;
}

// 清理UART插件（使用uart_api）
int plug_uart_deinit(void) {
    PLUG_UART_DEBUG_PRINT("清理UART插件");

    if (!g_uart_plugin.initialized) {
        return 0;
    }

    // 卸载插件
    eb_plug_unregister("uart");

    // 清理UART协议栈
    if (g_uart_plugin.handle) {
        uart_protocol_deinit(g_uart_plugin.handle);
        g_uart_plugin.handle = NULL;
    }

    g_uart_plugin.initialized = 0;
    memset(&g_uart_plugin, 0, sizeof(g_uart_plugin));

    PLUG_UART_DEBUG_PRINT("UART插件已清理");
    return 0;
}

// 获取UART插件状态
int plug_uart_is_initialized(void) {
    return g_uart_plugin.initialized;
}

// 处理插件定时任务（供外部调用）
void plug_uart_process(void) {
    uart_plugin_process();
}
