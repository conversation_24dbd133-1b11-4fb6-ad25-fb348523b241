/**
 * @file plug_bridge.h
 * @brief UART-HiCar桥接插件头文件（精简版）
 * @date 2025-07-30 15:20 GMT+8 (重构: 2025-07-30 17:05 GMT+8)
 * <AUTHOR> (全栈开发者)
 * @note 精简重构版本，移除app_event依赖
 */

#ifndef PLUG_BRIDGE_H
#define PLUG_BRIDGE_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化UART-HiCar桥接插件
 * @return 0:成功 其他:失败
 */
int plug_bridge_init(void);

/**
 * @brief 清理UART-HiCar桥接插件
 * @return 0:成功 其他:失败
 */
int plug_bridge_deinit(void);

/**
 * @brief 检查桥接插件是否已初始化
 * @return 1:已初始化 0:未初始化
 */
int plug_bridge_is_initialized(void);

/**
 * @brief 获取桥接插件状态
 * @param left_turn 左转向灯状态
 * @param right_turn 右转向灯状态
 * @param headlight 大灯状态
 * @param screen 屏幕状态
 * @param flash 双闪状态
 */
void plug_bridge_get_state(uint8_t *left_turn, uint8_t *right_turn,
                          uint8_t *headlight, uint8_t *screen, uint8_t *flash);

#ifdef __cplusplus
}
#endif

#endif /* PLUG_BRIDGE_H */
