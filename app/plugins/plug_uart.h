/**
 * @file plug_uart.h
 * @brief UART插件头文件（精简版）
 * @date 2025-07-30 14:50 GMT+8 (重构: 2025-07-30 16:40 GMT+8)
 * <AUTHOR> (全栈开发者)
 * @note 精简重构版本，移除app_event依赖
 */

#ifndef PLUG_UART_H
#define PLUG_UART_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化UART插件
 * @param device 串口设备路径，如"/dev/ttyS0"
 * @param baud_rate 波特率
 * @return 0:成功 其他:失败
 */
int plug_uart_init(const char *device, int baud_rate);

/**
 * @brief 发送UART数据（简化版）
 * @param id 命令ID
 * @param need_ack 是否需要应答（保留兼容性，实际未使用）
 * @param data 数据指针
 * @param data_len 数据长度
 * @return 发送的字节数，错误返回负值
 */
int plug_uart_send(uint8_t id, uint8_t need_ack, const uint8_t *data, uint8_t data_len);

/**
 * @brief 清理UART插件
 * @return 0:成功 其他:失败
 */
int plug_uart_deinit(void);

/**
 * @brief 检查UART插件是否已初始化
 * @return 1:已初始化 0:未初始化
 */
int plug_uart_is_initialized(void);

/**
 * @brief 处理UART插件定时任务
 */
void plug_uart_process(void);

// =============================================================================
// 调试宏
// =============================================================================

#ifdef PLUG_UART_DEBUG || PLUG_DEBUG
#include <stdio.h>
#define PLUG_UART_DEBUG_PRINT(fmt, ...) \
    printf("[PLUG_UART_DEBUG] " fmt "\n", ##__VA_ARGS__)
#else
#define PLUG_UART_DEBUG_PRINT(fmt, ...)
#endif

#ifdef __cplusplus
}
#endif

#endif /* PLUG_UART_H */
