/**
 * @file plugin.h
 * @brief 插件系统公共头文件
 */

#ifndef PLUGIN_H
#define PLUGIN_H

#ifdef __cplusplus
extern "C" {
#endif

// 系统事件ID定义
#define EVENT_SYS_START            0x1000  // 系统启动
#define EVENT_COMM_DATA_RECEIVED   0x1001  // UART数据接收

// HICAR相关事件ID定义
#define EVENT_BIZ_HICAR_COMMAND     0x2001  // HiCar命令
#define EVENT_BIZ_LIGHT_CHANGED     0x2002  // 灯光状态变化
#define EVENT_BIZ_SCREEN_CONTROL    0x2003  // 屏幕控制
#define EVENT_BIZ_SCREEN_CONTROL    0x2003  // 屏幕控制
#define EVENT_HICAR_STATE_CHANGED   0x2004  // HiCar状态变化

// 车辆业务事件ID定义
#define EVENT_BIZ_VEHICLE_STATUS   0x3001  // 车辆状态
#define EVENT_BIZ_SPEED_INFO       0x3002  // 速度信息
#define EVENT_BIZ_LIGHT_CONTROL    0x3003  // 灯光控制事件
#define EVENT_BIZ_TIME_DATA        0x3004  // 时间数据事件
#define EVENT_BIZ_MILEAGE_DATA     0x3005  // 里程数据事件

#ifdef __cplusplus
}
#endif

#endif /* PLUGIN_H */
