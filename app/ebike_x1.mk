APP_DIR_NAME ?= app

CSRCS += $(wildcard $(LVGL_DIR)/$(APP_DIR_NAME)/*.c)
CSRCS += $(wildcard $(LVGL_DIR)/$(APP_DIR_NAME)/common/*/*.c)
CSRCS += $(wildcard $(LVGL_DIR)/$(APP_DIR_NAME)/components/*.c)
CSRCS += $(wildcard $(LVGL_DIR)/$(APP_DIR_NAME)/components/data/*.c)
CSRCS += $(wildcard $(LVGL_DIR)/$(APP_DIR_NAME)/src/*.c)
CSRCS += $(wildcard $(LVGL_DIR)/$(APP_DIR_NAME)/api/*.c)
CSRCS += $(wildcard $(LVGL_DIR)/$(APP_DIR_NAME)/plugins/*.c)
# 排除有main函数的演示文件
CSRCS += $(filter-out %screen_demo.c, $(wildcard $(LVGL_DIR)/$(APP_DIR_NAME)/utils/*.c))

CFLAGS += -I$(LVGL_DIR)/$(APP_DIR_NAME)
CFLAGS += -I$(LVGL_DIR)/$(APP_DIR_NAME)/api
CFLAGS += -I$(LVGL_DIR)/$(APP_DIR_NAME)/common/config
CFLAGS += -I$(LVGL_DIR)/$(APP_DIR_NAME)/common/fonts
CFLAGS += -I$(LVGL_DIR)/$(APP_DIR_NAME)/common/lang
CFLAGS += -I$(LVGL_DIR)/$(APP_DIR_NAME)/common/themes
CFLAGS += -I$(LVGL_DIR)/$(APP_DIR_NAME)/components
CFLAGS += -I$(LVGL_DIR)/$(APP_DIR_NAME)/components/data
CFLAGS += -I$(LVGL_DIR)/$(APP_DIR_NAME)/plugins
CFLAGS += -I$(LVGL_DIR)/$(APP_DIR_NAME)/utils

# APP入口
APP_CSRCS := $(APP_DIR_NAME)/ebike_x1.c