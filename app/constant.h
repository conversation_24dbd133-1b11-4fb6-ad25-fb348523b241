/**
 * @file constant.h
 * @brief 全局的常量定义
 */

#ifndef CONSTANT_H
#define CONSTANT_H

#include "plugin.h"

#ifdef __cplusplus
extern "C" {
#endif

// 事件ID定义（使用plugins.h定义）
// #define EVENT_COMM_DATA_RECEIVED    0x1001  // UART数据接收
// #define EVENT_SYS_START            0x1000  // 系统启动
// #define EVENT_BIZ_VEHICLE_STATUS   0x3001  // 车辆状态
// #define EVENT_BIZ_SPEED_INFO       0x3002  // 速度信息

// 新增数据变化事件
#define EVENT_DATA_LIGHT_CHANGED    0x4001  // 灯光数据变化
#define EVENT_DATA_BATTERY_CHANGED  0x4002  // 电池数据变化
#define EVENT_DATA_RUNNING_CHANGED  0x4003  // 运行数据变化
#define EVENT_DATA_ATTITUDE_CHANGED 0x4004  // 姿态数据变化
#define EVENT_DATA_SYSTEM_CHANGED   0x4005  // 系统数据变化
#define EVENT_DATA_TIME_CHANGED     0x4006  // 时间数据变化

// UART消息ID定义（与uart_api.c保持一致）
#define UART_MSG_POWER_SYNC         0x01   // 上电同步信息 
#define UART_MSG_UPGRADE            0x02   // 升级 
#define UART_MSG_HEARTBEAT          0x03   // 心跳包 
#define UART_MSG_FACTORY_RESET      0x04   // 工厂复位 
#define UART_MSG_MCU_VERSION        0x05   // MCU版本号 
#define UART_MSG_LIGHT_SCREEN_CTRL  0x56   // 灯光和屏幕控制 
#define UART_MSG_SCREEN_CTRL        0x80   // 屏幕控制(已弃用 使用#define UART_MSG_LIGHT_SCREEN_CTRL)   
#define UART_MSG_SET_TIME           0x81   // 设置时间 
#define UART_MSG_CLEAR_MILEAGE      0x82   // 清除里程 
#define UART_MSG_SET_TOTAL_MILE     0x83   // 设置总里程 
#define UART_MSG_SPEED              0x60   // 车速 
#define UART_MSG_RPM                0x61   // 转速 
#define UART_MSG_GEAR               0x62   // 挡位 
#define UART_MSG_LIGHT              0x63   // 灯光、警示灯(已弃用，使用#define UART_MSG_LIGHT_SCREEN_CTRL)  
#define UART_MSG_REMAIN_RANGE       0x64   // 剩余续航里程 
#define UART_MSG_MILEAGE            0x65   // 总里程和小计里程 
#define UART_MSG_TIME               0x66   // 时间 
#define UART_MSG_CONTROLLER_STATUS  0x67   // 中控控制器状态 
#define UART_MSG_ACK                0x55   // 应答位 

#ifdef __cplusplus
}
#endif

#endif /* CONSTANT_H */
