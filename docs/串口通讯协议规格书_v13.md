# 串口通讯协议规格书

## 1. 文档描述
本文档描述 **MCU** 与 **SOC** 串口通讯协议，涉及物理层、数据链路层和应用层协议。

## **● 版本管理：**
| 修改时间 | 版本号 | 修改者 | 修改内容 |
|----------|-----|--------|----------|
| 2025-05-20 | V01 | HYH | 初版 |
| 2025-06-13 | V10 | HYH | 修订 |
| 2025-06-14 | V11 | GYZ | 修订 |
| 2025-06-16 | V12 | HYH | 修订,ID 0X56 0位，修改0x04: 打开右转灯 添加 0x06: 打开车灯 移除单项控制ID 0x80，屏幕控制改为双向控制, ID 0X56 第1位，0x01: 关闭屏幕 0x02: 打开屏幕 |
| 2025-07-01 | V13 | HYH | 1. 修订ID 0x61 转速参数由1位修改2位并添加详细定义 2. 添加中控控制器ID 0x67 参数的定义 3. 更正校验和 data[length]说明 |

## 2. 物理层描述
采用标准 **UART** 通信接口，逻辑电平为 **5V TTL** 电平，**UART** 工作在 **8N1** 模式，即 **8** 位数据位，无奇偶校验位，一位停止位，波特率固定在 **115200bps**。

## 3. 链路层描述

**● 帧格式：**
每帧数据由以下部分组成（字节顺序为大端模式）：

| 字段 | 长度 | 说明 | 示例值 |
|------|------|------|--------|
| 帧头 | 2 | 固定值（0xAA、0xBB） | 0xAA、0xBB |
| 长度 | 1 | 后续字段的总长度（数据长度+4） | 0x05 |
| 是否需要应答位 | 1 | 需要应答：0x00 不需要应答：0x01 | 0x01 |
| ID | 1 | 参见下表 ID 定义 | 0x03 |
| 数据 | N | 可变长度参数（每个 data 都是无符号 8 位） | 0x01 |
| 校验和高位 | 1 | 参见下表校验和定义 | 0x0d |
| 校验和低位 | 1 | 参见下表校验和定义 | 0x05 |

**● ACK：**
当接收到需要应答时，接收方需要回应**ID** 为 **0x55** 的数据包，发送的数据为：

| 帧头 1 | 帧头 2 | 长度 | 是否需要应答位 | ID | 数据 | 校验和高位 | 校验和低位 |
|--------|--------|------|----------------|----|------|------------|------------|
| 0xAA | 0xBB | 0x05 | 需要：0x00 不需要：0x01 | 0x55 | 接收到的 ID 号 | 参见下表校验和定义 | 参见下表校验和定义 |

例如：SOC 发送 **0xAA 0xBB 0x05 0x01 0x03 0x01 checksum_H checksum_L **给 MCU，需要 MCU 应答，那么 MCU 应该发送 **0xAA 0xBB 0x05 0x00 0x55 0x03 checksum_H checksum_L**响应给 SOC。

**● 校验和：**
data[2+N] = {是否需要应答, ID, N位数据};
length = N(数据位长度) + 2

```c
/**
 * @brief 计算checksum
 * 
 * @param data 数据缓冲区 = {是否需要应答, ID, N(数据位长度)}
 * @param length N(数据位长度)+ 2
 * @return uint16_t checksum
 */
```c
uint16_t calculate_checksum(const uint8_t *data, uint16_t length) {
    uint32_t checksum = 0;
    for (uint16_t i = 0; i < length; i++) {
        checksum += data[i];
        checksum += ((checksum & 0xFF) << 8) + 0x100;
        checksum = (checksum ^ (checksum >> 16)) & 0xFFFF;
    }
    return (uint16_t)checksum;
}
```

## 4. 应用层

### 4.1 数据层定义（串口波特率 115200bps）

| 序号 | 定义描述 | ID | 备注 |
|------|----------|----|------|
| SOC -> MCU | | | |
| 1 | 上电同步信息 | 0x01 | 例如：同步时间、同步 MCU 版本号等 |
| 2 | 升级 | 0x02 | 版本升级 |
| 3 | 心跳包 | 0x03 | |
| 4 | 工厂复位 | 0x04 | |
| 5 | MCU 版本号 | 0x05 | |
| 5 | 屏幕控制 | 0x80 | |
| 6 | 设置时间 | 0x81 | |
| 7 | 清除里程 | 0x82 | |
| 8 | 设置总里程 | 0x83 | |
| MCU -> SOC | | | |
| 1 | 车速 | 0x60 | |
| 2 | 转速 | 0x61 | |
| 3 | 挡位 | 0x62 | |
| 4 | 灯光、警示灯 | 0x63 | 例如：屏幕上的左右灯光、油量不足的报警灯等 |
| 5 | 剩余续航里程 | 0x64 | |
| 6 | 总里程和小计里程 | 0x65 | |
| 7 | 时间 | 0x66 | |
| MCU <-> SOC | | | |
| 1 | 应答位 | 0x55 | 接收到的ID作为数据发回去 |

### 4.2 数据格式

**SOC -> MCU**

**上电同步信息**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x01 | |
| Length | 1 | |
| Data0 | 参数 | 0x01：同步 |

**升级**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x02 | |
| Length | 1 | |
| Data0 | 参数 | ① 0x00: 升级 SOC ② 0x01: 升级 MCU ③ 0x02: 升级 MCU 和 SOC ④ 0x03: 开始升级 SOC ⑤ 0x04: SOC 升级成功 |

**心跳包**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x03 |  |
| Length | 1 | |
| Data0 | 参数 | 0x01 |

**工厂复位**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x04 | |
| Length | 1 | |
| Data0 | 参数 | 0x01 |

**MCU 版本号**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x05 | |
| Length | 20 | |
| Data0~Data19 | 参数 | SOC 将收到的数据转为字符 |


**设置时间**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x81 | |
| Length | 6 | |
| Data0 | 年 | 从 2000 年开始算，例如 2025 年，则发送 0x19 |
| Data1 | 月 | |
| Data2 | 日 | |
| Data3 | 时 | |
| Data4 | 分 | |
| Data5 | 秒 | |

**清除里程**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x80 | |
| Length | 1 | |
| Data0 | 清除里程 | 0x01：清除 |

**设置总里程**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x83 | |
| Length | 4 | |
| Data0 | Bit24~Bit32 | |
| Data1 | Bit16~Bit23 | |
| Data2 | Bit8~Bit15 | |
| Data3 | Bit0~Bit7 | |

**MCU -> SOC**

**车速**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x60 | |
| Length | 2 | |
| Data0 | 0-255 | 收到数据/2 单位:km |

**转速**

| 数据顺序 | 数据内容 | 备注 |
|----------|---------|------|
| ID | 0x61 | |
| Length | 2 | |
| Data0 | Bit0~Bit7 |   |
| Data1 | Bit8~Bit15 | 获取到数据之后需要根据轮胎型号进行计算 |

**挡位**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x62 | |
| Length | 1 | |
| Data0 | 参数 | 0x00: 停车挡 0x01: 倒挡 0x02：空挡 0x03: 前进挡 0x04: 运动挡 0x05: 低速挡 |

**剩余续航里程**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x64 | |
| Length | 2 | |
| Data0 | 剩余续航里程 | 单位：km |

**总计里程和小计里程**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x65 | |
| Length | 8 | |
| Data0 | Bit24~Bit32 | 总计里程 |
| Data1 | Bit16~Bit23 | |
| Data2 | Bit8~Bit15 | |
| Data3 | Bit0~Bit7 | |
| Data4 | Bit24~Bit32 | 小计里程 |
| Data5 | Bit16~Bit23 | |
| Data6 | Bit8~Bit15 | |
| Data7 | Bit0~Bit7 | |

**时间**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x66 | |
| Length | 6 | |
| Data0 | 年 | 从 2000 年开始算，例如 2025 年，则发送 0x19 |
| Data1 | 月 | |
| Data2 | 日 | |
| Data3 | 时 | |
| Data4 | 分 | |
| Data5 | 秒 | |

**中控控制器**

| 数据顺序   | 数据内容   | 备注                                                       |
|--------|--------| ---------------------------------------------------------- |
| ID     | 0x67   |                                                            |
| Length | 18     |                                                            |
| Data0  | 控制器状态 1 | Bit0：一键修复<br/>Bit1: TCS 状态（ 0：未运行 1：运行 ）<br/>Bit2: 低电量延长续航状态（ 0：不在 1：在 ）<br/>Bit3: DSR 感应（ 0：无感 1：有感应 ）<br/>Bit4: 制动状态（ 0：未制动 1：制动 ）<br/>Bit5: P 挡信号（ 0：未 P 档 1：P 档 ）<br/>Bit6~Bit7: 工作模式 0x01: ECO 0x02:POWER 0x03:CRUISE |
| Data1  | 控制器状态 2 | Bit0：巡航状态（ 0：否 1：是 ）<br/>Bit1: 功能 1 状态（ 0：否 1：是 ）<br/>Bit2: 推车状态（ 0：否 1：是 ）<br/>Bit3: 倒车状态（ 0：否 1：是 ）<br/>Bit4: 超车（ 0：否 1：是 ）<br/>Bit5: 边撑（ 0：否 1：是 ）<br/>Bit6： 长按 P 挡（ 0：否 1：是 ）<br/>Bit7： 刹车故障（ 0：无故障 1：故障 ） |
| Data2  | 电池电压使用状态 | Bit0~Bit3: 电压选择 0x01: 36V 0x02: 48V 0x03: 60V 0x04: 72V 0x05: 84V 0x06: 96V<br/>Bit5： 双欠压选择（ 0：第一次欠点 1：第二次欠点 ）<br/>Bit6： 车型（ 0：电自 1：电摩 ）<br/>Bit7~Bit8： 保留 |
| Data3  | 控制器温度  | 偏移值：-40<br/>单位：1℃<br/>备注：-40~213 ℃“0XFE”表示异常“0XFF”表示无效 |
| Data4  | 母线电压\_L |                                      |
| Data5  | 母线电压\_H | Data4+Data5组合取值<br/>单位：0.1V<br/>备注：0 ～ 6553.3V “0xFFFE”表示异常“0xFFFF”表示无效 |
| Data6  | 母线电流\_L |                                                           |
| Data7  | 母线电流\_H | Data6+Data7组合取值<br/>偏移值：-100<br/>单位：0.1A<br/>备注：-100.1 ～ 100.1 A “0xFFFE”表示异常“0xFFFF”表示无效 |
| Data8  | 故障代码 1 | Bit0：高温保护（0：无 1：有 ）<br/>Bit1：堵转保护（0：无 1：有 ）<br/>Bit2：过流保护（0：无 1：有 ）<br/>Bit3：欠压保护（0：无 1：有 ）<br/>Bit4：转把故障（0：无 1：有 ）<br/>Bit5：电机霍尔故障（0：无 1：有 ）<br/>Bit6：电机缺相（0：无 1：有 ）<br/>Bit7：控制器故障（0：无 1：有 ） |
| Data9  | 故障代码 2 | 预留                                                       |
| Data10 | 铅酸 SOC | 1~100% 如不支持为 0xFF                                        |
| Data11 | 转把电压\_L |                                      |
| Data12 | 转把电压\_H | Data11+Data12组合取值<br/>单位：1mV<br/>范围：0 ~ 5000 |
| Data13 | 使能开关   | Bit0：坐垫感应开关（ 0：关闭 1：打开 ）<br/>Bit1：边撑感应开关（ 0：关闭 1：打开 ）<br/>Bit2：驾驶习惯开关（ 0：关闭 1：打开 ）<br/>Bit3：倒车功能开关（ 0：关闭 1：打开 ）<br/>Bit4：推车功能开关（ 0：关闭 1：打开 ）<br/>Bit5：低电量延长续航功能开关（ 0：关闭 1：打开 ）<br/>Bit6：软/硬启动（ 0：硬启动 1：软启动 ）<br/>Bit7：功能 1 时能（ 0：关闭 1：打开 ） |
| Data14 | 控制器请求  | Bit0 ~ Bit6：查询/设置子命令，0 表示无效<br/>Bit7：请求命令（ 0：查询 1：设置 ） |
| Data15 | 整车状态   | Bit0：充电检测 （ 0：无 1：充电中 ）<br/>Bit1: 电自超速提示音 （ 0：否 1：是 ）<br/>Bit2 ~ Bit3: 坡道辅助 0x00：未运行（默认）0x01：运行中（仪表显示 H）0x10：仪表计时(5s 后仪表显示倒计时 3/2/1S)<br/>Bit4：超车标识（ 0：未超车 1：超车 ）<br/>Bit5：DSR 乘人（ 0：无 1：有 ）<br/>Bit6： 飞车保护（ 0：无 1：触发 ）<br/>Bit7：边撑时解 P（ 0：无 1：触发 ） |
| Data16 | 体重     | 单位：kg                                                    |
| Data17 | 颠簸等级   | 0x01：平稳 0x02：轻微颠簸 0x03：严重颠簸                     |


**MCU <-> SOC**

**应答位**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x55 | |
| Length | 1 | |
| Data0 | 参数 | 接收到的 ID 号 |


**灯光、，屏幕控制**

| 数据顺序 | 数据内容 | 备注 |
|----------|----------|------|
| ID | 0x56 | |
| Length | N | |
| Data0 | 参数 | 0x01: 打开双闪  0x02: 关闭双闪 0x03: 打开左转向 0x04: 打开右转灯 0x05: 关闭转向 0x06: 打开车灯 |
| Data1 | 参数 | 0x01: 开启屏幕  0x02: 关闭屏幕 |

## 5. 上电与心跳包流程
```mermaid
graph TD
    SOC1(SOC启动) --> SOC2(上电同步信息<br>ID:0x01)
    SOC2 --> SOC3(心跳包<br>ID:0x03)
    SOC3 -->|3s发送一次| SOC3
    SOC3 --> SOC4(10s未发送)
    SOC4 --> SOC5(MCU控制机器重启)
```

## 6. 应答
```mermaid
sequenceDiagram
    participant SOC
    participant MCU
    
    SOC->>MCU: Cmd (Need to be asked)
    MCU->>SOC: ACK
    SOC->>SOC: Running
    SOC->>SOC: Wating for ACK
    SOC->>SOC: Not success 3times later
    SOC->>SOC: Report communication error
```

# 注意事项
- 注意每个数据长度，例如 data[0]和 data[1]都是无符号 8 位的，如果有负数的话，会做相应的偏- 移值，到时根据实际情况会在文档里补充说明。

- 数据长度也会根据实际情况做出相应改变，软件上要将数据长度做得灵活一些，方便后续改动。