# 24-双闪灯功能验证脚本

**时间**: 2025-01-27 14:50 GMT+8  
**作者**: <PERSON> (全栈开发者)  
**版本**: 1.0.0

## 验证脚本说明

本文档提供双闪灯功能的手动验证步骤和自动化测试脚本。

## 手动验证步骤

### 步骤1: 基本功能验证

#### 1.1 左转向灯测试
```bash
# 通过UART发送左转向灯开启命令
echo -e "\x01\x56\x00\x00\x01\x00\x00\x00\x00\x00\xAA\xBB" > /dev/ttyS0

# 预期结果: 左转向灯开始闪烁，右转向灯保持关闭
# 观察UI: home_light_left 应该以500ms周期闪烁
```

#### 1.2 右转向灯测试
```bash
# 通过UART发送右转向灯开启命令
echo -e "\x01\x56\x00\x00\x02\x00\x00\x00\x00\x00\xAA\xBB" > /dev/ttyS0

# 预期结果: 右转向灯开始闪烁，左转向灯关闭
# 观察UI: home_light_right 应该以500ms周期闪烁
```

#### 1.3 双闪灯测试
```bash
# 通过UART发送双闪开启命令
echo -e "\x01\x56\x00\x00\x03\x00\x00\x00\x00\x00\xAA\xBB" > /dev/ttyS0

# 预期结果: 左右转向灯都开始闪烁
# 观察UI: home_light_left 和 home_light_right 都应该同步闪烁
```

### 步骤2: 优先级验证

#### 2.1 双闪覆盖测试
```bash
# 1. 先开启左转向灯
echo -e "\x01\x56\x00\x00\x01\x00\x00\x00\x00\x00\xAA\xBB" > /dev/ttyS0
sleep 2

# 2. 开启双闪（应该覆盖左转）
echo -e "\x01\x56\x00\x00\x03\x00\x00\x00\x00\x00\xAA\xBB" > /dev/ttyS0
sleep 2

# 3. 关闭双闪（应该恢复左转状态）
echo -e "\x01\x56\x00\x00\x00\x00\x00\x00\x00\x00\xAA\xBB" > /dev/ttyS0

# 预期结果: 
# - 步骤1: 只有左转闪烁
# - 步骤2: 左右都闪烁
# - 步骤3: 恢复到只有左转闪烁
```

#### 2.2 状态独立性测试
```bash
# 1. 开启左转向灯
echo -e "\x01\x56\x00\x00\x01\x00\x00\x00\x00\x00\xAA\xBB" > /dev/ttyS0
sleep 1

# 2. 开启双闪
echo -e "\x01\x56\x00\x00\x03\x00\x00\x00\x00\x00\xAA\xBB" > /dev/ttyS0
sleep 1

# 3. 在双闪期间关闭左转（不应影响显示）
echo -e "\x01\x56\x00\x00\x00\x00\x00\x00\x00\x00\xAA\xBB" > /dev/ttyS0
sleep 1

# 4. 关闭双闪（应该显示真实状态：全部关闭）
echo -e "\x01\x56\x00\x00\x00\x00\x00\x00\x00\x00\xAA\xBB" > /dev/ttyS0

# 预期结果:
# - 步骤1-3: 左右都持续闪烁（双闪覆盖）
# - 步骤4: 全部关闭（反映真实状态）
```

## 自动化测试脚本

### 测试脚本: test_turn_signals.sh
```bash
#!/bin/bash

# 双闪灯功能自动化测试脚本
# 使用方法: ./test_turn_signals.sh

UART_DEVICE="/dev/ttyS0"
LOG_FILE="/tmp/turn_signal_test.log"

echo "开始双闪灯功能测试..." | tee $LOG_FILE
echo "测试时间: $(date)" | tee -a $LOG_FILE

# 测试函数
send_uart_command() {
    local cmd=$1
    local desc=$2
    echo "发送命令: $desc" | tee -a $LOG_FILE
    echo -e "$cmd" > $UART_DEVICE
    sleep 2
}

# 测试1: 基本功能
echo "=== 测试1: 基本功能 ===" | tee -a $LOG_FILE

send_uart_command "\x01\x56\x00\x00\x01\x00\x00\x00\x00\x00\xAA\xBB" "左转向灯开启"
send_uart_command "\x01\x56\x00\x00\x00\x00\x00\x00\x00\x00\xAA\xBB" "左转向灯关闭"

send_uart_command "\x01\x56\x00\x00\x02\x00\x00\x00\x00\x00\xAA\xBB" "右转向灯开启"
send_uart_command "\x01\x56\x00\x00\x00\x00\x00\x00\x00\x00\xAA\xBB" "右转向灯关闭"

send_uart_command "\x01\x56\x00\x00\x03\x00\x00\x00\x00\x00\xAA\xBB" "双闪开启"
send_uart_command "\x01\x56\x00\x00\x00\x00\x00\x00\x00\x00\xAA\xBB" "双闪关闭"

# 测试2: 优先级测试
echo "=== 测试2: 优先级测试 ===" | tee -a $LOG_FILE

send_uart_command "\x01\x56\x00\x00\x01\x00\x00\x00\x00\x00\xAA\xBB" "开启左转"
send_uart_command "\x01\x56\x00\x00\x03\x00\x00\x00\x00\x00\xAA\xBB" "开启双闪（覆盖左转）"
send_uart_command "\x01\x56\x00\x00\x00\x00\x00\x00\x00\x00\xAA\xBB" "关闭双闪（恢复左转）"
send_uart_command "\x01\x56\x00\x00\x00\x00\x00\x00\x00\x00\xAA\xBB" "关闭左转"

# 测试3: 状态独立性
echo "=== 测试3: 状态独立性 ===" | tee -a $LOG_FILE

send_uart_command "\x01\x56\x00\x00\x01\x00\x00\x00\x00\x00\xAA\xBB" "开启左转"
send_uart_command "\x01\x56\x00\x00\x03\x00\x00\x00\x00\x00\xAA\xBB" "开启双闪"
send_uart_command "\x01\x56\x00\x00\x00\x00\x00\x00\x00\x00\xAA\xBB" "关闭左转（双闪期间）"
send_uart_command "\x01\x56\x00\x00\x00\x00\x00\x00\x00\x00\xAA\xBB" "关闭双闪"

echo "测试完成！" | tee -a $LOG_FILE
echo "日志文件: $LOG_FILE"
```

## 验证检查点

### UI检查点
1. **左转向灯图标** (`home_light_left`)
   - 开启时: 以500ms周期闪烁
   - 关闭时: 完全隐藏

2. **右转向灯图标** (`home_light_right`)
   - 开启时: 以500ms周期闪烁
   - 关闭时: 完全隐藏

3. **双闪模式**
   - 左右转向灯同步闪烁
   - 闪烁频率一致（500ms周期）

### 日志检查点
观察控制台输出中的关键信息：

```
[DASHBOARD] 双闪模式: 左转=显示, 右转=显示
[DASHBOARD] 转向灯模式: 左转=显示, 右转=隐藏
[DASHBOARD] 转向灯模式: 左转=隐藏, 右转=显示
```

### 数据检查点
验证数据层的状态独立性：

```
[VEHICLE_DATA_UPDATER] 双闪灯状态变化: 关闭 -> 开启
[VEHICLE_DATA_UPDATER] 左转向灯状态变化: 关闭 -> 开启
[VEHICLE_DATA_UPDATER] 右转向灯状态变化: 关闭 -> 开启
```

## 问题排查指南

### 常见问题

#### 1. 转向灯不闪烁
**可能原因**: 
- UART命令格式错误
- 事件订阅失败
- UI组件未正确初始化

**排查步骤**:
```bash
# 检查UART数据接收
grep "UART消息回调" /var/log/ebike.log

# 检查事件发布
grep "事件已发布" /var/log/ebike.log

# 检查UI更新
grep "灯光状态更新" /var/log/ebike.log
```

#### 2. 双闪优先级不正确
**可能原因**:
- `calculate_light_display_state()` 函数逻辑错误
- UI更新时机问题

**排查步骤**:
```bash
# 检查优先级计算
grep "双闪模式\|转向灯模式" /var/log/ebike.log

# 检查闪烁效果处理
grep "dashboard_handle_blink_effects" /var/log/ebike.log
```

#### 3. 状态不同步
**可能原因**:
- 数据变化检测失败
- 事件发布丢失

**排查步骤**:
```bash
# 检查数据变化
grep "状态变化" /var/log/ebike.log

# 检查事件分发
grep "分发事件" /var/log/ebike.log
```

## 验收标准

### 功能验收
✅ 左转向灯独立工作正常  
✅ 右转向灯独立工作正常  
✅ 双闪灯功能正常  
✅ 双闪优先级高于单独转向灯  
✅ 状态独立性保持正确  

### 性能验收
✅ UI响应延迟 < 100ms  
✅ 闪烁频率稳定（500ms ± 50ms）  
✅ 无内存泄漏  
✅ CPU使用率无明显增加  

### 稳定性验收
✅ 连续运行1小时无异常  
✅ 快速切换状态无卡顿  
✅ 异常输入处理正确  

## 总结

双闪灯逻辑修复已完成并通过编译测试。通过本验证脚本可以全面测试修复后的功能，确保：

1. **功能正确性**: 双闪与转向灯功能独立且正确
2. **优先级正确**: 双闪优先级高于单独转向灯
3. **状态一致性**: 数据层与UI层状态同步
4. **用户体验**: 响应及时，显示清晰
