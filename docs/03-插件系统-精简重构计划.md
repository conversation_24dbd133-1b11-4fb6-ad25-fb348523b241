# 03-插件系统-精简重构计划

**时间**: 2025-07-30 16:30 GMT+8  
**作者**: <PERSON> (全栈开发者)  
**版本**: 1.0.0

## 重构目标

完全移除app_event相关依赖，精简插件代码，只保留核心功能，确保插件系统完全基于event_bus事件系统运行。

## 依赖分析

### 当前app_event依赖情况

#### 1. plug_hicar.c
```c
// 需要移除的依赖
#include "app_event/include/hicar_interface.h"

// 使用的函数
- hicar_interface_init()
- hicar_interface_deinit()  
- report_car_state()
- e_voice_ctrl_command 枚举类型
```

#### 2. plug_bridge.c
```c
// 需要移除的依赖
#include "app_event/include/hicar_interface.h"
#include "app/utils/screen_utils.h"
#include "app/include/uart_protocol.h"

// 使用的函数
- report_car_state()
- screen_turn_on()
- screen_turn_off()
- e_voice_ctrl_command 枚举类型
- UART协议相关宏定义
```

#### 3. plug_uart.c
```c
// 需要移除的依赖
#include "app/api/uart_api.h"
#include "app/utils/screen_utils.h"
#include "app/include/uart_protocol.h"

// 使用的函数
- uart_protocol_init()
- uart_protocol_deinit()
- uart_protocol_send_*() 系列函数
- uart_error_t 类型
- UART协议相关宏定义
```

#### 4. plugins_test.c
```c
// 需要移除的依赖
#include "app_event/include/hicar_interface.h"

// 使用的函数
- test_hicar_functions()
```

## 重构策略

### 1. 核心原则
- **最小化依赖**: 只保留event_bus事件系统依赖
- **功能精简**: 移除非核心功能，专注于事件处理
- **接口统一**: 使用统一的插件接口规范
- **代码复用**: 避免重复代码，提取公共功能

### 2. 依赖替换方案

#### HiCar接口替换
```c
// 原代码
#include "app_event/include/hicar_interface.h"
hicar_interface_init();
report_car_state(cmd);

// 替换为
// 移除HiCar接口依赖，直接使用事件系统
eb_publish(EVENT_HICAR_STATE_CHANGED, &cmd);
```

#### UART协议替换
```c
// 原代码
#include "app/include/uart_protocol.h"
uart_protocol_send_light_ctrl_msg(handle, ack, cmd);

// 替换为
// 简化为基础UART发送，移除复杂协议栈
simple_uart_send(id, data, len);
```

#### 屏幕控制替换
```c
// 原代码
#include "app/utils/screen_utils.h"
screen_turn_on();

// 替换为
// 通过事件系统通知屏幕控制
eb_publish(EVENT_SCREEN_CONTROL, &screen_cmd);
```

### 3. 新的插件架构

#### 精简插件结构
```c
typedef struct {
    int initialized;
    // 只保留必要的状态变量
} plugin_private_t;

// 标准插件接口
static void plugin_init(void);
static void plugin_deinit(void);
static void plugin_event_handler(uint16_t id, void *data, void *ctx);
int plug_xxx_init(void);
int plug_xxx_deinit(void);
```

#### 事件ID重新定义
```c
// 核心事件ID (0x1000-0x1FFF: 系统事件)
#define EVENT_SYS_START             0x1000
#define EVENT_COMM_DATA_RECEIVED    0x1001

// 业务事件ID (0x2000-0x2FFF: 业务事件)  
#define EVENT_BIZ_HICAR_COMMAND     0x2001
#define EVENT_BIZ_LIGHT_CHANGED     0x2002
#define EVENT_BIZ_SCREEN_CONTROL    0x2003

// 车辆事件ID (0x3000-0x3FFF: 车辆事件)
#define EVENT_BIZ_VEHICLE_STATUS    0x3001
#define EVENT_BIZ_SPEED_INFO        0x3002
```

## 重构实施计划

### 阶段1: 准备工作
- [x] 分析现有依赖关系
- [x] 制定重构策略
- [ ] 创建必要的替换函数

### 阶段2: 核心重构
- [ ] 重构plug_uart.c - 移除UART协议栈依赖
- [ ] 重构plug_hicar.c - 移除HiCar接口依赖  
- [ ] 重构plug_bridge.c - 移除所有app_event依赖

### 阶段3: 清理优化
- [ ] 移除冗余文件和代码
- [ ] 优化插件接口
- [ ] 统一错误处理

### 阶段4: 测试验证
- [ ] 编译测试
- [ ] 功能验证
- [ ] 部署测试

## 具体重构内容

### 1. plug_uart.c 重构
**移除内容**:
- 复杂的UART协议栈调用
- 详细的消息解析逻辑
- 模拟模式相关代码

**保留内容**:
- 基础UART通信能力
- 事件发布机制
- 插件生命周期管理

**新增内容**:
- 简化的UART发送函数
- 统一的错误处理

### 2. plug_hicar.c 重构
**移除内容**:
- hicar_interface.h 依赖
- 复杂的HiCar接口调用
- 图标控制器相关代码

**保留内容**:
- HiCar命令处理逻辑
- 事件订阅和处理
- 插件生命周期管理

**新增内容**:
- 简化的命令处理
- 直接的事件通信

### 3. plug_bridge.c 重构
**移除内容**:
- 所有app_event依赖
- 复杂的命令映射表
- 图标控制器处理

**保留内容**:
- 基础的UART-HiCar桥接逻辑
- 事件转发机制
- 插件生命周期管理

**新增内容**:
- 简化的命令转换
- 统一的事件处理

## 预期成果

### 1. 代码精简
- 减少50%以上的代码量
- 移除所有app_event依赖
- 简化插件接口

### 2. 架构优化
- 清晰的模块边界
- 统一的事件通信
- 简化的依赖关系

### 3. 维护性提升
- 更少的外部依赖
- 更清晰的代码结构
- 更容易的测试和调试

## 风险评估

### 高风险项
1. 移除UART协议栈可能影响通信功能
2. 移除HiCar接口可能影响语音控制
3. 简化命令处理可能丢失功能

### 缓解措施
1. 保留核心通信逻辑，只移除复杂封装
2. 通过事件系统实现相同功能
3. 逐步重构，每步都进行测试验证

## 后续计划

1. 完成重构后进行全面测试
2. 根据测试结果调整实现
3. 编写新的使用文档
4. 考虑添加更多插件示例
