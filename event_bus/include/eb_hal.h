/**
 * @file eb_hal.h
 * @brief 事件总线HAL抽象API声明
 * @date 2025-07-29 17:19
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~30行
 */

#ifndef EB_HAL_H
#define EB_HAL_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/*==============================================================================
 * HAL抽象API (4个)
 *============================================================================*/

/**
 * @brief HAL层初始化
 * @return 成功返回true，失败返回false
 */
bool eb_hal_init(void);

/**
 * @brief HAL层定时器启动
 * @param ms 定时周期(毫秒)
 * @param repeat 是否重复
 * @param cb 回调函数
 * @return 成功返回true，失败返回false
 */
bool eb_hal_timer_start(uint32_t ms, bool repeat, void (*cb)(void));

/**
 * @brief HAL层定时器停止
 */
void eb_hal_timer_stop(void);

/**
 * @brief HAL层中断级事件投递
 * @param id 事件ID
 * @param data 事件数据
 * @return 成功返回true，失败返回false
 */
bool eb_hal_post_from_isr(uint16_t id, void *data);

#ifdef __cplusplus
}
#endif

#endif /* EB_HAL_H */
