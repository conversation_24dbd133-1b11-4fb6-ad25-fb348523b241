/**
 * @file eb_plugin.h
 * @brief 事件总线插件管理API声明
 * @date 2025-07-29 17:18
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~30行
 */

#ifndef EB_PLUGIN_H
#define EB_PLUGIN_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/*==============================================================================
 * 插件管理API (3个)
 *============================================================================*/

/**
 * @brief 注册插件
 * @param name 插件名称
 * @param init 初始化函数
 * @param deinit 清理函数
 * @return 成功返回true，失败返回false
 */
bool eb_plug_register(const char *name, void (*init)(void), void (*deinit)(void));

/**
 * @brief 卸载插件
 * @param name 插件名称
 * @return 成功返回true，失败返回false
 */
bool eb_plug_unregister(const char *name);

/**
 * @brief 启动所有插件
 */
void eb_plug_start_all(void);

/*==============================================================================
 * 插件查询接口
 *============================================================================*/

/**
 * @brief 获取插件数量
 * @return 当前注册的插件数量
 */
uint16_t eb_plug_get_count(void);

/**
 * @brief 检查插件是否存在
 * @param name 插件名称
 * @return 存在返回true，否则返回false
 */
bool eb_plug_exists(const char *name);

/**
 * @brief 检查插件是否激活
 * @param name 插件名称
 * @return 激活返回true，否则返回false
 */
bool eb_plug_is_active(const char *name);

#ifdef __cplusplus
}
#endif

#endif /* EB_PLUGIN_H */
