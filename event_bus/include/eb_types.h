/**
 * @file eb_types.h
 * @brief 事件总线数据结构和类型定义
 * @date 2025-07-29 17:15
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~80行
 */

#ifndef EB_TYPES_H
#define EB_TYPES_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/*==============================================================================
 * 配置参数
 *============================================================================*/

#define EVENT_QUEUE_SIZE        128     /**< 事件队列大小 - 增加到128以处理高频UART数据 */
#define MAX_SUBSCRIBERS         16      /**< 最大订阅者数量 */
#define MAX_PLUGINS             4       /**< 最大插件数量 */

/*==============================================================================
 * 核心数据结构
 *============================================================================*/

/**
 * @brief 事件结构
 */
typedef struct {
    uint16_t id;                       /**< 事件ID */
    void *data;                        /**< 事件数据 */
    uint32_t timestamp;                /**< 时间戳 */
} event_t;

/**
 * @brief 事件回调函数类型
 */
typedef void (*event_cb_t)(uint16_t id, void *data, void *ctx);

/**
 * @brief 订阅者结构
 */
typedef struct subscriber {
    uint16_t event_id;                 /**< 订阅的事件ID */
    event_cb_t callback;               /**< 回调函数 */
    void *context;                     /**< 用户上下文 */
    struct subscriber *next;           /**< 链表指针 */
} subscriber_t;

/**
 * @brief 插件结构
 */
typedef struct {
    const char *name;                  /**< 插件名称 */
    void (*init)(void);                /**< 初始化函数 */
    void (*deinit)(void);              /**< 清理函数 */
    bool is_active;                    /**< 激活状态 */
} plugin_t;

/**
 * @brief 事件总线结构
 */
typedef struct {
    // 事件队列
    event_t queue[EVENT_QUEUE_SIZE];
    uint16_t head;
    uint16_t tail;
    uint16_t count;
    
    // 订阅者链表
    subscriber_t *subscribers;
    subscriber_t subscriber_pool[MAX_SUBSCRIBERS];
    uint16_t subscriber_count;
    
    // 插件管理
    plugin_t plugins[MAX_PLUGINS];
    uint16_t plugin_count;
    
    // 统计信息
    uint32_t published_count;
    uint32_t lost_count;
    
    // 状态标志
    bool initialized;
} event_bus_t;

/*==============================================================================
 * 工具宏定义
 *============================================================================*/

/**
 * @brief 获取数组元素个数
 */
#define ARRAY_SIZE(arr)         (sizeof(arr) / sizeof((arr)[0]))

/**
 * @brief 检查指针是否有效
 */
#define IS_VALID_PTR(ptr)       ((ptr) != NULL)

/**
 * @brief 获取当前时间戳 (简化实现)
 */
static inline uint32_t get_timestamp(void) {
    static uint32_t counter = 0;
    return ++counter;
}

#ifdef __cplusplus
}
#endif

#endif /* EB_TYPES_H */
