/**
 * @file eb_utils.c
 * @brief 事件总线内部工具函数实现
 * @date 2025-07-29 17:21
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~80行
 */

#include <stdio.h>
#include <string.h>
#include "eb_types.h"

/*==============================================================================
 * 外部变量声明
 *============================================================================*/

extern event_bus_t g_event_bus;

/*==============================================================================
 * 内部辅助函数实现
 *============================================================================*/

/**
 * @brief 查找订阅者
 */
subscriber_t* eb_utils_find_subscriber(uint16_t event_id, event_cb_t callback) {
    subscriber_t *sub = g_event_bus.subscribers;
    while (sub) {
        if (sub->event_id == event_id && sub->callback == callback) {
            return sub;
        }
        sub = sub->next;
    }
    return NULL;
}

/**
 * @brief 分配订阅者节点
 */
subscriber_t* eb_utils_alloc_subscriber(void) {
    for (int i = 0; i < MAX_SUBSCRIBERS; i++) {
        if (g_event_bus.subscriber_pool[i].callback == NULL) {
            return &g_event_bus.subscriber_pool[i];
        }
    }
    return NULL;
}

/**
 * @brief 释放订阅者节点
 */
void eb_utils_free_subscriber(subscriber_t *sub) {
    if (sub >= g_event_bus.subscriber_pool && 
        sub < g_event_bus.subscriber_pool + MAX_SUBSCRIBERS) {
        memset(sub, 0, sizeof(subscriber_t));
    }
}

/**
 * @brief 查找插件
 */
plugin_t* eb_utils_find_plugin(const char *name) {
    if (!name) {
        return NULL;
    }
    
    for (int i = 0; i < g_event_bus.plugin_count; i++) {
        if (strcmp(g_event_bus.plugins[i].name, name) == 0) {
            return &g_event_bus.plugins[i];
        }
    }
    return NULL;
}

/**
 * @brief 获取事件总线实例
 */
event_bus_t* eb_utils_get_instance(void) {
    return &g_event_bus;
}

/**
 * @brief 检查事件总线是否已初始化
 */
bool eb_utils_is_initialized(void) {
    return g_event_bus.initialized;
}

/**
 * @brief 重置统计信息
 */
void eb_utils_reset_stats(void) {
    g_event_bus.published_count = 0;
    g_event_bus.lost_count = 0;
}

/**
 * @brief 获取队列使用率
 */
uint8_t eb_utils_get_queue_usage(void) {
    if (EVENT_QUEUE_SIZE == 0) {
        return 0;
    }
    return (g_event_bus.count * 100) / EVENT_QUEUE_SIZE;
}
