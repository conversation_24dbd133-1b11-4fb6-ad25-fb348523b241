/**
 * @file eb_hal.c
 * @brief 事件总线HAL抽象实现
 * @date 2025-07-29 17:26
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~80行
 */

#include <stdio.h>
#include "eb_types.h"
#include "eb_core.h"

/*==============================================================================
 * HAL抽象API实现 (空实现/桩函数)
 *============================================================================*/

/**
 * @brief HAL层初始化
 */
bool eb_hal_init(void) {
    printf("[HAL] Initialized (stub implementation)\n");
    return true;
}

/**
 * @brief HAL层定时器启动
 */
bool eb_hal_timer_start(uint32_t ms, bool repeat, void (*cb)(void)) {
    printf("[HAL] Timer start: %ums, repeat=%s, callback=%s (stub)\n", 
           ms, repeat ? "Yes" : "No", cb ? "Set" : "NULL");
    return true;
}

/**
 * @brief HAL层定时器停止
 */
void eb_hal_timer_stop(void) {
    printf("[HAL] Timer stop (stub)\n");
}

/**
 * @brief HAL层中断级事件投递
 */
bool eb_hal_post_from_isr(uint16_t id, void *data) {
    printf("[HAL] Post from ISR: event 0x%04X (stub)\n", id);
    
    // 直接调用普通的事件发布函数
    return eb_publish_isr(id, data);
}

/*==============================================================================
 * HAL层扩展接口 (可选实现)
 *============================================================================*/

/**
 * @brief HAL层获取系统时间
 * @return 系统时间戳 (毫秒)
 */
uint32_t eb_hal_get_time_ms(void) {
    return get_timestamp();
}

/**
 * @brief HAL层延时
 * @param ms 延时毫秒数
 */
void eb_hal_delay_ms(uint32_t ms) {
    printf("[HAL] Delay %ums (stub)\n", ms);
    // 实际实现中应该调用系统延时函数
}

/**
 * @brief HAL层进入临界区
 */
void eb_hal_critical_enter(void) {
    // 实际实现中应该禁用中断或获取互斥量
    // printf("[HAL] Critical enter (stub)\n");
}

/**
 * @brief HAL层退出临界区
 */
void eb_hal_critical_exit(void) {
    // 实际实现中应该恢复中断或释放互斥量
    // printf("[HAL] Critical exit (stub)\n");
}

/**
 * @brief HAL层获取状态信息
 */
void eb_hal_print_status(void) {
    printf("\n=== HAL Status ===\n");
    printf("HAL Implementation: Stub/Mock\n");
    printf("UART: Available\n");
    printf("Timer: Available\n");
    printf("ISR Support: Available\n");
    printf("==================\n\n");
}
