/**
 * @file eb_plugin.c
 * @brief 事件总线插件管理实现
 * @date 2025-07-29 17:25
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~120行
 */

#include <stdio.h>
#include <string.h>
#include "eb_types.h"
#include "eb_utils.h"

/*==============================================================================
 * 外部变量声明
 *============================================================================*/

extern event_bus_t g_event_bus;

/*==============================================================================
 * 插件管理API实现
 *============================================================================*/

/**
 * @brief 注册插件
 */
bool eb_plug_register(const char *name, void (*init)(void), void (*deinit)(void)) {
    if (!eb_utils_is_initialized() || !name || g_event_bus.plugin_count >= MAX_PLUGINS) {
        return false;
    }
    
    // 检查插件是否已存在
    if (eb_utils_find_plugin(name)) {
        return false;
    }
    
    // 添加新插件
    plugin_t *plugin = &g_event_bus.plugins[g_event_bus.plugin_count];
    plugin->name = name;
    plugin->init = init;
    plugin->deinit = deinit;
    plugin->is_active = false;
    g_event_bus.plugin_count++;
    
    printf("[Plugin] Registered: %s\n", name);
    return true;
}

/**
 * @brief 卸载插件
 */
bool eb_plug_unregister(const char *name) {
    if (!eb_utils_is_initialized() || !name) {
        return false;
    }
    
    for (int i = 0; i < g_event_bus.plugin_count; i++) {
        if (strcmp(g_event_bus.plugins[i].name, name) == 0) {
            // 如果插件激活，先清理
            if (g_event_bus.plugins[i].is_active && g_event_bus.plugins[i].deinit) {
                g_event_bus.plugins[i].deinit();
            }
            
            // 移动后续插件
            for (int j = i; j < g_event_bus.plugin_count - 1; j++) {
                g_event_bus.plugins[j] = g_event_bus.plugins[j + 1];
            }
            g_event_bus.plugin_count--;
            
            printf("[Plugin] Unregistered: %s\n", name);
            return true;
        }
    }
    
    return false;
}

/**
 * @brief 启动所有插件
 */
void eb_plug_start_all(void) {
    if (!eb_utils_is_initialized()) {
        return;
    }
    
    int started_count = 0;
    for (int i = 0; i < g_event_bus.plugin_count; i++) {
        if (!g_event_bus.plugins[i].is_active && g_event_bus.plugins[i].init) {
            g_event_bus.plugins[i].init();
            g_event_bus.plugins[i].is_active = true;
            started_count++;
        }
    }
    
    printf("[Plugin] Started %d plugins\n", started_count);
}

/**
 * @brief 获取插件数量
 */
uint16_t eb_plug_get_count(void) {
    return g_event_bus.plugin_count;
}

/**
 * @brief 检查插件是否存在
 */
bool eb_plug_exists(const char *name) {
    return eb_utils_find_plugin(name) != NULL;
}

/**
 * @brief 检查插件是否激活
 */
bool eb_plug_is_active(const char *name) {
    plugin_t *plugin = eb_utils_find_plugin(name);
    return plugin ? plugin->is_active : false;
}

/**
 * @brief 停止所有插件
 */
void eb_plug_stop_all(void) {
    if (!eb_utils_is_initialized()) {
        return;
    }
    
    int stopped_count = 0;
    for (int i = 0; i < g_event_bus.plugin_count; i++) {
        if (g_event_bus.plugins[i].is_active && g_event_bus.plugins[i].deinit) {
            g_event_bus.plugins[i].deinit();
            g_event_bus.plugins[i].is_active = false;
            stopped_count++;
        }
    }
    
    printf("[Plugin] Stopped %d plugins\n", stopped_count);
}

/**
 * @brief 打印插件信息
 */
void eb_plug_print_info(void) {
    if (!eb_utils_is_initialized()) {
        printf("Event Bus not initialized\n");
        return;
    }
    
    printf("\n=== Plugin Information ===\n");
    printf("Total Plugins: %u/%u\n", g_event_bus.plugin_count, MAX_PLUGINS);
    
    for (int i = 0; i < g_event_bus.plugin_count; i++) {
        plugin_t *plugin = &g_event_bus.plugins[i];
        printf("  [%d] %s - %s\n", i + 1, plugin->name, 
               plugin->is_active ? "Active" : "Inactive");
    }
    
    printf("==========================\n\n");
}
