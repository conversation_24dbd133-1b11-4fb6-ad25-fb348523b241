/**
 * @file eb_core.c
 * @brief 事件总线核心API实现
 * @date 2025-07-29 17:23
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~180行
 */

#include <stdio.h>
#include <string.h>
#include "eb_types.h"
#include "eb_utils.h"

/*==============================================================================
 * 全局事件总线实例
 *============================================================================*/

event_bus_t g_event_bus = {0};

/*==============================================================================
 * 核心事件API实现
 *============================================================================*/

/**
 * @brief 初始化事件总线
 */
bool eb_init(void) {
    if (g_event_bus.initialized) {
        return true;
    }
    
    memset(&g_event_bus, 0, sizeof(event_bus_t));
    g_event_bus.initialized = true;
    
    printf("[Event Bus] Initialized\n");
    return true;
}

/**
 * @brief 清理事件总线
 */
void eb_deinit(void) {
    if (!g_event_bus.initialized) {
        return;
    }
    
    // 清理所有插件
    for (int i = 0; i < g_event_bus.plugin_count; i++) {
        if (g_event_bus.plugins[i].deinit && g_event_bus.plugins[i].is_active) {
            g_event_bus.plugins[i].deinit();
        }
    }
    
    memset(&g_event_bus, 0, sizeof(event_bus_t));
    printf("[Event Bus] Deinitialized\n");
}

/**
 * @brief 订阅事件
 */
bool eb_subscribe(uint16_t id, event_cb_t cb, void *ctx) {
    if (!g_event_bus.initialized || !cb) {
        return false;
    }
    
    // 检查是否已经订阅
    if (eb_utils_find_subscriber(id, cb)) {
        return true;
    }
    
    // 分配新的订阅者节点
    subscriber_t *sub = eb_utils_alloc_subscriber();
    if (!sub) {
        return false;
    }
    
    sub->event_id = id;
    sub->callback = cb;
    sub->context = ctx;
    sub->next = g_event_bus.subscribers;
    g_event_bus.subscribers = sub;
    g_event_bus.subscriber_count++;
    
    return true;
}

/**
 * @brief 取消订阅事件
 */
bool eb_unsubscribe(uint16_t id, event_cb_t cb) {
    if (!g_event_bus.initialized || !cb) {
        return false;
    }
    
    subscriber_t **current = &g_event_bus.subscribers;
    while (*current) {
        if ((*current)->event_id == id && (*current)->callback == cb) {
            subscriber_t *to_remove = *current;
            *current = (*current)->next;
            eb_utils_free_subscriber(to_remove);
            g_event_bus.subscriber_count--;
            return true;
        }
        current = &(*current)->next;
    }
    
    return false;
}

/**
 * @brief 发布事件
 */
bool eb_publish(uint16_t id, void *data) {
    if (!g_event_bus.initialized) {
        printf("[Event Bus] 发布失败: 事件总线未初始化, ID=0x%04X\n", id);
        return false;
    }

    // 检查队列是否已满
    if (g_event_bus.count >= EVENT_QUEUE_SIZE) {
        g_event_bus.lost_count++;
        printf("[Event Bus] 发布失败: 队列已满 (%d/%d), ID=0x%04X\n",
               g_event_bus.count, EVENT_QUEUE_SIZE, id);
        return false;
    }
    
    // 添加事件到队列
    event_t *event = &g_event_bus.queue[g_event_bus.tail];
    event->id = id;
    event->data = data;
    event->timestamp = get_timestamp();
    
    g_event_bus.tail = (g_event_bus.tail + 1) % EVENT_QUEUE_SIZE;
    g_event_bus.count++;
    g_event_bus.published_count++;
    
    return true;
}

/**
 * @brief 中断安全发布事件
 */
bool eb_publish_isr(uint16_t id, void *data) {
    // 简化实现：与普通发布相同
    return eb_publish(id, data);
}

/**
 * @brief 分发事件
 */
void eb_dispatch(void) {
    if (!g_event_bus.initialized) {
        return;
    }

    if (g_event_bus.count == 0) {
        return;
    }

    // 处理队列中的所有事件
    int processed_count = 0;
    while (g_event_bus.count > 0) {
        event_t *event = &g_event_bus.queue[g_event_bus.head];

        // 查找并调用所有订阅者
        int subscriber_count = 0;
        subscriber_t *sub = g_event_bus.subscribers;
        while (sub) {
            if (sub->event_id == event->id && sub->callback) {
                printf("[Event Bus] 分发事件: ID=0x%04X 到订阅者 %d\n", event->id, subscriber_count);
                sub->callback(event->id, event->data, sub->context);
                subscriber_count++;
            }
            sub = sub->next;
        }

        if (subscriber_count == 0) {
            printf("[Event Bus] 警告: 事件 ID=0x%04X 没有订阅者\n", event->id);
        }

        // 移动队列头指针
        g_event_bus.head = (g_event_bus.head + 1) % EVENT_QUEUE_SIZE;
        g_event_bus.count--;
        processed_count++;
    }

    if (processed_count > 0) {
        printf("[Event Bus] 本次分发处理了 %d 个事件\n", processed_count);
    }
}

/**
 * @brief 获取丢失事件统计
 */
uint32_t eb_stats_lost(void) {
    return g_event_bus.lost_count;
}

/**
 * @brief 获取事件总线统计信息
 */
void eb_print_stats(void) {
    if (!g_event_bus.initialized) {
        printf("Event Bus not initialized\n");
        return;
    }
    
    printf("\n=== Event Bus Statistics ===\n");
    printf("Initialized: %s\n", g_event_bus.initialized ? "Yes" : "No");
    printf("Queue: %u/%u events\n", g_event_bus.count, EVENT_QUEUE_SIZE);
    printf("Subscribers: %u/%u\n", g_event_bus.subscriber_count, MAX_SUBSCRIBERS);
    printf("Plugins: %u/%u\n", g_event_bus.plugin_count, MAX_PLUGINS);
    printf("Published: %u events\n", g_event_bus.published_count);
    printf("Lost: %u events\n", g_event_bus.lost_count);
    printf("Queue Usage: %u%%\n", eb_utils_get_queue_usage());
    printf("============================\n\n");
}

/**
 * @brief 检查事件总线状态
 */
bool eb_is_healthy(void) {
    if (!g_event_bus.initialized) {
        return false;
    }
    
    // 检查队列是否接近满
    if (g_event_bus.count > EVENT_QUEUE_SIZE * 0.8) {
        return false;
    }
    
    // 检查丢失事件是否过多
    if (g_event_bus.lost_count > g_event_bus.published_count * 0.1) {
        return false;
    }
    
    return true;
}
