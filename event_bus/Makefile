# HiCar Lite 项目 Makefile
# 创建时间: 2025-07-29 15:50
# 作者: <PERSON> (全栈开发者)

# 项目配置
PROJECT_NAME = event_bus
VERSION = 1.0.0
TARGET = $(PROJECT_NAME)

# 目录配置
SRC_DIR = src
INC_DIR = include
OBJ_DIR = obj
BIN_DIR = bin
TEST_DIR = tests

# 源文件目录
TESTS_DIR = $(TEST_DIR)

# 编译器配置
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
CPPFLAGS = -I$(INC_DIR) -I$(SRC_DIR)
LDFLAGS = -lm -lpthread -lrt

# 调试模式
ifdef DEBUG
    CFLAGS += -DDEBUG -O0 -g3
endif

# 源文件列表
SRC_SOURCES = $(wildcard $(SRC_DIR)/*.c)
TESTS_SOURCES = $(wildcard $(TESTS_DIR)/*.c)
MAIN_SOURCE = main.c

ALL_SOURCES = $(SRC_SOURCES) $(TESTS_SOURCES) $(MAIN_SOURCE)

# 目标文件列表
EVENT_BUS_OBJECTS = $(SRC_SOURCES:$(SRC_DIR)/%.c=$(OBJ_DIR)/eb_%.o)
TESTS_OBJECTS = $(TESTS_SOURCES:$(TESTS_DIR)/%.c=$(OBJ_DIR)/test_%.o)
MAIN_OBJECT = main.o

ALL_OBJECTS = $(EVENT_BUS_OBJECTS) $(TESTS_OBJECTS) $(MAIN_OBJECT)

# 测试文件
TEST_SOURCES = $(wildcard $(TEST_DIR)/unit/*.c) $(wildcard $(TEST_DIR)/integration/*.c)
TEST_OBJECTS = $(TEST_SOURCES:%.c=$(OBJ_DIR)/%.o)
TEST_TARGET = $(BIN_DIR)/$(PROJECT_NAME)

# 默认目标
.PHONY: all clean test install help

all: $(BIN_DIR)/$(TARGET)

# 创建目录
$(OBJ_DIR) $(BIN_DIR):
	@mkdir -p $@

# 主程序编译
$(BIN_DIR)/$(TARGET): $(ALL_OBJECTS) | $(BIN_DIR)
	@echo "Linking $(TARGET)..."
	@$(CC) $(ALL_OBJECTS) -o $@ $(LDFLAGS)
	@echo "Build complete: $@"

# 事件总线目标文件
$(OBJ_DIR)/eb_%.o: $(SRC_DIR)/%.c | $(OBJ_DIR)
	@echo "Compiling Event Bus: $<"
	@$(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $@

# 测试目标文件
$(OBJ_DIR)/test_%.o: $(TESTS_DIR)/%.c | $(OBJ_DIR)
	@echo "Compiling Test: $<"
	@$(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $@

# 主程序目标文件
$(OBJ_DIR)/main.o: $(SRC_DIR)/main.c | $(OBJ_DIR)
	@echo "Compiling Main: $<"
	@$(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $@

# 测试目标文件
$(OBJ_DIR)/%.o: %.c | $(OBJ_DIR)
	@echo "Compiling Test: $<"
	@mkdir -p $(dir $@)
	@$(CC) $(CFLAGS) $(CPPFLAGS) -DUNIT_TEST -c $< -o $@

# 测试程序
test: $(TEST_TARGET)
	@echo "Running tests..."
	@./$(TEST_TARGET)

$(TEST_TARGET): $(TEST_OBJECTS) $(filter-out $(MAIN_OBJECT), $(ALL_OBJECTS)) | $(BIN_DIR)
	@echo "Linking test executable..."
	@$(CC) $^ -o $@ $(LDFLAGS) -DUNIT_TEST

# 清理
clean:
	@echo "Cleaning..."
	@rm -rf $(OBJ_DIR) $(BIN_DIR)
	@echo "Clean complete."

# 安装
install: $(BIN_DIR)/$(TARGET)
	@echo "Installing $(TARGET)..."
	@sudo cp $(BIN_DIR)/$(TARGET) /usr/local/bin/
	@echo "Installation complete."

# 代码统计
stats:
	@echo "=== Code Statistics ==="
	@echo "Event Bus Core:"
	@wc -l $(SRC_SOURCES) 2>/dev/null || echo "No Event Bus files found"
	@echo "Test Files:"
	@wc -l $(TESTS_SOURCES) 2>/dev/null || echo "No Test files found"
	@echo "Main Program:"
	@wc -l $(MAIN_SOURCE) 2>/dev/null || echo "No Main file found"
	@echo "Header Files:"
	@find $(INC_DIR) -name "*.h" -exec wc -l {} + 2>/dev/null || echo "No header files found"
	@echo "Total Source:"
	@wc -l $(ALL_SOURCES) 2>/dev/null || echo "No source files found"

# 调试编译
debug:
	@$(MAKE) DEBUG=1

# 发布编译
release:
	@$(MAKE) CFLAGS="-Wall -Wextra -std=c99 -O3 -DNDEBUG"

# 格式化代码
format:
	@echo "Formatting code..."
	@find $(SRC_DIR) $(INC_DIR) -name "*.c" -o -name "*.h" | xargs clang-format -i
	@echo "Format complete."

# 静态分析
analyze:
	@echo "Running static analysis..."
	@cppcheck --enable=all --inconclusive $(SRC_DIR) $(INC_DIR)

# 帮助信息
help:
	@echo "HiCar Lite Build System"
	@echo "======================="
	@echo "Targets:"
	@echo "  all      - Build the main program (default)"
	@echo "  test     - Build and run tests"
	@echo "  clean    - Remove build artifacts"
	@echo "  install  - Install to system"
	@echo "  debug    - Build with debug flags"
	@echo "  release  - Build optimized release"
	@echo "  stats    - Show code statistics"
	@echo "  format   - Format source code"
	@echo "  analyze  - Run static analysis"
	@echo "  help     - Show this help"
	@echo ""
	@echo "Variables:"
	@echo "  DEBUG=1  - Enable debug mode"
	@echo ""
	@echo "Examples:"
	@echo "  make debug"
	@echo "  make test"
	@echo "  make clean && make release"

# 依赖关系
-include $(ALL_OBJECTS:.o=.d)

# 生成依赖文件
$(OBJ_DIR)/%.d: %.c | $(OBJ_DIR)
	@mkdir -p $(dir $@)
	@$(CC) $(CPPFLAGS) -MM -MT $(@:.d=.o) $< > $@
