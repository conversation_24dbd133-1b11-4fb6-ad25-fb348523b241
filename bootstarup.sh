#!/bin/bash
set -euo pipefail

# 日志配置
LOGS_PATH="/root/logs"
mkdir -p "$LOGS_PATH"
LOG_FILE="${LOGS_PATH}/$(date +'%Y%m%d_%H%M%S').log"
exec >>"$LOG_FILE" 2>&1

log_msg() {
    echo "[$(date '+%F %T')] $*"
}

# 启动视频播放（静默模式，禁止刷屏）
VIDEO_PATH="/root/video.mp4"
log_msg "开始播放开机视频：$VIDEO_PATH"
tplayerdemo "$VIDEO_PATH" >/dev/null 2>&1 &
VIDEO_PID=$!

# 等待视频播放完成（最多 2 秒）
TIMEOUT=2
ELAPSED=0
while kill -0 "$VIDEO_PID" 2>/dev/null && [ $ELAPSED -lt $TIMEOUT ]; do
    sleep 1
    ELAPSED=$((ELAPSED + 1))
done

# 如果视频仍在运行，则强制终止
if kill -0 "$VIDEO_PID" 2>/dev/null; then
    log_msg "视频超时未结束，强制终止播放器 (PID=$VIDEO_PID)"
    kill "$VIDEO_PID"
    sleep 1
    kill -0 "$VIDEO_PID" 2>/dev/null && kill -9 "$VIDEO_PID"
fi

# 使用 setsid 启动主程序，脱离当前 shell
MAIN_APP="/root/ebike_x1"
log_msg "启动主程序：$MAIN_APP"
setsid "$MAIN_APP" >>"$LOG_FILE" 2>&1 &

MAIN_PID=$!
log_msg "主程序已独立启动，PID=$MAIN_PID"
log_msg "脚本退出，主程序继续在后台运行"