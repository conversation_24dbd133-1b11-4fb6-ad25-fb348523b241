// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _PRJ_EBIKE_X1_UI_H
#define _PRJ_EBIKE_X1_UI_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl/lvgl.h"

#include "ui_helpers.h"
#include "components/ui_comp.h"
#include "components/ui_comp_hook.h"
#include "ui_events.h"
#include "app/ebike_x1.h"

void topon_Animation(lv_obj_t * TargetObject, int delay);
void topoff_Animation(lv_obj_t * TargetObject, int delay);
void lefton_Animation(lv_obj_t * TargetObject, int delay);
void leftoff_Animation(lv_obj_t * TargetObject, int delay);
void opaon_Animation(lv_obj_t * TargetObject, int delay);
void opaoff_Animation(lv_obj_t * TargetObject, int delay);
void arrow_Animation(lv_obj_t * TargetObject, int delay);
void buttonon_Animation(lv_obj_t * TargetObject, int delay);
void buttonoff_Animation(lv_obj_t * TargetObject, int delay);
void bed_Animation(lv_obj_t * TargetObject, int delay);
void printer_in_Animation(lv_obj_t * TargetObject, int delay);

// SCREEN: ui_dashboard
void ui_dashboard_screen_init(void);
extern lv_obj_t * ui_dashboard;
extern lv_obj_t * ui_statusbar;
extern lv_obj_t * ui_speedometer;
extern lv_obj_t * ui_sta_icon;
extern lv_obj_t * ui_sta_icon;
extern lv_obj_t * ui_sta_icon;
extern lv_obj_t * ui_StaIcon1;
extern lv_obj_t * ui_StaIcon2;
extern lv_obj_t * ui_sidebar;
extern lv_obj_t * ui_home;
extern lv_obj_t * ui_hicar;
extern lv_obj_t * ui_setting;
extern lv_obj_t * ui_SettingSidebar;
extern lv_obj_t * ui_setting_content;
extern lv_obj_t * ui_system_setting_warp;
extern lv_obj_t * ui_system_item1;
extern lv_obj_t * ui_SettingItem;
extern lv_obj_t * ui_system_item2;
extern lv_obj_t * ui_SettingItem2;
extern lv_obj_t * ui_system_item3;
extern lv_obj_t * ui_SettingVolume;
extern lv_obj_t * ui_system_item4;
extern lv_obj_t * ui_SettingItem3;
extern lv_obj_t * ui_vehicle_setting_warp;
extern lv_obj_t * ui_vehicle_setting_warp1;
extern lv_obj_t * ui_SystemItem3;
extern lv_obj_t * ui_vehicle_setting_warp2;
extern lv_obj_t * ui_SystemItem2;
extern lv_obj_t * ui_vehicle_setting_warp3;
extern lv_obj_t * ui_SettingInfo;
extern lv_obj_t * ui_other_setting_warp;
extern lv_obj_t * ui_InfoLarge1;
extern lv_obj_t * ui_InfoLarge2;
extern lv_obj_t * ui_InfoLarge3;
extern lv_obj_t * ui_dialog;
extern lv_obj_t * ui_dialog_msg;
extern lv_obj_t * ui_ToastMsg;
extern lv_obj_t * ui_dialog_confirm;
extern lv_obj_t * ui_DialogConfirm;
extern lv_obj_t * ui_Keyboard;
extern lv_obj_t * ui_keyboard_input;
extern lv_obj_t * ui_dialog_view;
extern lv_obj_t * ui_paired;
extern lv_obj_t * ui_DeviceItem3;
extern lv_obj_t * ui_paired1;
extern lv_obj_t * ui_DeviceItem4;
extern lv_obj_t * ui_unpaired;
extern lv_obj_t * ui_DeviceItem;
extern lv_obj_t * ui_unpaired1;
extern lv_obj_t * ui_DeviceItem1;
extern lv_obj_t * ui_BLANK;
extern lv_obj_t * ui_ListEmpty;
// CUSTOM VARIABLES

// SCREEN: ui_components
void ui_components_screen_init(void);
extern lv_obj_t * ui_components;
// CUSTOM VARIABLES

// EVENTS

extern lv_obj_t * ui____initial_actions0;

// IMAGES AND IMAGE SETS
LV_IMG_DECLARE(ui_img_dark_ic_sta_charging_png);    // assets/dark/ic_sta_charging.png
LV_IMG_DECLARE(ui_img_dark_ic_sta_location_png);    // assets/dark/ic_sta_location.png
LV_IMG_DECLARE(ui_img_dark_ic_sta_battery_png);    // assets/dark/ic_sta_battery.png
LV_IMG_DECLARE(ui_img_dark_ic_sta_signals_l_png);    // assets/dark/ic_sta_signals_l.png
LV_IMG_DECLARE(ui_img_dark_ic_sta_signals_r_png);    // assets/dark/ic_sta_signals_r.png
LV_IMG_DECLARE(ui_img_dark_ic_sta_nfc_png);    // assets/dark/ic_sta_nfc.png
LV_IMG_DECLARE(ui_img_dark_ic_sta_signals_f_png);    // assets/dark/ic_sta_signals_f.png
LV_IMG_DECLARE(ui_img_dark_ic_sta_signals_fd_png);    // assets/dark/ic_sta_signals_fd.png
LV_IMG_DECLARE(ui_img_dark_ic_sta_signals_fe_png);    // assets/dark/ic_sta_signals_fe.png
LV_IMG_DECLARE(ui_img_dark_ic_sta_4g_full_png);    // assets/dark/ic_sta_4g_full.png
LV_IMG_DECLARE(ui_img_dark_nav_btn_active_png);    // assets/dark/nav_btn_active.png
LV_IMG_DECLARE(ui_img_dark_ic_home_png);    // assets/dark/ic_home.png
LV_IMG_DECLARE(ui_img_dark_ic_hicar_png);    // assets/dark/ic_hicar.png
LV_IMG_DECLARE(ui_img_dark_ic_setting_png);    // assets/dark/ic_setting.png
LV_IMG_DECLARE(ui_img_bg_home_png);    // assets/bg_home.png
LV_IMG_DECLARE(ui_img_bg_car_model_png);    // assets/bg_car_model.png
LV_IMG_DECLARE(ui_img_dark_ic_sta_nos_bg_png);    // assets/dark/ic_sta_nos_bg.png
LV_IMG_DECLARE(ui_img_dark_ic_line_light_png);    // assets/dark/ic_line_light.png
LV_IMG_DECLARE(ui_img_dark_sta_battery_null_png);    // assets/dark/sta_battery_null.png
LV_IMG_DECLARE(ui_img_dark_sta_battery_png);    // assets/dark/sta_battery.png
LV_IMG_DECLARE(ui_img_dark_bg_dashboard_main_png);    // assets/dark/bg_dashboard_main.png
LV_IMG_DECLARE(ui_img_setting_ic_wifi_big_png);    // assets/setting/ic_wifi_big.png
LV_IMG_DECLARE(ui_img_dark_ic_btn_active_bg_png);    // assets/dark/ic_btn_active_bg.png
LV_IMG_DECLARE(ui_img_dark_ic_setting_btn_bg_png);    // assets/dark/ic_setting_btn_bg.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_wifi_png);    // assets/setting/ic_setting_wifi.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_arrow_png);    // assets/setting/ic_setting_arrow.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_bluetooth_png);    // assets/setting/ic_setting_bluetooth.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_volume_png);    // assets/setting/ic_setting_volume.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_date_png);    // assets/setting/ic_setting_date.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_btkey_png);    // assets/setting/ic_setting_btkey.png
LV_IMG_DECLARE(ui_img_dark_ic_setting_btn_xl_bg_png);    // assets/dark/ic_setting_btn_xl_bg.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_dashbord_png);    // assets/setting/ic_setting_dashbord.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_lan_png);    // assets/setting/ic_setting_lan.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_reset_png);    // assets/setting/ic_setting_reset.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_alarm_png);    // assets/setting/ic_setting_alarm.png
LV_IMG_DECLARE(ui_img_setting_ic_setting_keyboard_png);    // assets/setting/ic_setting_keyboard.png
LV_IMG_DECLARE(ui_img_dialog_pic_bg_m_png);    // assets/dialog/pic_bg_m.png
LV_IMG_DECLARE(ui_img_dialog_ic_wifi_msg_png);    // assets/dialog/ic_wifi_msg.png
LV_IMG_DECLARE(ui_img_dialog_pic_bg_l_png);    // assets/dialog/pic_bg_l.png
LV_IMG_DECLARE(ui_img_dialog_dialog_confirm_btn_png);    // assets/dialog/dialog_confirm_btn.png
LV_IMG_DECLARE(ui_img_dialog_ic_close_png);    // assets/dialog/ic_close.png
LV_IMG_DECLARE(ui_img_dialog_dialog_view_bg_png);    // assets/dialog/dialog_view_bg.png
LV_IMG_DECLARE(ui_img_dialog_ic_return_png);    // assets/dialog/ic_return.png
LV_IMG_DECLARE(ui_img_dialog_list_item_normal_png);    // assets/dialog/list_item_normal.png
LV_IMG_DECLARE(ui_img_dialog_list_item_active_png);    // assets/dialog/list_item_active.png
LV_IMG_DECLARE(ui_img_dialog_ic_bluetooth_png);    // assets/dialog/ic_bluetooth.png
LV_IMG_DECLARE(ui_img_dialog_list_item_checked_png);    // assets/dialog/list_item_checked.png
LV_IMG_DECLARE(ui_img_dialog_list_item_more_png);    // assets/dialog/list_item_more.png

// FONTS
LV_FONT_DECLARE(ui_font_AlibabaPuHui14);
LV_FONT_DECLARE(ui_font_AlibabaPuHui15);
LV_FONT_DECLARE(ui_font_AlibabaPuHui18);
LV_FONT_DECLARE(ui_font_AlibabaPuHui24);
LV_FONT_DECLARE(ui_font_AlibabaPuHui26);
LV_FONT_DECLARE(ui_font_BrunoAceSCRegular150);
LV_FONT_DECLARE(ui_font_BrunoAceSCRegular80);

// UI INIT
void ui_init(void);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
