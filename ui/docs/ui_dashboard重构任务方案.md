# 《ui_dashboard.c模块化重构任务方案》

## 一、重构目标
- 保证原有功能、布局、交互完全不变，仅优化代码结构和可维护性。
- 按照LVGL最佳实践，将大块UI构建代码拆分为职责单一的子函数或组件，提升可读性和可扩展性。
- 新增的组件文件统一放置于`components/ui_dashboard`目录下，命名规范，避免与现有组件冲突。

## 二、重构原则
1. **功能与布局零改动**：所有UI元素、属性、事件绑定保持原样。
2. **职责分明**：每个子函数/组件仅负责单一UI区域或功能块（如状态栏、侧边栏、主内容区、弹窗等）。
3. **接口兼容**：全局变量、外部接口、事件回调等声明与调用方式保持不变，确保与`ui.h`、`ui.c`等文件的兼容性。
4. **LVGL最佳实践**：充分利用LVGL的分层、复用、灵活布局等特性，避免重复代码。
5. **中文注释**：所有新建函数、文件、关键逻辑均需添加详细中文注释，便于后续维护。
6. **文档同步**：重构完成后，需同步更新README和本方案，说明模块划分与优化点。

## 三、模块划分建议
- `ui_dashboard_screen_init`主函数仅负责顶层调度，具体UI区域交由下列子函数/组件实现：
  1. **仪表盘主背景区**（如主背景、主面板）
  2. **状态栏区**（如左/中/右状态栏、图标、时间、温度等）
  3. **侧边栏区**（如HOME、HICAR、SETTING按钮及其图标、标签）
  4. **主内容区**（如速度、模式、角度、能量等显示）
  5. **弹窗与对话框区**（如蓝牙、WiFi、键盘、提示等弹窗）
  6. **设置区**（如系统、车辆、其他设置等）
- 每个区域建议独立为一个`ui_comp_dashboard_xxx.c/h`组件文件，函数命名如`ui_comp_dashboard_statusbar_create`。
- 事件绑定、全局变量赋值等保留在主初始化函数或专用函数中。

## 四、接口与兼容性
- 保持`ui_dashboard_screen_init`函数签名、全局变量声明、事件回调接口不变。
- 新增组件函数通过头文件暴露，主文件通过`#include`调用。
- 组件内部变量、静态函数不对外暴露，防止命名冲突。

## 五、注释与文档要求
- 每个新建文件、函数、结构体均需添加中文注释，说明用途、参数、返回值。
- 关键逻辑、LVGL特殊用法需详细说明。
- 本方案和后续README需同步更新，便于团队理解和维护。

---
**进度补充：**
- 已完成状态栏、侧边栏、主内容区的组件化重构，主入口函数已调用新组件。
- 后续可继续弹窗和设置区的组件化。

**版本：v1.1**  
**日期：2025-05-29 11:27**
