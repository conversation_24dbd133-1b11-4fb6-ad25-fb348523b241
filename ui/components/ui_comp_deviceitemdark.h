// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_DEVICEITEMDARK_H
#define _UI_COMP_DEVICEITEMDARK_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT DeviceItemDark
#define UI_COMP_DEVICEITEMDARK_DEVICEITEMDARK 0
#define UI_COMP_DEVICEITEMDARK_WARP 1
#define UI_COMP_DEVICEITEMDARK_WARP_LEFT 2
#define UI_COMP_DEVICEITEMDARK_WARP_LEFT_ICON 3
#define UI_COMP_DEVICEITEMDARK_WARP_LEFT_ICON_IMAGE 4
#define UI_COMP_DEVICEITEMDARK_WARP_LEFT_LEBAL 5
#define UI_COMP_DEVICEITEMDARK_WARP_RIGHT 6
#define UI_COMP_DEVICEITEMDARK_WARP_RIGHT_GROUP 7
#define UI_COMP_DEVICEITEMDARK_WARP_RIGHT_GROUP_DESC 8
#define UI_COMP_DEVICEITEMDARK_WARP_RIGHT_GROUP_ICON1 9
#define UI_COMP_DEVICEITEMDARK_WARP_RIGHT_GROUP_ICON1_IMAGE1 10
#define UI_COMP_DEVICEITEMDARK_WARP_RIGHT_GROUP_ICON2 11
#define UI_COMP_DEVICEITEMDARK_WARP_RIGHT_GROUP_ICON2_IMAGE2 12
#define _UI_COMP_DEVICEITEMDARK_NUM 13
lv_obj_t * ui_DeviceItemDark_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
