// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT Listitem

lv_obj_t * ui_Listitem_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_Listitem;
    cui_Listitem = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_Listitem, 812);
    lv_obj_set_height(cui_Listitem, 68);
    lv_obj_set_flex_flow(cui_Listitem, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_Listitem, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_Listitem, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Listitem, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Listitem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_Listitem, &ui_img_dialog_list_item_normal_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_Listitem, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_Listitem, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_Listitem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_Listitem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_Listitem, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_Listitem, 0, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_img_src(cui_Listitem, &ui_img_dialog_list_item_active_png, LV_PART_MAIN | LV_STATE_CHECKED);

    lv_obj_t * cui_list_item_left;
    cui_list_item_left = lv_obj_create(cui_Listitem);
    lv_obj_set_height(cui_list_item_left, lv_pct(100));
    lv_obj_set_width(cui_list_item_left, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_list_item_left, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_left, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_left, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_left, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_left, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Panel9;
    cui_Panel9 = lv_obj_create(cui_list_item_left);
    lv_obj_set_width(cui_Panel9, 50);
    lv_obj_set_height(cui_Panel9, 50);
    lv_obj_set_align(cui_Panel9, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_Panel9, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Panel9, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Panel9, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Image2;
    cui_Image2 = lv_img_create(cui_Panel9);
    lv_img_set_src(cui_Image2, &ui_img_dialog_ic_bluetooth_png);
    lv_obj_set_width(cui_Image2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_Image2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_Image2, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_Image2, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_Image2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_Label2;
    cui_Label2 = lv_label_create(cui_list_item_left);
    lv_obj_set_width(cui_Label2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_Label2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_Label2, LV_ALIGN_CENTER);
    lv_label_set_text(cui_Label2, "TextTextTextTextTextText");
    lv_obj_set_style_text_color(cui_Label2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_Label2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_Label2, &ui_font_AlibabaPuHui18, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_Label2, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_Label2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_Label2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_Label2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_right;
    cui_list_item_right = lv_obj_create(cui_Listitem);
    lv_obj_set_height(cui_list_item_right, lv_pct(100));
    lv_obj_set_width(cui_list_item_right, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_list_item_right, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_right, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_right, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_right, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_right, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_rc;
    cui_list_item_rc = lv_obj_create(cui_list_item_right);
    lv_obj_set_width(cui_list_item_rc, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_rc, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_rc, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_rc, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_rc, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_list_item_rc, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_rc, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_rc, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_sta_value;
    cui_item_sta_value = lv_label_create(cui_list_item_rc);
    lv_obj_set_width(cui_item_sta_value, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_item_sta_value, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_item_sta_value, -1);
    lv_obj_set_y(cui_item_sta_value, 0);
    lv_obj_set_align(cui_item_sta_value, LV_ALIGN_CENTER);
    lv_label_set_text(cui_item_sta_value, "#CONNECTED");
    lv_obj_set_style_text_color(cui_item_sta_value, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_item_sta_value, 200, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_item_sta_value, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_item_sta_value, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_item_sta_value, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_item_sta_value, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_sta_icon_del;
    cui_item_sta_icon_del = lv_obj_create(cui_list_item_rc);
    lv_obj_set_width(cui_item_sta_icon_del, 50);
    lv_obj_set_height(cui_item_sta_icon_del, 50);
    lv_obj_set_align(cui_item_sta_icon_del, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_sta_icon_del, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_sta_icon_del, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_sta_icon_del, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Image7;
    cui_Image7 = lv_img_create(cui_item_sta_icon_del);
    lv_img_set_src(cui_Image7, &ui_img_dialog_list_item_checked_png);
    lv_obj_set_width(cui_Image7, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_Image7, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_Image7, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_Image7, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_Image7, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_item_sta_icon_dis;
    cui_item_sta_icon_dis = lv_obj_create(cui_list_item_rc);
    lv_obj_set_width(cui_item_sta_icon_dis, 50);
    lv_obj_set_height(cui_item_sta_icon_dis, 50);
    lv_obj_set_align(cui_item_sta_icon_dis, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_sta_icon_dis, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_sta_icon_dis, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_sta_icon_dis, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Image3;
    cui_Image3 = lv_img_create(cui_item_sta_icon_dis);
    lv_img_set_src(cui_Image3, &ui_img_dialog_list_item_more_png);
    lv_obj_set_width(cui_Image3, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_Image3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_Image3, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_Image3, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_Image3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_LISTITEM_NUM);
    children[UI_COMP_LISTITEM_LISTITEM] = cui_Listitem;
    children[UI_COMP_LISTITEM_LIST_ITEM_LEFT] = cui_list_item_left;
    children[UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9] = cui_Panel9;
    children[UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9_IMAGE2] = cui_Image2;
    children[UI_COMP_LISTITEM_LIST_ITEM_LEFT_LABEL2] = cui_Label2;
    children[UI_COMP_LISTITEM_LIST_ITEM_RIGHT] = cui_list_item_right;
    children[UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC] = cui_list_item_rc;
    children[UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_VALUE] = cui_item_sta_value;
    children[UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL] = cui_item_sta_icon_del;
    children[UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL_IMAGE7] = cui_Image7;
    children[UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS] = cui_item_sta_icon_dis;
    children[UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS_IMAGE3] = cui_Image3;
    lv_obj_add_event_cb(cui_Listitem, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_Listitem, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_Listitem_create_hook(cui_Listitem);
    return cui_Listitem;
}

