// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT Settingbtn

lv_obj_t * ui_Settingbtn_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_Settingbtn;
    cui_Settingbtn = lv_btn_create(comp_parent);
    lv_obj_set_width(cui_Settingbtn, 175);
    lv_obj_set_height(cui_Settingbtn, 65);
    lv_obj_set_x(cui_Settingbtn, -299);
    lv_obj_set_y(cui_Settingbtn, -14);
    lv_obj_set_align(cui_Settingbtn, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_Settingbtn, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(cui_Settingbtn, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Settingbtn, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Settingbtn, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_Settingbtn, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_Settingbtn, 0, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_img_src(cui_Settingbtn, &ui_img_dark_ic_btn_active_bg_png, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_img_src(cui_Settingbtn, &ui_img_dark_ic_btn_active_bg_png, LV_PART_MAIN | LV_STATE_PRESSED);

    lv_obj_t * cui_setting_btn_label;
    cui_setting_btn_label = lv_label_create(cui_Settingbtn);
    lv_obj_set_width(cui_setting_btn_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_setting_btn_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_setting_btn_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_setting_btn_label, "Vehicle");
    lv_obj_set_style_text_color(cui_setting_btn_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_setting_btn_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_setting_btn_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_setting_btn_label, 45, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_setting_btn_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_setting_btn_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SETTINGBTN_NUM);
    children[UI_COMP_SETTINGBTN_SETTINGBTN] = cui_Settingbtn;
    children[UI_COMP_SETTINGBTN_SETTING_BTN_LABEL] = cui_setting_btn_label;
    lv_obj_add_event_cb(cui_Settingbtn, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_Settingbtn, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_Settingbtn_create_hook(cui_Settingbtn);
    return cui_Settingbtn;
}

