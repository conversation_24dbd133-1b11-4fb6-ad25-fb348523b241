// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT StaIcon

lv_obj_t * ui_StaIcon_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_StaIcon;
    cui_StaIcon = lv_obj_create(comp_parent);
    lv_obj_set_height(cui_StaIcon, 48);
    lv_obj_set_width(cui_StaIcon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(cui_StaIcon, 574);
    lv_obj_set_y(cui_StaIcon, 38);
    lv_obj_set_flex_flow(cui_StaIcon, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_StaIcon, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_StaIcon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_StaIcon, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_StaIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_StaIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_StaIcon, 8, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_StaIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_StaIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_icon;
    cui_icon = lv_img_create(cui_StaIcon);
    lv_img_set_src(cui_icon, &ui_img_dark_ic_sta_nfc_png);
    lv_obj_set_width(cui_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_icon, -298);
    lv_obj_set_y(cui_icon, -230);
    lv_obj_set_align(cui_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_STAICON_NUM);
    children[UI_COMP_STAICON_STAICON] = cui_StaIcon;
    children[UI_COMP_STAICON_ICON] = cui_icon;
    lv_obj_add_event_cb(cui_StaIcon, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_StaIcon, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_StaIcon_create_hook(cui_StaIcon);
    return cui_StaIcon;
}

