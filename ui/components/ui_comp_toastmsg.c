// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT Toastmsg

lv_obj_t * ui_Toastmsg_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_Toastmsg;
    cui_Toastmsg = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_Toastmsg, lv_pct(100));
    lv_obj_set_height(cui_Toastmsg, lv_pct(100));
    lv_obj_set_align(cui_Toastmsg, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_Toastmsg, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_Toastmsg, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_Toastmsg, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Toastmsg, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Toastmsg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_toast_msg_icon;
    cui_toast_msg_icon = lv_obj_create(cui_Toastmsg);
    lv_obj_set_width(cui_toast_msg_icon, 75);
    lv_obj_set_height(cui_toast_msg_icon, 75);
    lv_obj_set_align(cui_toast_msg_icon, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_toast_msg_icon, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_toast_msg_icon, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_toast_msg_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_toast_msg_icon, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_toast_msg_icon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_toast_msg_icon_asset;
    cui_toast_msg_icon_asset = lv_img_create(cui_toast_msg_icon);
    lv_obj_set_width(cui_toast_msg_icon_asset, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_toast_msg_icon_asset, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_toast_msg_icon_asset, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_toast_msg_icon_asset, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_toast_msg_icon_asset, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_toast_msg_label;
    cui_toast_msg_label = lv_obj_create(cui_Toastmsg);
    lv_obj_set_height(cui_toast_msg_label, 36);
    lv_obj_set_width(cui_toast_msg_label, lv_pct(100));
    lv_obj_set_align(cui_toast_msg_label, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_toast_msg_label, LV_FLEX_FLOW_COLUMN_WRAP);
    lv_obj_set_flex_align(cui_toast_msg_label, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_toast_msg_label, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_toast_msg_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_toast_msg_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_toast_msg_label, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_toast_msg_label, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_toast_msg_label, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_toast_msg_label, 20, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_toast_msg_label, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_toast_msg_label, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_toast_msg_label, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_toast_msg_label, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_toast_msg_label_value;
    cui_toast_msg_label_value = lv_label_create(cui_toast_msg_label);
    lv_obj_set_width(cui_toast_msg_label_value, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_toast_msg_label_value, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_toast_msg_label_value, LV_ALIGN_CENTER);
    lv_label_set_text(cui_toast_msg_label_value, "#BLUETOOTH_CONNECT_SUCCESS");
    lv_obj_set_style_text_color(cui_toast_msg_label_value, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_toast_msg_label_value, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(cui_toast_msg_label_value, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_toast_msg_sub_label;
    cui_toast_msg_sub_label = lv_obj_create(cui_Toastmsg);
    lv_obj_set_height(cui_toast_msg_sub_label, 28);
    lv_obj_set_width(cui_toast_msg_sub_label, lv_pct(100));
    lv_obj_set_align(cui_toast_msg_sub_label, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_toast_msg_sub_label, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(cui_toast_msg_sub_label, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_toast_msg_sub_label, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_toast_msg_sub_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_toast_msg_sub_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_toast_msg_sub_label, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_toast_msg_sub_label, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_toast_msg_sub_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_toast_msg_sub_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_toast_msg_sub_label, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_toast_msg_sub_label, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_toast_msg_sub_label, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_toast_msg_sub_label, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_toast_msg_sub_label_value;
    cui_toast_msg_sub_label_value = lv_label_create(cui_toast_msg_sub_label);
    lv_obj_set_width(cui_toast_msg_sub_label_value, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_toast_msg_sub_label_value, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_toast_msg_sub_label_value, LV_ALIGN_CENTER);
    lv_label_set_text(cui_toast_msg_sub_label_value, "#RETURN");
    lv_obj_set_style_text_color(cui_toast_msg_sub_label_value, lv_color_hex(0xC3C3C3), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_toast_msg_sub_label_value, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(cui_toast_msg_sub_label_value, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_toast_msg_sub_label_value, &lv_font_montserrat_12, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_TOASTMSG_NUM);
    children[UI_COMP_TOASTMSG_TOASTMSG] = cui_Toastmsg;
    children[UI_COMP_TOASTMSG_TOAST_MSG_ICON] = cui_toast_msg_icon;
    children[UI_COMP_TOASTMSG_TOAST_MSG_ICON_TOAST_MSG_ICON_ASSET] = cui_toast_msg_icon_asset;
    children[UI_COMP_TOASTMSG_TOAST_MSG_LABEL] = cui_toast_msg_label;
    children[UI_COMP_TOASTMSG_TOAST_MSG_LABEL_TOAST_MSG_LABEL_VALUE] = cui_toast_msg_label_value;
    children[UI_COMP_TOASTMSG_TOAST_MSG_SUB_LABEL] = cui_toast_msg_sub_label;
    children[UI_COMP_TOASTMSG_TOAST_MSG_SUB_LABEL_TOAST_MSG_SUB_LABEL_VALUE] = cui_toast_msg_sub_label_value;
    lv_obj_add_event_cb(cui_Toastmsg, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_Toastmsg, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_Toastmsg_create_hook(cui_Toastmsg);
    return cui_Toastmsg;
}

