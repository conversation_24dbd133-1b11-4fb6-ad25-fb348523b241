// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_SYSTEMITEM_H
#define _UI_COMP_SYSTEMITEM_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT SystemItem
#define UI_COMP_SYSTEMITEM_SYSTEMITEM 0
#define UI_COMP_SYSTEMITEM_ITEM_ICON1 1
#define UI_COMP_SYSTEMITEM_ITEM_LABEL1 2
#define UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_LEFT1 3
#define UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_LEFT1_ITEM_LABEL_VAL1 4
#define UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_RIGHT1 5
#define UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_RIGHT1_LIST_ITEM_SUB1 6
#define UI_COMP_SYSTEMITEM_LIST_ITEM_ARROW3 7
#define _UI_COMP_SYSTEMITEM_NUM 8
lv_obj_t * ui_SystemItem_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
