// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_DIALOGVIEW_H
#define _UI_COMP_DIALOGVIEW_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT DialogView
#define UI_COMP_DIALOGVIEW_DIALOGVIEW 0
#define UI_COMP_DIALOGVIEW_DIALOG_VIEW_BG 1
#define UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP 2
#define UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD 3
#define UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD_HEAD_LEFT 4
#define UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD_HEAD_LEFT_HEAD_ICON 5
#define UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD_HEAD_LEFT_HEAD_ICON_IMAGE1 6
#define UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD_HEAD_LEFT_HEAD_LABEL 7
#define UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD_HEAD_RIGHT 8
#define UI_COMP_DIALOGVIEW_SWITCHBTN 9
#define UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_DIALOG_VIEW_MAIN 10
#define UI_COMP_DIALOGVIEW_ITEM_LIST 11
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_PAIRED_LABEL 12
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED 13
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1 14
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8 15
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_LEFT 16
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9 17
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9_ITEM_LIST_IMAGE2 18
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_LABEL2 19
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT 20
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC 21
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_VALUE 22
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL 23
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL_ITEM_LIST_IMAGE7 24
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS 25
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS_ITEM_LIST_IMAGE3 26
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2 27
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6 28
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_LEFT 29
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9 30
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9_ITEM_LIST_IMAGE2 31
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_LABEL2 32
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT 33
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC 34
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_VALUE 35
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL 36
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL_ITEM_LIST_IMAGE7 37
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS 38
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS_ITEM_LIST_IMAGE3 39
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_UNPAIRED_LABEL 40
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED 41
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3 42
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2 43
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_LEFT 44
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9 45
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9_ITEM_LIST_IMAGE2 46
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_LABEL2 47
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT 48
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC 49
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_VALUE 50
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL 51
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL_ITEM_LIST_IMAGE7 52
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS 53
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS_ITEM_LIST_IMAGE3 54
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4 55
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1 56
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_LEFT 57
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9 58
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9_ITEM_LIST_IMAGE2 59
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_LABEL2 60
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT 61
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC 62
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_VALUE 63
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL 64
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL_ITEM_LIST_IMAGE7 65
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS 66
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS_ITEM_LIST_IMAGE3 67
#define UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_BLANK 68
#define UI_COMP_DIALOGVIEW_LIST_EMPTY 69
#define UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_ICON 70
#define UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_ICON_LIST_EMPTY_DIALOG_CONNECT_ICON_ASSET 71
#define UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_LABEL_WARP 72
#define UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_LABEL_WARP_LIST_EMPTY_STATUS_LABEL 73
#define UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_SUB_WARP 74
#define UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_SUB_WARP_LIST_EMPTY_STATUS_SUB_LABEL 75
#define _UI_COMP_DIALOGVIEW_NUM 76
lv_obj_t * ui_DialogView_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
