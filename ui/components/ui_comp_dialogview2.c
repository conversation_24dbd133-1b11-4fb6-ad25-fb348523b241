// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT DialogView2

lv_obj_t * ui_DialogView2_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_DialogView2;
    cui_DialogView2 = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_DialogView2, lv_pct(100));
    lv_obj_set_height(cui_DialogView2, lv_pct(100));
    lv_obj_set_align(cui_DialogView2, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_DialogView2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_DialogView2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_DialogView2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_DialogView2, 124, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_DialogView2, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_DialogView2, 100, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_DialogView2, 25, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_bg_pic;
    cui_bg_pic = lv_obj_create(cui_DialogView2);
    lv_obj_set_width(cui_bg_pic, lv_pct(100));
    lv_obj_set_height(cui_bg_pic, lv_pct(100));
    lv_obj_set_align(cui_bg_pic, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_bg_pic, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_bg_pic, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_bg_pic, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_bg_pic, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_bg_pic, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_bg_pic, &ui_img_dialog_dialog_view_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_main;
    cui_main = lv_obj_create(cui_DialogView2);
    lv_obj_set_width(cui_main, lv_pct(100));
    lv_obj_set_height(cui_main, lv_pct(100));
    lv_obj_set_align(cui_main, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_main, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_main, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_main, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_main, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_main, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_main, &ui_img_dialog_dialog_view_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_main, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_main, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_main, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_main, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_head;
    cui_head = lv_obj_create(cui_main);
    lv_obj_set_height(cui_head, 90);
    lv_obj_set_width(cui_head, lv_pct(100));
    lv_obj_set_align(cui_head, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_head, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_head, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_SPACE_BETWEEN);
    lv_obj_clear_flag(cui_head, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_head, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_head, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_left;
    cui_left = lv_obj_create(cui_head);
    lv_obj_set_height(cui_left, 50);
    lv_obj_set_width(cui_left, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_left, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_left, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_left, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_left, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_left, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_icon;
    cui_icon = lv_obj_create(cui_left);
    lv_obj_set_width(cui_icon, 50);
    lv_obj_set_height(cui_icon, 50);
    lv_obj_set_align(cui_icon, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_icon, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_icon, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_icon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_image;
    cui_image = lv_img_create(cui_icon);
    lv_img_set_src(cui_image, &ui_img_dialog_ic_return_png);
    lv_obj_set_width(cui_image, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_image, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_image, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_image, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_image, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_label;
    cui_label = lv_label_create(cui_left);
    lv_obj_set_width(cui_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_label, "#BLUETOOTH");
    lv_obj_set_style_text_color(cui_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_label, &lv_font_montserrat_28, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_label, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_right;
    cui_right = lv_obj_create(cui_head);
    lv_obj_set_width(cui_right, 100);
    lv_obj_set_height(cui_right, 50);
    lv_obj_set_align(cui_right, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_right, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_right, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_right, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_right, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_switch;
    cui_switch = lv_switch_create(cui_right);
    lv_obj_set_width(cui_switch, 35);
    lv_obj_set_height(cui_switch, 18);
    lv_obj_set_align(cui_switch, LV_ALIGN_TOP_MID);
    lv_obj_set_style_radius(cui_switch, 35, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_switch, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_switch, 250, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(cui_switch, 20, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_color(cui_switch, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_switch, 0, LV_PART_MAIN | LV_STATE_CHECKED);

    lv_obj_set_style_radius(cui_switch, 35, LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_color(cui_switch, lv_color_hex(0x0179FF), LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_switch, 255, LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_grad_color(cui_switch, lv_color_hex(0x00D4FF), LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_main_stop(cui_switch, 0, LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_grad_stop(cui_switch, 255, LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_grad_dir(cui_switch, LV_GRAD_DIR_HOR, LV_PART_INDICATOR | LV_STATE_CHECKED);

    lv_obj_set_style_radius(cui_switch, 35, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_switch, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_switch, 200, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(cui_switch, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(cui_switch, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(cui_switch, 200, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(cui_switch, LV_GRAD_DIR_VER, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_switch, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_switch, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_switch, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_switch, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_switch, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_switch, 250, LV_PART_KNOB | LV_STATE_CHECKED);
    lv_obj_set_style_pad_left(cui_switch, -2, LV_PART_KNOB | LV_STATE_CHECKED);
    lv_obj_set_style_pad_right(cui_switch, -2, LV_PART_KNOB | LV_STATE_CHECKED);
    lv_obj_set_style_pad_top(cui_switch, -2, LV_PART_KNOB | LV_STATE_CHECKED);
    lv_obj_set_style_pad_bottom(cui_switch, -2, LV_PART_KNOB | LV_STATE_CHECKED);

    lv_obj_t * cui_content;
    cui_content = lv_obj_create(cui_main);
    lv_obj_set_height(cui_content, 386);
    lv_obj_set_width(cui_content, lv_pct(100));
    lv_obj_set_align(cui_content, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(cui_content, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_content, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list;
    cui_list = lv_obj_create(cui_content);
    lv_obj_set_width(cui_list, lv_pct(100));
    lv_obj_set_height(cui_list, lv_pct(100));
    lv_obj_set_align(cui_list, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_list, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_set_style_bg_color(cui_list, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_paired_labe;
    cui_paired_labe = lv_label_create(cui_list);
    lv_obj_set_width(cui_paired_labe, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_paired_labe, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_paired_labe, LV_ALIGN_CENTER);
    lv_label_set_text(cui_paired_labe, "#UNPAIRED");
    lv_obj_set_style_text_color(cui_paired_labe, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_paired_labe, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_paired_labe, &ui_font_AlibabaPuHui18, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_paired_labe, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_paired_labe, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_paired_labe, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_paired_labe, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_paired_items;
    cui_paired_items = lv_obj_create(cui_list);
    lv_obj_set_width(cui_paired_items, lv_pct(100));
    lv_obj_set_height(cui_paired_items, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_paired_items, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_paired_items, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_paired_items, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_paired_items, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_paired_items, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_paired_items, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_unpaired_label;
    cui_unpaired_label = lv_label_create(cui_list);
    lv_obj_set_width(cui_unpaired_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_unpaired_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_unpaired_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_unpaired_label, "#UNPAIRED");
    lv_obj_set_style_text_color(cui_unpaired_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_unpaired_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_unpaired_label, &ui_font_AlibabaPuHui18, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_unpaired_label, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_unpaired_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_unpaired_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_unpaired_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_unpaired_items;
    cui_unpaired_items = lv_obj_create(cui_list);
    lv_obj_set_width(cui_unpaired_items, lv_pct(100));
    lv_obj_set_height(cui_unpaired_items, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_unpaired_items, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_unpaired_items, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_unpaired_items, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_unpaired_items, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_unpaired_items, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_unpaired_items, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_DIALOGVIEW2_NUM);
    children[UI_COMP_DIALOGVIEW2_DIALOGVIEW2] = cui_DialogView2;
    children[UI_COMP_DIALOGVIEW2_BG_PIC] = cui_bg_pic;
    children[UI_COMP_DIALOGVIEW2_MAIN] = cui_main;
    children[UI_COMP_DIALOGVIEW2_MAIN_HEAD] = cui_head;
    children[UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT] = cui_left;
    children[UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT_ICON] = cui_icon;
    children[UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT_ICON_IMAGE] = cui_image;
    children[UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT_LABEL] = cui_label;
    children[UI_COMP_DIALOGVIEW2_MAIN_HEAD_RIGHT] = cui_right;
    children[UI_COMP_DIALOGVIEW2_MAIN_HEAD_RIGHT_SWITCH] = cui_switch;
    children[UI_COMP_DIALOGVIEW2_MAIN_CONTENT] = cui_content;
    children[UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST] = cui_list;
    children[UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_PAIRED_LABE] = cui_paired_labe;
    children[UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_PAIRED_ITEMS] = cui_paired_items;
    children[UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_UNPAIRED_LABEL] = cui_unpaired_label;
    children[UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_UNPAIRED_ITEMS] = cui_unpaired_items;
    lv_obj_add_event_cb(cui_DialogView2, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_DialogView2, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_DialogView2_create_hook(cui_DialogView2);
    return cui_DialogView2;
}

