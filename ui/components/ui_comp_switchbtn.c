// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT SwitchBtn

lv_obj_t * ui_SwitchBtn_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_SwitchBtn;
    cui_SwitchBtn = lv_switch_create(comp_parent);
    lv_obj_set_width(cui_SwitchBtn, 35);
    lv_obj_set_height(cui_SwitchBtn, 18);
    lv_obj_set_x(cui_SwitchBtn, 343);
    lv_obj_set_y(cui_SwitchBtn, 278);
    lv_obj_set_align(cui_SwitchBtn, LV_ALIGN_TOP_MID);
    lv_obj_set_style_radius(cui_SwitchBtn, 35, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SwitchBtn, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SwitchBtn, 250, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(cui_SwitchBtn, 20, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_color(cui_SwitchBtn, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_SwitchBtn, 0, LV_PART_MAIN | LV_STATE_CHECKED);

    lv_obj_set_style_radius(cui_SwitchBtn, 35, LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_color(cui_SwitchBtn, lv_color_hex(0x0179FF), LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_SwitchBtn, 255, LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_grad_color(cui_SwitchBtn, lv_color_hex(0x00D4FF), LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_main_stop(cui_SwitchBtn, 0, LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_grad_stop(cui_SwitchBtn, 255, LV_PART_INDICATOR | LV_STATE_CHECKED);
    lv_obj_set_style_bg_grad_dir(cui_SwitchBtn, LV_GRAD_DIR_HOR, LV_PART_INDICATOR | LV_STATE_CHECKED);

    lv_obj_set_style_radius(cui_SwitchBtn, 35, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SwitchBtn, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SwitchBtn, 200, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(cui_SwitchBtn, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(cui_SwitchBtn, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(cui_SwitchBtn, 200, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(cui_SwitchBtn, LV_GRAD_DIR_VER, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_SwitchBtn, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_SwitchBtn, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_SwitchBtn, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_SwitchBtn, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SwitchBtn, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_SwitchBtn, 250, LV_PART_KNOB | LV_STATE_CHECKED);
    lv_obj_set_style_pad_left(cui_SwitchBtn, -2, LV_PART_KNOB | LV_STATE_CHECKED);
    lv_obj_set_style_pad_right(cui_SwitchBtn, -2, LV_PART_KNOB | LV_STATE_CHECKED);
    lv_obj_set_style_pad_top(cui_SwitchBtn, -2, LV_PART_KNOB | LV_STATE_CHECKED);
    lv_obj_set_style_pad_bottom(cui_SwitchBtn, -2, LV_PART_KNOB | LV_STATE_CHECKED);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SWITCHBTN_NUM);
    children[UI_COMP_SWITCHBTN_SWITCHBTN] = cui_SwitchBtn;
    lv_obj_add_event_cb(cui_SwitchBtn, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_SwitchBtn, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_SwitchBtn_create_hook(cui_SwitchBtn);
    return cui_SwitchBtn;
}

