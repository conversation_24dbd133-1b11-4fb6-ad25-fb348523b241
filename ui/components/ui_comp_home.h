// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_HOME_H
#define _UI_COMP_HOME_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT Home
#define UI_COMP_HOME_HOME 0
#define UI_COMP_HOME_HOME_LEFT 1
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT 2
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_L 3
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_L_STA_MAIN_RUN1 4
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FD 5
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FD_STA_ICON_R_8 6
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_F 7
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_F_STA_ICON_R_9 8
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FE 9
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FE_STA_ICON_R_10 10
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_R 11
#define UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_R_STA_ICON_R_6 12
#define UI_COMP_HOME_HOME_LEFT_RUN_MODE 13
#define UI_COMP_HOME_HOME_LEFT_RUN_MODE_IMG_CAR_MODEL 14
#define UI_COMP_HOME_HOME_LEFT_RUN_ANGLE 15
#define UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_PITCH_ANGLE 16
#define UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_ROLL_ANGLE 17
#define UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_ACCELERATION 18
#define UI_COMP_HOME_HOME_RIGHT 19
#define UI_COMP_HOME_HOME_RIGHT_RUN_NOS 20
#define UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_ICON 21
#define UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_ICON_STA_MAIN_NOS_ICON 22
#define UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_SPEED_BG 23
#define UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_SPEED_BG_STA_NOS_SPEED_VALUE 24
#define UI_COMP_HOME_HOME_RIGHT_RUN_SPEED 25
#define UI_COMP_HOME_HOME_RIGHT_RUN_SPEED_STA_RUN_SPEED_VALUE 26
#define UI_COMP_HOME_HOME_RIGHT_RUN_UNIT 27
#define UI_COMP_HOME_HOME_RIGHT_RUN_UNIT_SPEED_NAV_TITLE_VALUE 28
#define UI_COMP_HOME_HOME_RIGHT_RUN_UNIT_SPEED_NAV_LINE 29
#define UI_COMP_HOME_HOME_RIGHT_BATTERY_VAL 30
#define UI_COMP_HOME_HOME_RIGHT_BATTERY_INFO 31
#define UI_COMP_HOME_HOME_RIGHT_BATTERY_INFO_BATTERY_ICON 32
#define UI_COMP_HOME_HOME_RIGHT_BATTERY_INFO_BATTERY_LABEL 33
#define _UI_COMP_HOME_NUM 34
lv_obj_t * ui_Home_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
