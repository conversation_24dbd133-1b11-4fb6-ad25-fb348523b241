// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_SETTINGINFO_H
#define _UI_COMP_SETTINGINFO_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT SettingInfo
#define UI_COMP_SETTINGINFO_SETTINGINFO 0
#define UI_COMP_SETTINGINFO_INFO_ICON 1
#define UI_COMP_SETTINGINFO_INFO_LEFT 2
#define UI_COMP_SETTINGINFO_INFO_LEFT_INFO_LEFT_LABEL_VAL 3
#define UI_COMP_SETTINGINFO_INFO_SPACE 4
#define UI_COMP_SETTINGINFO_SPACELINE1 5
#define UI_COMP_SETTINGINFO_SPACELINE1_SPACELINE1_PANEL3 6
#define UI_COMP_SETTINGINFO_SPACELINE1_SPACELINE1_PANEL2 7
#define UI_COMP_SETTINGINFO_INFO_ICON2 8
#define UI_COMP_SETTINGINFO_INFO_RIGHT 9
#define UI_COMP_SETTINGINFO_INFO_RIGHT_INFO_RIGHT_LABEL_VAL 10
#define UI_COMP_SETTINGINFO_INFO_ICON_RIGHT 11
#define _UI_COMP_SETTINGINFO_NUM 12
lv_obj_t * ui_SettingInfo_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
