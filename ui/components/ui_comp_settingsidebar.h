// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_SETTINGSIDEBAR_H
#define _UI_COMP_SETTINGSIDEBAR_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT SettingSidebar
#define UI_COMP_SETTINGSIDEBAR_SETTINGSIDEBAR 0
#define UI_COMP_SETTINGSIDEBAR_SIDEBAR_HEAD 1
#define UI_COMP_SETTINGSIDEBAR_SIDEBAR_HEAD_SIDEBAR_LABEL 2
#define UI_COMP_SETTINGSIDEBAR_SIDEBAR_ITEMS 3
#define UI_COMP_SETTINGSIDEBAR_SETTINGBTN1 4
#define UI_COMP_SETTINGSIDEBAR_SETTINGBTN1_SETTINGBTN1_SYSTEM_LABEL 5
#define UI_COMP_SETTINGSIDEBAR_SETTINGBTN2 6
#define UI_COMP_SETTINGSIDEBAR_SETTINGBTN2_SETTINGBTN2_SYSTEM_LABEL2 7
#define UI_COMP_SETTINGSIDEBAR_SETTINGBTN3 8
#define UI_COMP_SETTINGSIDEBAR_SETTINGBTN3_SETTINGBTN3_SYSTEM_LABEL3 9
#define UI_COMP_SETTINGSIDEBAR_SPACELINE 10
#define UI_COMP_SETTINGSIDEBAR_SPACELINE_SPACELINE_PANEL3 11
#define UI_COMP_SETTINGSIDEBAR_SPACELINE_SPACELINE_PANEL2 12
#define _UI_COMP_SETTINGSIDEBAR_NUM 13
lv_obj_t * ui_SettingSidebar_create(lv_obj_t * comp_parent);
void ui_event_comp_SettingSidebar_Settingbtn1(lv_event_t * e);
void ui_event_comp_SettingSidebar_Settingbtn2(lv_event_t * e);
void ui_event_comp_SettingSidebar_Settingbtn3(lv_event_t * e);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
