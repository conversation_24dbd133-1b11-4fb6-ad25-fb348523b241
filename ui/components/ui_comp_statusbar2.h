// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_STATUSBAR2_H
#define _UI_COMP_STATUSBAR2_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT Statusbar2
#define UI_COMP_STATUSBAR2_STATUSBAR2 0
#define UI_COMP_STATUSBAR2_LEFT 1
#define UI_COMP_STATUSBAR2_LEFT_REAL 2
#define UI_COMP_STATUSBAR2_LEFT_REAL_STA 3
#define UI_COMP_STATUSBAR2_LEFT_REAL_STA_TIME 4
#define UI_COMP_STATUSBAR2_LEFT_REAL_STA_TEMP 5
#define UI_COMP_STATUSBAR2_LEFT_REAL_MODE 6
#define UI_COMP_STATUSBAR2_LEFT_REAL_MODE_MODE_LABEL 7
#define UI_COMP_STATUSBAR2_LEFT_REAL_GEAR 8
#define UI_COMP_STATUSBAR2_LEFT_REAL_GEAR_GEAR_LABEL 9
#define UI_COMP_STATUSBAR2_LEFT_REAL_DYNAMIC_L 10
#define UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER 11
#define UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_TRIP 12
#define UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_TRIP_TRIP_INFO 13
#define UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_POWER 14
#define UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_POWER_POWER_ICON 15
#define UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_POWER_POWER_LABEL 16
#define UI_COMP_STATUSBAR2_CENTER 17
#define UI_COMP_STATUSBAR2_RIGHT 18
#define UI_COMP_STATUSBAR2_RIGHT_NORMAL 19
#define UI_COMP_STATUSBAR2_RIGHT_NORMAL_DYNAMIC_R 20
#define UI_COMP_STATUSBAR2_RIGHT_NORMAL_POSITION 21
#define UI_COMP_STATUSBAR2_RIGHT_NORMAL_POSITION_LOCATION_ICON 22
#define UI_COMP_STATUSBAR2_RIGHT_NORMAL_POSITION_LOCATION_LABEL 23
#define UI_COMP_STATUSBAR2_RIGHT_NORMAL_BATTERY 24
#define UI_COMP_STATUSBAR2_RIGHT_NORMAL_BATTERY_BAT_ICON 25
#define UI_COMP_STATUSBAR2_RIGHT_NORMAL_BATTERY_BAT_LABEL 26
#define UI_COMP_STATUSBAR2_RIGHT_ODO 27
#define UI_COMP_STATUSBAR2_RIGHT_ODO_READY 28
#define UI_COMP_STATUSBAR2_RIGHT_ODO_ICON 29
#define UI_COMP_STATUSBAR2_RIGHT_ODO_LABEL 30
#define _UI_COMP_STATUSBAR2_NUM 31
lv_obj_t * ui_Statusbar2_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
