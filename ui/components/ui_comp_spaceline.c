// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT Spaceline

lv_obj_t * ui_Spaceline_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_Spaceline;
    cui_Spaceline = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_Spaceline, 1);
    lv_obj_set_height(cui_Spaceline, 300);
    lv_obj_set_x(cui_Spaceline, -212);
    lv_obj_set_y(cui_Spaceline, 50);
    lv_obj_set_align(cui_Spaceline, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_Spaceline, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_Spaceline, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_Spaceline, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Spaceline, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Spaceline, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Panel3;
    cui_Panel3 = lv_obj_create(cui_Spaceline);
    lv_obj_set_width(cui_Panel3, lv_pct(100));
    lv_obj_set_height(cui_Panel3, lv_pct(50));
    lv_obj_set_x(cui_Panel3, -125);
    lv_obj_set_y(cui_Panel3, -97);
    lv_obj_set_align(cui_Panel3, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_Panel3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Panel3, lv_color_hex(0x051433), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Panel3, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(cui_Panel3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(cui_Panel3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(cui_Panel3, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(cui_Panel3, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Panel2;
    cui_Panel2 = lv_obj_create(cui_Spaceline);
    lv_obj_set_width(cui_Panel2, 100);
    lv_obj_set_height(cui_Panel2, lv_pct(50));
    lv_obj_set_align(cui_Panel2, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_Panel2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(cui_Panel2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_Panel2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Panel2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(cui_Panel2, lv_color_hex(0x001835), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(cui_Panel2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(cui_Panel2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(cui_Panel2, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SPACELINE_NUM);
    children[UI_COMP_SPACELINE_SPACELINE] = cui_Spaceline;
    children[UI_COMP_SPACELINE_PANEL3] = cui_Panel3;
    children[UI_COMP_SPACELINE_PANEL2] = cui_Panel2;
    lv_obj_add_event_cb(cui_Spaceline, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_Spaceline, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_Spaceline_create_hook(cui_Spaceline);
    return cui_Spaceline;
}

