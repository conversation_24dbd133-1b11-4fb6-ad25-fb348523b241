// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT SettingItem

lv_obj_t * ui_SettingItem_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_SettingItem;
    cui_SettingItem = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_SettingItem, 313);
    lv_obj_set_height(cui_SettingItem, 95);
    lv_obj_set_align(cui_SettingItem, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_SettingItem, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_SettingItem, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_SettingItem, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_SettingItem, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SettingItem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_SettingItem, &ui_img_dark_ic_setting_btn_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_SettingItem, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_SettingItem, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_SettingItem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_SettingItem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_icon;
    cui_item_icon = lv_img_create(cui_SettingItem);
    lv_img_set_src(cui_item_icon, &ui_img_setting_ic_setting_wifi_png);
    lv_obj_set_width(cui_item_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_item_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_item_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_item_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_item_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_item_label;
    cui_item_label = lv_obj_create(cui_SettingItem);
    lv_obj_set_width(cui_item_label, 213);
    lv_obj_set_height(cui_item_label, 50);
    lv_obj_set_align(cui_item_label, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_label, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_item_label, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_label, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_label_left;
    cui_item_label_left = lv_obj_create(cui_item_label);
    lv_obj_set_height(cui_item_label_left, 50);
    lv_obj_set_width(cui_item_label_left, LV_SIZE_CONTENT);   /// 106
    lv_obj_set_x(cui_item_label_left, -25);
    lv_obj_set_y(cui_item_label_left, 4);
    lv_obj_set_align(cui_item_label_left, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_label_left, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_item_label_left, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_label_left, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_label_left, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_label_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_item_label_left, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_item_label_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_item_label_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_item_label_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_label_val;
    cui_item_label_val = lv_label_create(cui_item_label_left);
    lv_obj_set_width(cui_item_label_val, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_item_label_val, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_item_label_val, LV_ALIGN_CENTER);
    lv_label_set_text(cui_item_label_val, "#WIFI");
    lv_obj_set_style_text_color(cui_item_label_val, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_item_label_val, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_label_right;
    cui_item_label_right = lv_obj_create(cui_item_label);
    lv_obj_set_height(cui_item_label_right, 50);
    lv_obj_set_width(cui_item_label_right, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_item_label_right, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_label_right, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_item_label_right, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_label_right, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_label_right, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_label_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_sub;
    cui_list_item_sub = lv_label_create(cui_item_label_right);
    lv_obj_set_width(cui_list_item_sub, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_sub, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_sub, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_sub, "#ON");
    lv_obj_set_style_text_color(cui_list_item_sub, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_sub, 200, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_arrow_icon;
    cui_arrow_icon = lv_img_create(cui_SettingItem);
    lv_img_set_src(cui_arrow_icon, &ui_img_setting_ic_setting_arrow_png);
    lv_obj_set_width(cui_arrow_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_arrow_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_arrow_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_arrow_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_arrow_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SETTINGITEM_NUM);
    children[UI_COMP_SETTINGITEM_SETTINGITEM] = cui_SettingItem;
    children[UI_COMP_SETTINGITEM_ITEM_ICON] = cui_item_icon;
    children[UI_COMP_SETTINGITEM_ITEM_LABEL] = cui_item_label;
    children[UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_LEFT] = cui_item_label_left;
    children[UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_LEFT_ITEM_LABEL_VAL] = cui_item_label_val;
    children[UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_RIGHT] = cui_item_label_right;
    children[UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_RIGHT_LIST_ITEM_SUB] = cui_list_item_sub;
    children[UI_COMP_SETTINGITEM_ARROW_ICON] = cui_arrow_icon;
    lv_obj_add_event_cb(cui_SettingItem, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_SettingItem, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_SettingItem_create_hook(cui_SettingItem);
    return cui_SettingItem;
}

