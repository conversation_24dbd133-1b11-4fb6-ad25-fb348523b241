// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT SliderVolume

lv_obj_t * ui_SliderVolume_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_SliderVolume;
    cui_SliderVolume = lv_slider_create(comp_parent);
    lv_slider_set_value(cui_SliderVolume, 50, LV_ANIM_OFF);
    if(lv_slider_get_mode(cui_SliderVolume) == LV_SLIDER_MODE_RANGE) lv_slider_set_left_value(cui_SliderVolume, 0,
                                                                                                  LV_ANIM_OFF);
    lv_obj_set_width(cui_SliderVolume, 171);
    lv_obj_set_height(cui_SliderVolume, 12);
    lv_obj_set_x(cui_SliderVolume, 319);
    lv_obj_set_y(cui_SliderVolume, 47);
    lv_obj_set_align(cui_SliderVolume, LV_ALIGN_CENTER);
    lv_obj_set_style_radius(cui_SliderVolume, 35, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SliderVolume, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SliderVolume, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(cui_SliderVolume, lv_color_hex(0x25AAFC), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SliderVolume, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(cui_SliderVolume, lv_color_hex(0x0DE1F2), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(cui_SliderVolume, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(cui_SliderVolume, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(cui_SliderVolume, LV_GRAD_DIR_HOR, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    lv_obj_set_style_radius(cui_SliderVolume, 14, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SliderVolume, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SliderVolume, 255, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(cui_SliderVolume, lv_color_hex(0xFF005B), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(cui_SliderVolume, 255, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(cui_SliderVolume, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(cui_SliderVolume, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(cui_SliderVolume, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(cui_SliderVolume, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_SliderVolume, 2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_SliderVolume, 2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_SliderVolume, 2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_SliderVolume, 2, LV_PART_KNOB | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SLIDERVOLUME_NUM);
    children[UI_COMP_SLIDERVOLUME_SLIDERVOLUME] = cui_SliderVolume;
    lv_obj_add_event_cb(cui_SliderVolume, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_SliderVolume, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_SliderVolume_create_hook(cui_SliderVolume);
    return cui_SliderVolume;
}

