// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_LISTITEM_H
#define _UI_COMP_LISTITEM_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT Listitem
#define UI_COMP_LISTITEM_LISTITEM 0
#define UI_COMP_LISTITEM_LIST_ITEM_LEFT 1
#define UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9 2
#define UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9_IMAGE2 3
#define UI_COMP_LISTITEM_LIST_ITEM_LEFT_LABEL2 4
#define UI_COMP_LISTITEM_LIST_ITEM_RIGHT 5
#define UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC 6
#define UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_VALUE 7
#define UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL 8
#define UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL_IMAGE7 9
#define UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS 10
#define UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS_IMAGE3 11
#define _UI_COMP_LISTITEM_NUM 12
lv_obj_t * ui_Listitem_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
