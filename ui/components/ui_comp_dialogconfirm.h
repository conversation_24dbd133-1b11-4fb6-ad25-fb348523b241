// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_DIALOGCONFIRM_H
#define _UI_COMP_DIALOGCONFIRM_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT DialogConfirm
#define UI_COMP_DIALOGCONFIRM_DIALOGCONFIRM 0
#define UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT 1
#define UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_TITLE 2
#define UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_TITLE_WIFI_NAME_LABEL 3
#define UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_TIPS 4
#define UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_TIPS_WIFI_CON_TIPS 5
#define UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_TEXTAREA 6
#define UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_CONFIRM_BTN 7
#define UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_CONFIRM_BTN_DIALOG_CONFIRM_BTN_LABEL 8
#define UI_COMP_DIALOGCONFIRM_DIALOG_CLOSE 9
#define _UI_COMP_DIALOGCONFIRM_NUM 10
lv_obj_t * ui_DialogConfirm_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
