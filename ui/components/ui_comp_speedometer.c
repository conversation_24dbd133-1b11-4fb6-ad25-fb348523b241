// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT Speedometer

lv_obj_t * ui_Speedometer_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_Speedometer;
    cui_Speedometer = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_Speedometer, 390);
    lv_obj_set_height(cui_Speedometer, lv_pct(100));
    lv_obj_set_x(cui_Speedometer, -21);
    lv_obj_set_y(cui_Speedometer, 5);
    lv_obj_set_align(cui_Speedometer, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_Speedometer, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Speedometer, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Speedometer, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_CENTER;
    cui_CENTER = lv_obj_create(cui_Speedometer);
    lv_obj_set_width(cui_CENTER, 270);
    lv_obj_set_height(cui_CENTER, lv_pct(100));
    lv_obj_set_align(cui_CENTER, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_CENTER, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_CENTER, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_CENTER, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_CENTER, 20, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_CENTER, 20, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_CENTER, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_CENTER, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_LEFT;
    cui_LEFT = lv_obj_create(cui_CENTER);
    lv_obj_set_width(cui_LEFT, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_LEFT, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_flex_flow(cui_LEFT, LV_FLEX_FLOW_ROW_REVERSE);
    lv_obj_set_flex_align(cui_LEFT, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_LEFT, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_LEFT, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_LEFT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_LEFT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_LEFT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_LEFT, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_LEFT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_LEFT, 20, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_LEFT, 20, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_LEFT, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_LEFT, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_turn_left;
    cui_turn_left = lv_img_create(cui_LEFT);
    lv_img_set_src(cui_turn_left, &ui_img_dark_ic_sta_signals_l_png);
    lv_obj_set_width(cui_turn_left, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_turn_left, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_turn_left, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_turn_left, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_turn_left, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_RIGHT;
    cui_RIGHT = lv_obj_create(cui_CENTER);
    lv_obj_set_width(cui_RIGHT, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_RIGHT, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_RIGHT, LV_ALIGN_TOP_RIGHT);
    lv_obj_set_flex_flow(cui_RIGHT, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_RIGHT, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_RIGHT, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_RIGHT, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_RIGHT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_RIGHT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_RIGHT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_RIGHT, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_RIGHT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_turn_right;
    cui_turn_right = lv_img_create(cui_RIGHT);
    lv_img_set_src(cui_turn_right, &ui_img_dark_ic_sta_signals_r_png);
    lv_obj_set_width(cui_turn_right, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_turn_right, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_turn_right, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_turn_right, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_turn_right, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_SPEND;
    cui_SPEND = lv_obj_create(cui_CENTER);
    lv_obj_set_height(cui_SPEND, lv_pct(100));
    lv_obj_set_width(cui_SPEND, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_SPEND, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_SPEND, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_SPEND, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_SPEND, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_SPEND, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SPEND, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_speed_label;
    cui_speed_label = lv_label_create(cui_SPEND);
    lv_obj_set_width(cui_speed_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_speed_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_speed_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_speed_label, "100");
    lv_obj_set_style_text_color(cui_speed_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_speed_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_speed_label, &ui_font_BrunoAceSCRegular80, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_UNIT;
    cui_UNIT = lv_obj_create(cui_CENTER);
    lv_obj_set_width(cui_UNIT, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_UNIT, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_UNIT, 0);
    lv_obj_set_y(cui_UNIT, -5);
    lv_obj_set_align(cui_UNIT, LV_ALIGN_BOTTOM_RIGHT);
    lv_obj_clear_flag(cui_UNIT, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_UNIT, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_UNIT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_unit_label;
    cui_unit_label = lv_label_create(cui_UNIT);
    lv_obj_set_width(cui_unit_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_unit_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_unit_label, LV_ALIGN_BOTTOM_RIGHT);
    lv_label_set_text(cui_unit_label, "Km/h");
    lv_obj_set_style_text_color(cui_unit_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_unit_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_unit_label, &ui_font_AlibabaPuHui24, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_unit_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_unit_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_unit_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_unit_label, 5, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_ICON_L;
    cui_ICON_L = lv_obj_create(cui_Speedometer);
    lv_obj_set_width(cui_ICON_L, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_ICON_L, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_ICON_L, 40);
    lv_obj_set_y(cui_ICON_L, 0);
    lv_obj_clear_flag(cui_ICON_L, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_ICON_L, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_ICON_L, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_ICON_L, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_ICON_L, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_ICON_L, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_ICON_L, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_ICON_R;
    cui_ICON_R = lv_obj_create(cui_Speedometer);
    lv_obj_set_width(cui_ICON_R, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_ICON_R, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_ICON_R, 345);
    lv_obj_set_y(cui_ICON_R, 0);
    lv_obj_clear_flag(cui_ICON_R, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_ICON_R, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_ICON_R, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_ICON_R, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_ICON_R, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_ICON_R, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_ICON_R, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SPEEDOMETER_NUM);
    children[UI_COMP_SPEEDOMETER_SPEEDOMETER] = cui_Speedometer;
    children[UI_COMP_SPEEDOMETER_CENTER] = cui_CENTER;
    children[UI_COMP_SPEEDOMETER_CENTER_LEFT] = cui_LEFT;
    children[UI_COMP_SPEEDOMETER_CENTER_LEFT_TURN_LEFT] = cui_turn_left;
    children[UI_COMP_SPEEDOMETER_CENTER_RIGHT] = cui_RIGHT;
    children[UI_COMP_SPEEDOMETER_CENTER_RIGHT_TURN_RIGHT] = cui_turn_right;
    children[UI_COMP_SPEEDOMETER_CENTER_SPEND] = cui_SPEND;
    children[UI_COMP_SPEEDOMETER_CENTER_SPEND_SPEED_LABEL] = cui_speed_label;
    children[UI_COMP_SPEEDOMETER_CENTER_UNIT] = cui_UNIT;
    children[UI_COMP_SPEEDOMETER_CENTER_UNIT_UNIT_LABEL] = cui_unit_label;
    children[UI_COMP_SPEEDOMETER_ICON_L] = cui_ICON_L;
    children[UI_COMP_SPEEDOMETER_ICON_R] = cui_ICON_R;
    lv_obj_add_event_cb(cui_Speedometer, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_Speedometer, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_Speedometer_create_hook(cui_Speedometer);
    return cui_Speedometer;
}

