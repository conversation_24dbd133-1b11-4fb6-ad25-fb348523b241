// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT SettingInfo

lv_obj_t * ui_SettingInfo_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_SettingInfo;
    cui_SettingInfo = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_SettingInfo, 643);
    lv_obj_set_height(cui_SettingInfo, lv_pct(100));
    lv_obj_set_align(cui_SettingInfo, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_SettingInfo, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_SettingInfo, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_SettingInfo, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_SettingInfo, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SettingInfo, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_SettingInfo, &ui_img_dark_ic_setting_btn_xl_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_SettingInfo, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_SettingInfo, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_SettingInfo, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_SettingInfo, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_info_icon;
    cui_info_icon = lv_img_create(cui_SettingInfo);
    lv_img_set_src(cui_info_icon, &ui_img_setting_ic_setting_dashbord_png);
    lv_obj_set_width(cui_info_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_info_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_info_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_info_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_info_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_info_left;
    cui_info_left = lv_obj_create(cui_SettingInfo);
    lv_obj_set_width(cui_info_left, 245);
    lv_obj_set_height(cui_info_left, 50);
    lv_obj_set_align(cui_info_left, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_info_left, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_info_left, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_info_left, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_info_left, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_info_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_info_left, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_info_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_info_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_info_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_info_left_label_val;
    cui_info_left_label_val = lv_label_create(cui_info_left);
    lv_obj_set_width(cui_info_left_label_val, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_info_left_label_val, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_info_left_label_val, LV_ALIGN_CENTER);
    lv_label_set_text(cui_info_left_label_val, "#ODO");
    lv_obj_set_style_text_color(cui_info_left_label_val, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_info_left_label_val, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_info_space;
    cui_info_space = lv_obj_create(cui_SettingInfo);
    lv_obj_set_width(cui_info_space, 45);
    lv_obj_set_height(cui_info_space, lv_pct(100));
    lv_obj_set_x(cui_info_space, 74);
    lv_obj_set_y(cui_info_space, 1);
    lv_obj_set_align(cui_info_space, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_info_space, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_info_space, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_info_space, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_info_space, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_info_space, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_info_space, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_info_space, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Spaceline1;
    cui_Spaceline1 = ui_Spaceline_create(cui_info_space);
    lv_obj_set_width(cui_Spaceline1, 1);
    lv_obj_set_height(cui_Spaceline1, 32);
    lv_obj_set_x(cui_Spaceline1, 0);
    lv_obj_set_y(cui_Spaceline1, 0);

    lv_obj_set_style_bg_color(ui_comp_get_child(cui_Spaceline1, UI_COMP_SPACELINE_PANEL3), lv_color_hex(0x072B66),
                              LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_comp_get_child(cui_Spaceline1, UI_COMP_SPACELINE_PANEL3), 240,
                            LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(ui_comp_get_child(cui_Spaceline1, UI_COMP_SPACELINE_PANEL3), 0,
                                  LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(ui_comp_get_child(cui_Spaceline1, UI_COMP_SPACELINE_PANEL3), 220,
                                  LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_comp_get_child(cui_Spaceline1, UI_COMP_SPACELINE_PANEL2), lv_color_hex(0xFFFFFF),
                              LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_comp_get_child(cui_Spaceline1, UI_COMP_SPACELINE_PANEL2), 255,
                            LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_comp_get_child(cui_Spaceline1, UI_COMP_SPACELINE_PANEL2), lv_color_hex(0x014DAA),
                                   LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_info_icon2;
    cui_info_icon2 = lv_img_create(cui_SettingInfo);
    lv_img_set_src(cui_info_icon2, &ui_img_setting_ic_setting_lan_png);
    lv_obj_set_width(cui_info_icon2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_info_icon2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_info_icon2, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_info_icon2, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_info_icon2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_info_right;
    cui_info_right = lv_obj_create(cui_SettingInfo);
    lv_obj_set_width(cui_info_right, 228);
    lv_obj_set_height(cui_info_right, 50);
    lv_obj_set_align(cui_info_right, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_info_right, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_info_right, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_info_right, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_info_right, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_info_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_info_right, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_info_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_info_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_info_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_info_right, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_info_right, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_info_right, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_info_right, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_info_right_label_val;
    cui_info_right_label_val = lv_label_create(cui_info_right);
    lv_obj_set_width(cui_info_right_label_val, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_info_right_label_val, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_info_right_label_val, LV_ALIGN_CENTER);
    lv_label_set_text(cui_info_right_label_val, "#SUB_MILEAGE");
    lv_obj_set_style_text_color(cui_info_right_label_val, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_info_right_label_val, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_info_icon_right;
    cui_info_icon_right = lv_img_create(cui_SettingInfo);
    lv_img_set_src(cui_info_icon_right, &ui_img_setting_ic_setting_reset_png);
    lv_obj_set_width(cui_info_icon_right, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_info_icon_right, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_info_icon_right, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_info_icon_right, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_info_icon_right, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SETTINGINFO_NUM);
    children[UI_COMP_SETTINGINFO_SETTINGINFO] = cui_SettingInfo;
    children[UI_COMP_SETTINGINFO_INFO_ICON] = cui_info_icon;
    children[UI_COMP_SETTINGINFO_INFO_LEFT] = cui_info_left;
    children[UI_COMP_SETTINGINFO_INFO_LEFT_INFO_LEFT_LABEL_VAL] = cui_info_left_label_val;
    children[UI_COMP_SETTINGINFO_INFO_SPACE] = cui_info_space;
    children[UI_COMP_SETTINGINFO_SPACELINE1] = cui_Spaceline1;
    children[UI_COMP_SETTINGINFO_SPACELINE1_SPACELINE1_PANEL3] = ui_comp_get_child(cui_Spaceline1,
                                                                                   UI_COMP_SPACELINE_PANEL3);
    children[UI_COMP_SETTINGINFO_SPACELINE1_SPACELINE1_PANEL2] = ui_comp_get_child(cui_Spaceline1,
                                                                                   UI_COMP_SPACELINE_PANEL2);
    children[UI_COMP_SETTINGINFO_INFO_ICON2] = cui_info_icon2;
    children[UI_COMP_SETTINGINFO_INFO_RIGHT] = cui_info_right;
    children[UI_COMP_SETTINGINFO_INFO_RIGHT_INFO_RIGHT_LABEL_VAL] = cui_info_right_label_val;
    children[UI_COMP_SETTINGINFO_INFO_ICON_RIGHT] = cui_info_icon_right;
    lv_obj_add_event_cb(cui_SettingInfo, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_SettingInfo, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_SettingInfo_create_hook(cui_SettingInfo);
    return cui_SettingInfo;
}

