// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP__H
#define _UI_COMP__H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

void get_component_child_event_cb(lv_event_t * e);
void del_component_child_event_cb(lv_event_t * e);

lv_obj_t * ui_comp_get_child(lv_obj_t * comp, uint32_t child_idx);
extern uint32_t LV_EVENT_GET_COMP_CHILD;
#include "ui_comp_deviceitem.h"
#include "ui_comp_deviceitemdark.h"
#include "ui_comp_dialogconfirm.h"
#include "ui_comp_dialogview.h"
#include "ui_comp_dialogview2.h"
#include "ui_comp_hicar.h"
#include "ui_comp_home.h"
#include "ui_comp_infolarge.h"
#include "ui_comp_itemlist.h"
#include "ui_comp_listempty.h"
#include "ui_comp_listitem.h"
#include "ui_comp_setting.h"
#include "ui_comp_settingbtn.h"
#include "ui_comp_settinginfo.h"
#include "ui_comp_settingitem.h"
#include "ui_comp_settingsidebar.h"
#include "ui_comp_settingvolume.h"
#include "ui_comp_sidebar.h"
#include "ui_comp_sidebarbtn.h"
#include "ui_comp_slidervolume.h"
#include "ui_comp_spaceline.h"
#include "ui_comp_speedometer.h"
#include "ui_comp_staicon.h"
#include "ui_comp_statusbar2.h"
#include "ui_comp_switchbtn.h"
#include "ui_comp_systemitem.h"
#include "ui_comp_toastmsg.h"
#include "ui_comp_viewstate.h"

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
