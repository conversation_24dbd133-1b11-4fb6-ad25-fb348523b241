// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT DeviceItem

lv_obj_t * ui_DeviceItem_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_DeviceItem;
    cui_DeviceItem = lv_obj_create(comp_parent);
    lv_obj_set_height(cui_DeviceItem, 68);
    lv_obj_set_width(cui_DeviceItem, lv_pct(100));
    lv_obj_set_flex_flow(cui_DeviceItem, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_DeviceItem, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_DeviceItem, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_DeviceItem, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_DeviceItem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_DeviceItem, &ui_img_dialog_list_item_normal_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_DeviceItem, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_DeviceItem, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_DeviceItem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_DeviceItem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_DeviceItem, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_DeviceItem, 0, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_img_src(cui_DeviceItem, &ui_img_dialog_list_item_active_png, LV_PART_MAIN | LV_STATE_CHECKED);

    lv_obj_t * cui_left;
    cui_left = lv_obj_create(cui_DeviceItem);
    lv_obj_set_height(cui_left, lv_pct(100));
    lv_obj_set_width(cui_left, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_left, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_left, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_left, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_left, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_left, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_icon;
    cui_icon = lv_obj_create(cui_left);
    lv_obj_set_width(cui_icon, 50);
    lv_obj_set_height(cui_icon, 50);
    lv_obj_set_align(cui_icon, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_icon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_image;
    cui_image = lv_img_create(cui_icon);
    lv_img_set_src(cui_image, &ui_img_dialog_ic_bluetooth_png);
    lv_obj_set_width(cui_image, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_image, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_image, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_image, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_image, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_label;
    cui_label = lv_label_create(cui_left);
    lv_obj_set_width(cui_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_label, "TextTextTextTextTextText");
    lv_obj_set_style_text_color(cui_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_label, &ui_font_AlibabaPuHui18, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_label, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_right;
    cui_right = lv_obj_create(cui_DeviceItem);
    lv_obj_set_height(cui_right, lv_pct(100));
    lv_obj_set_width(cui_right, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_right, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_right, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_right, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_right, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_right, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_group;
    cui_group = lv_obj_create(cui_right);
    lv_obj_set_width(cui_group, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_group, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_group, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_group, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_group, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_group, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_group, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_group, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_desc;
    cui_desc = lv_label_create(cui_group);
    lv_obj_set_width(cui_desc, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_desc, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_desc, -1);
    lv_obj_set_y(cui_desc, 0);
    lv_obj_set_align(cui_desc, LV_ALIGN_CENTER);
    lv_label_set_text(cui_desc, "#CONNECTED");
    lv_obj_set_style_text_color(cui_desc, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_desc, 200, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_desc, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_desc, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_desc, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_desc, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_icon1;
    cui_icon1 = lv_obj_create(cui_group);
    lv_obj_set_width(cui_icon1, 50);
    lv_obj_set_height(cui_icon1, 50);
    lv_obj_set_align(cui_icon1, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_icon1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_icon1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_icon1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_image1;
    cui_image1 = lv_img_create(cui_icon1);
    lv_img_set_src(cui_image1, &ui_img_dialog_list_item_checked_png);
    lv_obj_set_width(cui_image1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_image1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_image1, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_image1, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_image1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_icon2;
    cui_icon2 = lv_obj_create(cui_group);
    lv_obj_set_width(cui_icon2, 50);
    lv_obj_set_height(cui_icon2, 50);
    lv_obj_set_align(cui_icon2, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_icon2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_icon2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_icon2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_image2;
    cui_image2 = lv_img_create(cui_icon2);
    lv_img_set_src(cui_image2, &ui_img_dialog_list_item_more_png);
    lv_obj_set_width(cui_image2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_image2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_image2, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_image2, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_image2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_DEVICEITEM_NUM);
    children[UI_COMP_DEVICEITEM_DEVICEITEM] = cui_DeviceItem;
    children[UI_COMP_DEVICEITEM_LEFT] = cui_left;
    children[UI_COMP_DEVICEITEM_LEFT_ICON] = cui_icon;
    children[UI_COMP_DEVICEITEM_LEFT_ICON_IMAGE] = cui_image;
    children[UI_COMP_DEVICEITEM_LEFT_LABEL] = cui_label;
    children[UI_COMP_DEVICEITEM_RIGHT] = cui_right;
    children[UI_COMP_DEVICEITEM_RIGHT_GROUP] = cui_group;
    children[UI_COMP_DEVICEITEM_RIGHT_GROUP_DESC] = cui_desc;
    children[UI_COMP_DEVICEITEM_RIGHT_GROUP_ICON1] = cui_icon1;
    children[UI_COMP_DEVICEITEM_RIGHT_GROUP_ICON1_IMAGE1] = cui_image1;
    children[UI_COMP_DEVICEITEM_RIGHT_GROUP_ICON2] = cui_icon2;
    children[UI_COMP_DEVICEITEM_RIGHT_GROUP_ICON2_IMAGE2] = cui_image2;
    lv_obj_add_event_cb(cui_DeviceItem, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_DeviceItem, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_DeviceItem_create_hook(cui_DeviceItem);
    return cui_DeviceItem;
}

