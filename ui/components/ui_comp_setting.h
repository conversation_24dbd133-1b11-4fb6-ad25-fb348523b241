// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.5.1
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_SETTING_H
#define _UI_COMP_SETTING_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT Setting
#define UI_COMP_SETTING_SETTING 0
#define UI_COMP_SETTING_SETTING_WARP_LEFT 1
#define UI_COMP_SETTING_SETTING_WARP_LEFT_SETTING_WARP_LEFT_LABEL 2
#define UI_COMP_SETTING_SETTING_WARP_LEFT_SETTING_WARP_LEFT_LABEL_SETTING_TITLE_LABEL 3
#define UI_COMP_SETTING_SETTING_WARP_LEFT_SETTING_WARP_LEFT_MENUS 4
#define UI_COMP_SETTING_SETTINGBTN1 5
#define UI_COMP_SETTING_SETTINGBTN1_SETTINGBTN1_SYSTEM_LABEL 6
#define UI_COMP_SETTING_SETTINGBTN2 7
#define UI_COMP_SETTING_SETTINGBTN2_SETTINGBTN2_SYSTEM_LABEL2 8
#define UI_COMP_SETTING_SETTINGBTN3 9
#define UI_COMP_SETTING_SETTINGBTN3_SETTINGBTN3_SYSTEM_LABEL3 10
#define UI_COMP_SETTING_SPACELINE 11
#define UI_COMP_SETTING_SPACELINE_SPACELINE_PANEL3 12
#define UI_COMP_SETTING_SPACELINE_SPACELINE_PANEL2 13
#define UI_COMP_SETTING_SETTING_WARP_RIGHT 14
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP 15
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1 16
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5 17
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_ICON9 18
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_LABEL9 19
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_LABEL9_LIST_ITEM_LABEL_LEFT9 20
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_LABEL9_LIST_ITEM_LABEL_LEFT9_LIST_ITEM_LABEL_VALUE9 21
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_LABEL9_LIST_ITEM_LABEL_RIGHT9 22
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_LABEL9_LIST_ITEM_LABEL_RIGHT9_LIST_ITEM_LABEL_EXTEND9 23
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_ARROW9 24
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2 25
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11 26
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_ICON2 27
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_LABEL2 28
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_LABEL2_LIST_ITEM_LABEL_LEFT2 29
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_LABEL2_LIST_ITEM_LABEL_LEFT2_LIST_ITEM_LABEL_VALUE2 30
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_LABEL2_LIST_ITEM_LABEL_RIGHT2 31
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_LABEL2_LIST_ITEM_LABEL_RIGHT2_LIST_ITEM_LABEL_EXTEND2 32
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_ARROW2 33
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3 34
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13 35
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_ICON6 36
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6 37
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_LEFT6 38
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_LEFT6_SLIDERVOLUME2 39
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_LEFT6_LIST_ITEM_LABEL_VALUE6 40
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_RIGHT6 41
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_RIGHT6_LIST_ITEM_LABEL_EXTEND6 42
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_ARROW6 43
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4 44
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15 45
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_ICON7 46
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_LABEL7 47
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_LABEL7_LIST_ITEM_LABEL_LEFT7 48
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_LABEL7_LIST_ITEM_LABEL_LEFT7_LIST_ITEM_LABEL_VALUE7 49
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_LABEL7_LIST_ITEM_LABEL_RIGHT7 50
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_LABEL7_LIST_ITEM_LABEL_RIGHT7_LIST_ITEM_LABEL_EXTEND7 51
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_ARROW7 52
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP 53
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1 54
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1 55
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_ICON8 56
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_LABEL8 57
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_LABEL8_LIST_ITEM_LABEL_LEFT8 58
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_LABEL8_LIST_ITEM_LABEL_LEFT8_LIST_ITEM_LABEL_VALUE8 59
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_LABEL8_LIST_ITEM_LABEL_RIGHT8 60
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_LABEL8_LIST_ITEM_LABEL_RIGHT8_LIST_ITEM_LABEL_EXTEND8 61
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_ARROW8 62
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2 63
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3 64
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_ICON1 65
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_LABEL1 66
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_LABEL1_LIST_ITEM_LABEL_LEFT1 67
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_LABEL1_LIST_ITEM_LABEL_LEFT1_LIST_ITEM_LABEL_VALUE1 68
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_LABEL1_LIST_ITEM_LABEL_RIGHT1 69
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_LABEL1_LIST_ITEM_LABEL_RIGHT1_LIST_ITEM_LABEL_EXTEND1 70
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_ARROW1 71
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3 72
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6 73
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_ICON3 74
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_LABEL3 75
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_LABEL3_LIST_ITEM_LABEL_VALUE3 76
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_PANEL4 77
#define UI_COMP_SETTING_SPACELINE2 78
#define UI_COMP_SETTING_SPACELINE2_SPACELINE2_PANEL3 79
#define UI_COMP_SETTING_SPACELINE2_SPACELINE2_PANEL2 80
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_ICON14 81
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_LABEL12 82
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_LABEL12_LIST_ITEM_LABEL_VALUE12 83
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_ICON12 84
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP 85
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1 86
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4 87
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4_LIST_ITEM_ICON17 88
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4_LIST_ITEM_LABEL15 89
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4_LIST_ITEM_LABEL15_LIST_ITEM_LABEL_VALUE15 90
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4_LIST_ITEM_LABEL4 91
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4_LIST_ITEM_LABEL4_LIST_ITEM_LABEL_VALUE4 92
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2 93
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7 94
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7_LIST_ITEM_ICON5 95
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7_LIST_ITEM_LABEL11 96
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7_LIST_ITEM_LABEL11_LIST_ITEM_LABEL_VALUE11 97
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7_LIST_ITEM_LABEL5 98
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7_LIST_ITEM_LABEL5_LIST_ITEM_LABEL_VALUE5 99
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3 100
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8 101
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_ICON10 102
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_LABEL13 103
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_LABEL13_LIST_ITEM_LABEL_VALUE13 104
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_LABEL14 105
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_LABEL14_LIST_ITEM_LABEL_VALUE14 106
#define UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_ARROW4 107
#define _UI_COMP_SETTING_NUM 108
lv_obj_t * ui_Setting_create(lv_obj_t * comp_parent);
void ui_event_comp_Setting_Settingbtn1(lv_event_t * e);
void ui_event_comp_Setting_Settingbtn2(lv_event_t * e);
void ui_event_comp_Setting_Settingbtn3(lv_event_t * e);
void ui_event_comp_Setting_settinglistitem5(lv_event_t * e);
void ui_event_comp_Setting_list_item_label9(lv_event_t * e);
void ui_event_comp_Setting_settinglistitem11(lv_event_t * e);
void ui_event_comp_Setting_list_item_label2(lv_event_t * e);
void ui_event_comp_Setting_SliderVolume2(lv_event_t * e);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
