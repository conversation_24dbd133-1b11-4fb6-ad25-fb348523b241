#
# Makefile - 优化版本
#
# ⭐快速编译命令： make -j$(nproc)
# ⭐完全重新编译： make clean-all && make -j$(nproc)
# ⭐只清理应用代码： make clean && make -j$(nproc)

#CC ?= gcc
CC := arm-linux-gnueabi-gcc	# 注意配置工具链！
AR := arm-linux-gnueabi-ar
LVGL_DIR_NAME ?= lvgl
LVGL_DIR ?= .
CFLAGS ?= -O3 -g0 -I$(LVGL_DIR)/ -Wall -Wshadow -Wundef -Wmissing-prototypes -Wno-discarded-qualifiers -Wall -Wextra -Wno-unused-function -Wno-error=strict-prototypes -Wpointer-arith -fno-strict-aliasing -Wno-error=cpp -Wuninitialized -Wmaybe-uninitialized -Wno-unused-parameter -Wno-missing-field-initializers -Wtype-limits -Wsizeof-pointer-memaccess -Wno-format-nonliteral -Wno-cast-qual -Wunreachable-code -Wno-switch-default -Wreturn-type -Wmultichar -Wformat-security -Wno-ignored-qualifiers -Wno-error=pedantic -Wno-sign-compare -Wno-error=missing-prototypes -Wdouble-promotion -Wclobbered -Wdeprecated -Wempty-body -Wtype-limits -Wstack-usage=2048 -Wno-unused-value -Wno-unused-parameter -Wno-missing-field-initializers -Wuninitialized -Wmaybe-uninitialized -Wall -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -Wtype-limits -Wsizeof-pointer-memaccess -Wno-format-nonliteral -Wpointer-arith -Wno-cast-qual -Wmissing-prototypes -Wunreachable-code -Wno-switch-default -Wreturn-type -Wmultichar -Wno-discarded-qualifiers -Wformat-security -Wno-ignored-qualifiers -Wno-sign-compare
LDFLAGS ?= -lm -lpthread -lrt

# SSH部署配置
SSH_HOST ?= ***************
SSH_USER ?= root
SSH_PORT ?= 22
SSH_TARGET_DIR ?= /root/
SSH_KEY ?=
SSH_OPTS ?= -o StrictHostKeyChecking=no

# 目标文件路径
OBJ_DIR := obj
BIN_DIR := bin
LIB_DIR := lib

# 目标应用
BIN = ebike_x1

# 应用源文件
MAINSRC = ./main.c

# 引入APP SDK
SDK_LIB = $(LVGL_DIR)/app/lib
SDK_INC = $(LVGL_DIR)/app/lib/include
SDK_NAME = ebike-utils

# SDK编译参数
CFLAGS += -I$(SDK_INC)
LDFLAGS += -L$(SDK_LIB) -l$(SDK_NAME) -Wl,-rpath,'$$ORIGIN' -Wl,-rpath,'$$ORIGIN/$(SDK_LIB)' -Wl,-rpath-link,'$$ORIGIN'

# 事件系统
EVENT_BUS_SRCS = $(wildcard $(LVGL_DIR)/event_bus/src/*.c)
CFLAGS += -I$(LVGL_DIR)/event_bus/include

# 启用调试模式
CFLAGS += -DPLUG_UART_DEBUG -DVEHICLE_DATA_DEBUG
#-DUI_COMP_MGR_DEBUG -DVEHICLE_DATA_DEBUG

# 增加头文件自动依赖追踪
CFLAGS += -MMD -MP

# 静态库文件
LVGL_LIB = $(LIB_DIR)/liblvgl.a
DRIVERS_LIB = $(LIB_DIR)/liblv_drivers.a
UI_LIB = $(LIB_DIR)/libui.a

# 引入编译文件
include $(LVGL_DIR)/lvgl/lvgl.mk
include $(LVGL_DIR)/lv_drivers/lv_drivers.mk
include $(LVGL_DIR)/ui/ui.mk
include $(LVGL_DIR)/app/ebike_x1.mk

# 添加事件系统源文件
CSRCS += $(EVENT_BUS_SRCS)

# 分离LVGL相关源文件（用于静态库）
LVGL_SRCS = $(filter $(LVGL_DIR)/lvgl/%,$(CSRCS))
DRIVERS_SRCS = $(filter $(LVGL_DIR)/lv_drivers/%,$(CSRCS))
UI_SRCS = $(filter $(LVGL_DIR)/ui/%,$(CSRCS))
APP_SRCS = $(filter-out $(LVGL_DIR)/lvgl/% $(LVGL_DIR)/lv_drivers/% $(LVGL_DIR)/ui/%,$(CSRCS))

OBJEXT ?= .o

# 生成各模块的目标文件列表
LVGL_OBJS = $(patsubst %.c,$(OBJ_DIR)/%.o,$(LVGL_SRCS))
DRIVERS_OBJS = $(patsubst %.c,$(OBJ_DIR)/%.o,$(DRIVERS_SRCS))
UI_OBJS = $(patsubst %.c,$(OBJ_DIR)/%.o,$(UI_SRCS))
APP_OBJS = $(patsubst %.c,$(OBJ_DIR)/%.o,$(APP_SRCS))
ALL_OBJS = $(patsubst %.c,$(OBJ_DIR)/%.o,$(CSRCS))
MAINOBJ = $(patsubst %.c,$(OBJ_DIR)/%.o,$(MAINSRC))

# 自动包含所有依赖文件
-include $(ALL_OBJS:.o=.d) $(MAINOBJ:.o=.d)

all: default

# 创建目录
$(OBJ_DIR) $(BIN_DIR) $(LIB_DIR):
	@mkdir -p $@

# 编译规则
$(OBJ_DIR)/%.o: %.c | $(OBJ_DIR)
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) -c $< -o $@
	@echo "CC $<"

# LVGL静态库
$(LVGL_LIB): $(LVGL_OBJS) | $(LIB_DIR)
	@echo "创建LVGL静态库..."
	$(AR) rcs $@ $^
	@echo "LVGL静态库创建完成: $@"

# Drivers静态库
$(DRIVERS_LIB): $(DRIVERS_OBJS) | $(LIB_DIR)
	@echo "创建Drivers静态库..."
	$(AR) rcs $@ $^
	@echo "Drivers静态库创建完成: $@"

# UI静态库
$(UI_LIB): $(UI_OBJS) | $(LIB_DIR)
	@echo "创建UI静态库..."
	$(AR) rcs $@ $^
	@echo "UI静态库创建完成: $@"

# 智能编译：优先使用静态库，如果不存在则使用传统方式
default: | $(BIN_DIR)
	@if [ -f "$(LVGL_LIB)" ] && [ -f "$(DRIVERS_LIB)" ] && [ -f "$(UI_LIB)" ]; then \
		echo "🚀 使用静态库快速编译..."; \
		$(MAKE) fast-build; \
	else \
		echo "🔄 首次编译或库文件缺失，创建静态库..."; \
		$(MAKE) full-build; \
	fi

# 快速编译（使用静态库）
fast-build: $(APP_OBJS) $(MAINOBJ)
	@echo "链接主程序（使用静态库）..."
	$(CC) -o $(BIN) $(MAINOBJ) $(APP_OBJS) -Wl,--start-group $(UI_LIB) $(LVGL_LIB) $(DRIVERS_LIB) -Wl,--end-group $(LDFLAGS)
	mv $(BIN) $(LVGL_DIR)/$(BIN_DIR)/
	@echo "✅ 快速编译完成: $(BIN_DIR)/$(BIN)"

# 完整编译（创建静态库）
full-build: $(LVGL_LIB) $(DRIVERS_LIB) $(UI_LIB) $(APP_OBJS) $(MAINOBJ)
	@echo "链接主程序（使用新创建的静态库）..."
	$(CC) -o $(BIN) $(MAINOBJ) $(APP_OBJS) -Wl,--start-group $(UI_LIB) $(LVGL_LIB) $(DRIVERS_LIB) -Wl,--end-group $(LDFLAGS)
	mv $(BIN) $(LVGL_DIR)/$(BIN_DIR)/
	@echo "✅ 完整编译完成: $(BIN_DIR)/$(BIN)"

# 传统编译方式（备用）
traditional: $(ALL_OBJS) $(MAINOBJ) | $(BIN_DIR)
	@echo "使用传统方式编译..."
	$(CC) -o $(BIN) $(MAINOBJ) $(ALL_OBJS) $(LDFLAGS)
	mv $(BIN) $(LVGL_DIR)/$(BIN_DIR)/
	@echo "✅ 传统编译完成: $(BIN_DIR)/$(BIN)"

# 清理应用代码（保留静态库）
clean:
	@echo "清理应用代码和主程序..."
	rm -rf $(BIN) $(APP_OBJS) $(MAINOBJ) ./$(BIN_DIR)/*
	rm -f $(APP_OBJS:.o=.d) $(MAINOBJ:.o=.d)
	@echo "应用代码清理完成（静态库已保留）"

# 完全清理（包括静态库）
clean-all:
	@echo "完全清理所有编译文件..."
	rm -rf $(BIN) ./$(BIN_DIR)/* ./$(OBJ_DIR)/* ./$(LIB_DIR)/*
	@echo "完全清理完成"

# 清理静态库（强制重新编译库）
clean-libs:
	@echo "清理静态库..."
	rm -rf $(LVGL_LIB) $(DRIVERS_LIB) $(UI_LIB)
	rm -rf $(LVGL_OBJS) $(DRIVERS_OBJS) $(UI_OBJS)
	rm -f $(LVGL_OBJS:.o=.d) $(DRIVERS_OBJS:.o=.d) $(UI_OBJS:.o=.d)
	@echo "静态库清理完成"

run: default
	./$(BIN_DIR)/$(BIN)

# 显示编译状态
status:
	@echo "=== 编译状态检查 ==="
	@echo "LVGL库: $(if $(wildcard $(LVGL_LIB)),✅ 存在,❌ 不存在)"
	@echo "Drivers库: $(if $(wildcard $(DRIVERS_LIB)),✅ 存在,❌ 不存在)"
	@echo "UI库: $(if $(wildcard $(UI_LIB)),✅ 存在,❌ 不存在)"
	@echo "主程序: $(if $(wildcard $(BIN_DIR)/$(BIN)),✅ 存在,❌ 不存在)"
	@echo ""
	@echo "=== 编译建议 ==="
	@if [ ! -f "$(LVGL_LIB)" ] || [ ! -f "$(DRIVERS_LIB)" ] || [ ! -f "$(UI_LIB)" ]; then \
		echo "🔄 首次编译或库文件缺失，将自动编译所有库"; \
	else \
		echo "⚡ 库文件已存在，只编译应用代码，编译速度更快"; \
	fi

# SSH部署到目标设备
deploy: default
	@echo "正在部署到目标设备 $(SSH_USER)@$(SSH_HOST)..."
	scp $(SSH_OPTS) ./$(BIN_DIR)/$(BIN) $(SSH_USER)@$(SSH_HOST):$(SSH_TARGET_DIR)
	scp $(SSH_OPTS) -r ./app/lib/*.so $(SSH_USER)@$(SSH_HOST):$(SSH_TARGET_DIR)
	@echo "部署完成"

# 在目标设备上运行
remote-run: deploy
	@echo "在目标设备上运行演示程序..."
	ssh $(SSH_OPTS) $(SSH_USER)@$(SSH_HOST) "cd $(SSH_TARGET_DIR) && export LD_LIBRARY_PATH=$(SSH_TARGET_DIR)lib:\$$LD_LIBRARY_PATH && ./$(BIN)"

# 编译模拟器库
simulator:
	@echo "编译UART模拟器库..."
	@cd app_simulator && make && make install
	@echo "模拟器库编译完成"

# 部署模拟器库
deploy-simulator: simulator
	@echo "部署UART模拟器库到目标设备..."
	@if [ ! -f "libserial_utils_simulator.so" ]; then \
		echo "错误: 模拟器库不存在，请先编译"; \
		echo "运行: cd app_simulator && make && make install"; \
		exit 1; \
	fi
	scp $(SSH_OPTS) ./libserial_utils_simulator.so $(SSH_USER)@$(SSH_HOST):$(SSH_TARGET_DIR)
	@echo "模拟器库部署完成"

# 在目标设备上使用模拟器运行
remote-run-simulator: deploy deploy-simulator
	@echo "在目标设备上运行演示程序（模拟器模式）..."
	@echo "使用UART模拟器替代真实硬件"
	ssh $(SSH_OPTS) $(SSH_USER)@$(SSH_HOST) "cd $(SSH_TARGET_DIR) && export LD_LIBRARY_PATH=$(SSH_TARGET_DIR)lib:\$$LD_LIBRARY_PATH && LD_PRELOAD=./libserial_utils_simulator.so ./$(BIN)"

# 帮助信息
help:
	@echo "=== Makefile 使用说明 ==="
	@echo ""
	@echo "🚀 常用命令:"
	@echo "  make              - 智能编译（只编译变化的部分）"
	@echo "  make -j8          - 并行编译（推荐）"
	@echo "  make status       - 查看编译状态"
	@echo ""
	@echo "🧹 清理命令:"
	@echo "  make clean        - 清理应用代码（保留库文件，快速重编译）"
	@echo "  make clean-libs   - 清理静态库（强制重新编译库）"
	@echo "  make clean-all    - 完全清理所有文件"
	@echo ""
	@echo "🚀 部署命令:"
	@echo "  make deploy                - 部署到目标设备"
	@echo "  make remote-run           - 在目标设备运行"
	@echo "  make remote-run-simulator - 在目标设备运行（模拟器模式）"
	@echo ""
	@echo "💡 编译优化说明:"
	@echo "  - LVGL、UI、Drivers编译为静态库，只在首次或强制清理后重新编译"
	@echo "  - 日常开发只编译应用代码，大幅提升编译速度"
	@echo "  - 使用 'make status' 查看当前编译状态"

.PHONY: all clean clean-all clean-libs run status help deploy remote-run deploy-simulator remote-run-simulator default fast-build full-build traditional
