# UART模拟器库编译Makefile (ARM版本)
# 创建时间: 2025-07-31 17:25 GMT+8
# 更新时间: 2025-07-31 18:00 GMT+8

# ARM交叉编译工具链
CC = arm-linux-gnueabi-gcc
AR = arm-linux-gnueabi-ar
STRIP = arm-linux-gnueabi-strip

# 编译选项
CFLAGS = -Wall -Wextra -fPIC -O2 -g
LDFLAGS = -shared -pthread

# 目录定义
SRC_DIR = .
ADAPTER_DIR = adapter
HAL_DIR = hal
BUILD_DIR = build
LIB_DIR = ../app/lib/include

# 源文件
SIMULATOR_SRCS = $(SRC_DIR)/data_simulator.c \
                 $(HAL_DIR)/uart_hal_simulator.c \
                 $(ADAPTER_DIR)/libserial_utils_simulator.c

# 目标文件
SIMULATOR_OBJS = $(SIMULATOR_SRCS:%.c=$(BUILD_DIR)/%.o)

# 目标库
TARGET_LIB = libserial_utils_simulator.so

# 头文件路径
INCLUDES = -I$(LIB_DIR) \
           -I$(HAL_DIR) \
           -I$(SRC_DIR)

# 默认目标
all: $(TARGET_LIB)

# 创建构建目录
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)
	mkdir -p $(BUILD_DIR)/$(SRC_DIR)
	mkdir -p $(BUILD_DIR)/$(HAL_DIR)
	mkdir -p $(BUILD_DIR)/$(ADAPTER_DIR)

# 编译目标文件
$(BUILD_DIR)/%.o: %.c | $(BUILD_DIR)
	@echo "编译: $<"
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 链接动态库
$(TARGET_LIB): $(SIMULATOR_OBJS)
	@echo "链接模拟器库: $@"
	$(CC) $(LDFLAGS) -o $@ $^
	@echo "模拟器库编译完成: $@"

# 安装到主项目
install: $(TARGET_LIB)
	@echo "安装模拟器库到主项目..."
	cp $(TARGET_LIB) ../
	@echo "安装完成"

# 清理
clean:
	@echo "清理构建文件..."
	rm -rf $(BUILD_DIR)
	rm -f $(TARGET_LIB)
	rm -f ../$(TARGET_LIB)
	@echo "清理完成"

# 测试编译
test: $(TARGET_LIB)
	@echo "测试模拟器库..."
	@echo "库文件信息:"
	file $(TARGET_LIB)
	@echo "导出符号:"
	nm -D $(TARGET_LIB) | grep -E "(serial_|uart_)" | head -10
	@echo "测试完成"

# 帮助信息
help:
	@echo "UART模拟器库编译系统"
	@echo ""
	@echo "可用目标:"
	@echo "  all      - 编译模拟器库 (默认)"
	@echo "  install  - 安装到主项目"
	@echo "  clean    - 清理构建文件"
	@echo "  test     - 测试编译结果"
	@echo "  help     - 显示此帮助信息"
	@echo ""
	@echo "使用方法:"
	@echo "  make              # 编译库"
	@echo "  make install      # 编译并安装"
	@echo "  make clean        # 清理"

.PHONY: all install clean test help
