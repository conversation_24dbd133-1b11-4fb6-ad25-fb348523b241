# Agent
*agent
*Link.bat
app/src

# Node modules
node_modules/

# Logs
logs
*.log
npm-debug.log*
*-log.*

# LVGL
lvgl.layout
/*.depend

# Build output
dist/
build/

# System files
.DS_Store

# Environment variables
.env

# Idea
.vscode
.idea
lvgl
lv_drivers
*-agent

# Prerequisites
*.d

# Object files
*.o
*.ko
*.obj
*.elf

# Linker output
*.ilk
*.map
*.exp
obj
bin

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
#*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
#*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf
