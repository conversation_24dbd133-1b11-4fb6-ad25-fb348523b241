/**
 * @file libserial_utils_simulator.c
 * @brief Serial Utils模拟器库实现 - 通过LD_PRELOAD替换原始库
 * @version 1.0.0
 * @date 2025-07-31 17:20 GMT+8
 */

#include "serial_utils.h"
#include "uart_hal.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h>

// 模拟器状态管理
typedef struct {
    bool initialized;
    pthread_t receive_thread;
    bool receive_thread_running;
    void (*callback)(const char *data, int len);
    pthread_mutex_t mutex;
} simulator_state_t;

static simulator_state_t g_sim_state = {0};

// 内部函数声明
static void* simulator_receive_thread(void* arg);
static void simulator_cleanup(void);

/**
 * @brief 模拟器接收线程
 */
static void* simulator_receive_thread(void* arg) {
    (void)arg; // 避免未使用参数警告
    
    printf("[SIM_LIB] 模拟器接收线程启动\n");
    
    uint8_t buffer[1024];
    uint16_t received_length;
    
    while (g_sim_state.receive_thread_running) {
        // 从HAL模拟器接收数据
        uart_hal_error_t ret = uart_hal_receive(buffer, sizeof(buffer), &received_length);
        
        if (ret == UART_HAL_SUCCESS && received_length > 0) {
            // 调用回调函数
            pthread_mutex_lock(&g_sim_state.mutex);
            if (g_sim_state.callback) {
                g_sim_state.callback((const char*)buffer, received_length);
                printf("[SIM_LIB] 模拟器接收数据: %d字节\n", received_length);
            }
            pthread_mutex_unlock(&g_sim_state.mutex);
        }
        
        // 短暂休眠
        usleep(10000); // 10ms
    }
    
    printf("[SIM_LIB] 模拟器接收线程退出\n");
    return NULL;
}

/**
 * @brief 清理模拟器资源
 */
static void simulator_cleanup(void) {
    if (g_sim_state.receive_thread_running) {
        g_sim_state.receive_thread_running = false;
        pthread_join(g_sim_state.receive_thread, NULL);
    }
    
    if (g_sim_state.initialized) {
        uart_hal_deinit();
        pthread_mutex_destroy(&g_sim_state.mutex);
        g_sim_state.initialized = false;
    }
}

/**
 * @brief 初始化串口设备 (模拟器版本)
 */
serial_error_t serial_init(const char *device, int baud_rate, int data_bits, 
                          char parity, int stop_bits, int flow_control, 
                          serial_handle_t **handle) {
    printf("[SIM_LIB] 模拟器库初始化串口: %s, %d bps\n", device, baud_rate);
    
    if (!device || !handle) {
        return SERIAL_ERROR_INVALID_PARAM;
    }
    
    // 初始化模拟器状态
    if (!g_sim_state.initialized) {
        if (pthread_mutex_init(&g_sim_state.mutex, NULL) != 0) {
            return SERIAL_ERROR_SYSTEM;
        }
        
        // 初始化HAL模拟器
        uart_hal_error_t hal_ret = uart_hal_init();
        if (hal_ret != UART_HAL_SUCCESS) {
            pthread_mutex_destroy(&g_sim_state.mutex);
            printf("[SIM_LIB] HAL模拟器初始化失败: %d\n", hal_ret);
            return SERIAL_ERROR_SYSTEM;
        }
        
        // 设置HAL配置
        uart_hal_config_t config = {
            .baudrate = baud_rate,
            .data_bits = data_bits,
            .stop_bits = stop_bits,
            .parity = (parity == 'E') ? 1 : 0,
            .flow_control = (flow_control != 0)
        };
        uart_hal_open(device, &config);
        
        g_sim_state.initialized = true;
        
        // 注册清理函数
        atexit(simulator_cleanup);
    }
    
    // 创建模拟的serial_handle_t
    serial_handle_t *sim_handle = malloc(sizeof(serial_handle_t));
    if (!sim_handle) {
        return SERIAL_ERROR_SYSTEM;
    }
    
    // 初始化句柄
    memset(sim_handle, 0, sizeof(serial_handle_t));
    sim_handle->fd = -1; // 模拟器模式下不使用真实fd
    strncpy(sim_handle->device, device, sizeof(sim_handle->device) - 1);
    sim_handle->baud_rate = baud_rate;
    sim_handle->data_bits = data_bits;
    sim_handle->parity = parity;
    sim_handle->stop_bits = stop_bits;
    sim_handle->flow_control = flow_control;
    sim_handle->thread_running = 0;
    sim_handle->callback = NULL;
    
    *handle = sim_handle;
    
    printf("[SIM_LIB] 模拟器串口句柄创建成功\n");
    return SERIAL_ERROR_NONE;
}

/**
 * @brief 设置数据接收回调函数 (模拟器版本)
 */
serial_error_t serial_set_callback(serial_handle_t *handle, 
                                  void (*callback)(const char *data, int len)) {
    if (!handle) {
        return SERIAL_ERROR_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_sim_state.mutex);
    g_sim_state.callback = callback;
    handle->callback = callback;
    pthread_mutex_unlock(&g_sim_state.mutex);
    
    printf("[SIM_LIB] 设置数据接收回调\n");
    return SERIAL_ERROR_NONE;
}

/**
 * @brief 启动数据接收线程 (模拟器版本)
 */
serial_error_t serial_start_receive(serial_handle_t *handle) {
    if (!handle) {
        return SERIAL_ERROR_INVALID_PARAM;
    }
    
    if (g_sim_state.receive_thread_running) {
        return SERIAL_ERROR_NONE; // 已经在运行
    }
    
    printf("[SIM_LIB] 启动模拟器接收线程\n");
    
    g_sim_state.receive_thread_running = true;
    if (pthread_create(&g_sim_state.receive_thread, NULL, simulator_receive_thread, handle) != 0) {
        g_sim_state.receive_thread_running = false;
        printf("[SIM_LIB] 接收线程创建失败\n");
        return SERIAL_ERROR_THREAD_FAILED;
    }
    
    handle->thread_running = 1;
    printf("[SIM_LIB] 模拟器接收线程启动成功\n");
    return SERIAL_ERROR_NONE;
}

/**
 * @brief 停止数据接收线程 (模拟器版本)
 */
serial_error_t serial_stop_receive(serial_handle_t *handle) {
    if (!handle) {
        return SERIAL_ERROR_INVALID_PARAM;
    }
    
    if (!g_sim_state.receive_thread_running) {
        return SERIAL_ERROR_NONE; // 已经停止
    }
    
    printf("[SIM_LIB] 停止模拟器接收线程\n");
    
    g_sim_state.receive_thread_running = false;
    pthread_join(g_sim_state.receive_thread, NULL);
    
    handle->thread_running = 0;
    printf("[SIM_LIB] 模拟器接收线程已停止\n");
    return SERIAL_ERROR_NONE;
}

/**
 * @brief 发送字符串数据 (模拟器版本)
 */
serial_error_t serial_send_string(serial_handle_t *handle, const char *data) {
    if (!handle || !data) {
        return SERIAL_ERROR_INVALID_PARAM;
    }
    
    return serial_send_binary(handle, data, strlen(data));
}

/**
 * @brief 发送十六进制数据 (模拟器版本)
 */
serial_error_t serial_send_hex(serial_handle_t *handle, const char *hex_string) {
    if (!handle || !hex_string) {
        return SERIAL_ERROR_INVALID_PARAM;
    }
    
    // 简化实现：直接发送字符串
    printf("[SIM_LIB] 发送十六进制数据: %s\n", hex_string);
    uint16_t sent;
    uart_hal_error_t ret = uart_hal_send((const uint8_t*)hex_string, strlen(hex_string), &sent);
    return (ret == UART_HAL_SUCCESS) ? SERIAL_ERROR_NONE : SERIAL_ERROR_WRITE_FAILED;
}

/**
 * @brief 发送二进制数据 (模拟器版本)
 */
serial_error_t serial_send_binary(serial_handle_t *handle, const char *data, int len) {
    if (!handle || !data || len <= 0) {
        return SERIAL_ERROR_INVALID_PARAM;
    }
    
    printf("[SIM_LIB] 模拟器发送数据: %d字节\n", len);
    
    // 打印发送的数据（调试用）
    printf("[SIM_LIB] 发送数据: ");
    for (int i = 0; i < len && i < 32; i++) {
        printf("%02X ", (unsigned char)data[i]);
    }
    if (len > 32) printf("...");
    printf("\n");
    
    // 通过HAL模拟器发送数据
    uint16_t sent;
    uart_hal_error_t ret = uart_hal_send((const uint8_t*)data, len, &sent);
    if (ret != UART_HAL_SUCCESS) {
        printf("[SIM_LIB] HAL发送失败: %d\n", ret);
        return SERIAL_ERROR_WRITE_FAILED;
    }
    
    return SERIAL_ERROR_NONE;
}

/**
 * @brief 读取串口数据 (模拟器版本)
 */
serial_error_t serial_read(serial_handle_t *handle, char *buffer, int buffer_size,
                          int *bytes_read, int timeout_ms) {
    (void)timeout_ms; // 避免未使用参数警告

    if (!handle || !buffer || !bytes_read) {
        return SERIAL_ERROR_INVALID_PARAM;
    }
    
    *bytes_read = 0;
    
    // 从HAL模拟器读取数据
    uint16_t received_length;
    uart_hal_error_t ret = uart_hal_receive((uint8_t*)buffer, buffer_size, &received_length);
    
    if (ret == UART_HAL_SUCCESS) {
        *bytes_read = received_length;
        if (received_length > 0) {
            printf("[SIM_LIB] 读取数据: %d字节\n", received_length);
        }
        return SERIAL_ERROR_NONE;
    }
    
    return SERIAL_ERROR_READ_FAILED;
}

/**
 * @brief 关闭串口设备并释放资源 (模拟器版本)
 */
serial_error_t serial_close(serial_handle_t *handle) {
    if (!handle) {
        return SERIAL_ERROR_INVALID_PARAM;
    }
    
    printf("[SIM_LIB] 关闭模拟器串口句柄\n");
    
    // 停止接收线程
    serial_stop_receive(handle);
    
    // 释放句柄内存
    free(handle);
    
    printf("[SIM_LIB] 模拟器串口句柄已关闭\n");
    return SERIAL_ERROR_NONE;
}

/**
 * @brief 获取错误码对应的错误描述字符串 (模拟器版本)
 */
const char *serial_error_to_string(serial_error_t error) {
    switch (error) {
        case SERIAL_ERROR_NONE: return "成功";
        case SERIAL_ERROR_OPEN_FAILED: return "打开串口失败";
        case SERIAL_ERROR_INVALID_PARAM: return "无效参数";
        case SERIAL_ERROR_CONFIG_FAILED: return "配置串口失败";
        case SERIAL_ERROR_WRITE_FAILED: return "写入失败";
        case SERIAL_ERROR_READ_FAILED: return "读取失败";
        case SERIAL_ERROR_THREAD_FAILED: return "线程创建失败";
        case SERIAL_ERROR_TIMEOUT: return "超时";
        case SERIAL_ERROR_SYSTEM: return "系统错误";
        case SERIAL_ERROR_NOT_OPENED: return "设备未打开";
        default: return "未知错误";
    }
}
