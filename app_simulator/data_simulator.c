/**
 * @file data_simulator.c
 * @brief 数据模拟器实现 - 生成各种协议数据用于测试
 * @version 1.0.0
 * @date 2025-07-26
 */

#define _GNU_SOURCE
#include "data_simulator.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include <sys/time.h>
#include <math.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 内部函数声明
static void update_speed_generator(speed_generator_t* gen, uint32_t current_time);
static void update_rpm_generator(rpm_generator_t* gen, uint32_t current_time);
static void update_controller_generator(controller_generator_t* gen, uint32_t current_time);
static void update_time_generator(time_generator_t* gen, uint32_t current_time);
static float generate_sine_wave(uint32_t time, float frequency, float amplitude, float offset);
static float generate_random_value(float min, float max);
static uint16_t clamp_uint16(int32_t value, uint16_t min, uint16_t max);
static int16_t clamp_int16(int32_t value, int16_t min, int16_t max);

// =============================================================================
// 全局变量
// =============================================================================

static data_simulator_t g_simulator = {0};

// =============================================================================
// 内部函数声明
// =============================================================================

static void init_speed_generator(speed_generator_t* gen, simulator_scenario_t scenario);
static void init_rpm_generator(rpm_generator_t* gen, simulator_scenario_t scenario);
static void init_controller_generator(controller_generator_t* gen, simulator_scenario_t scenario);
static void init_time_generator(time_generator_t* gen);

static void update_speed_generator(speed_generator_t* gen, uint32_t current_time);
static void update_rpm_generator(rpm_generator_t* gen, uint32_t current_time);
static void update_controller_generator(controller_generator_t* gen, uint32_t current_time);
static void update_time_generator(time_generator_t* gen, uint32_t current_time);

static float generate_sine_wave(uint32_t time, float frequency, float amplitude, float offset);
static float generate_random_value(float min, float max);
static uint16_t clamp_uint16(int32_t value, uint16_t min, uint16_t max);
static int16_t clamp_int16(int32_t value, int16_t min, int16_t max);

// =============================================================================
// 公共接口实现
// =============================================================================

int data_simulator_init(data_simulator_t* simulator) {
    if (!simulator) {
        return -1;
    }
    
    if (simulator->initialized) {
        return 0;
    }
    
    memset(simulator, 0, sizeof(data_simulator_t));
    
    simulator->scenario = SCENARIO_NORMAL_DRIVING;
    simulator->auto_mode = true;
    simulator->start_time = data_simulator_get_timestamp();
    simulator->current_time = simulator->start_time;
    
    // 初始化各个数据生成器
    init_speed_generator(&simulator->speed_gen, simulator->scenario);
    init_rpm_generator(&simulator->rpm_gen, simulator->scenario);
    init_controller_generator(&simulator->controller_gen, simulator->scenario);
    init_time_generator(&simulator->time_gen);
    
    simulator->initialized = true;
    return 0;
}

void data_simulator_deinit(data_simulator_t* simulator) {
    if (!simulator || !simulator->initialized) {
        return;
    }
    
    simulator->running = false;
    memset(simulator, 0, sizeof(data_simulator_t));
}

int data_simulator_start(data_simulator_t* simulator, simulator_scenario_t scenario, uint32_t duration) {
    if (!simulator || !simulator->initialized) {
        return -1;
    }
    
    if (scenario >= SCENARIO_COUNT) {
        return -1;
    }
    
    simulator->scenario = scenario;
    simulator->duration = duration;
    simulator->start_time = data_simulator_get_timestamp();
    simulator->current_time = simulator->start_time;
    simulator->running = true;
    
    // 重新初始化生成器
    init_speed_generator(&simulator->speed_gen, scenario);
    init_rpm_generator(&simulator->rpm_gen, scenario);
    init_controller_generator(&simulator->controller_gen, scenario);
    
    // 重置统计信息
    data_simulator_reset_statistics(simulator);
    
    return 0;
}

void data_simulator_stop(data_simulator_t* simulator) {
    if (!simulator) {
        return;
    }
    
    simulator->running = false;
}

int data_simulator_update(data_simulator_t* simulator) {
    if (!simulator || !simulator->initialized || !simulator->running) {
        return -1;
    }
    
    uint32_t current_time = data_simulator_get_timestamp();
    simulator->current_time = current_time;
    
    // 检查运行时长
    if (simulator->duration > 0) {
        uint32_t elapsed = current_time - simulator->start_time;
        if (elapsed >= simulator->duration) {
            simulator->running = false;
            return 0;
        }
    }
    
    // 更新各个数据生成器
    update_speed_generator(&simulator->speed_gen, current_time);
    update_rpm_generator(&simulator->rpm_gen, current_time);
    update_controller_generator(&simulator->controller_gen, current_time);
    update_time_generator(&simulator->time_gen, current_time);
    
    simulator->update_count++;
    return 0;
}

int data_simulator_set_scenario(data_simulator_t* simulator, simulator_scenario_t scenario) {
    if (!simulator || !simulator->initialized) {
        return -1;
    }
    
    if (scenario >= SCENARIO_COUNT) {
        return -1;
    }
    
    simulator->scenario = scenario;
    
    // 重新初始化生成器
    init_speed_generator(&simulator->speed_gen, scenario);
    init_rpm_generator(&simulator->rpm_gen, scenario);
    init_controller_generator(&simulator->controller_gen, scenario);
    
    return 0;
}

simulator_scenario_t data_simulator_get_scenario(data_simulator_t* simulator) {
    if (!simulator || !simulator->initialized) {
        return SCENARIO_NORMAL_DRIVING;
    }
    
    return simulator->scenario;
}

// =============================================================================
// 数据生成接口实现
// =============================================================================

int data_simulator_generate_controller_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !simulator->initialized || !data) {
        return -1;
    }
    
    controller_generator_t* gen = &simulator->controller_gen;
    
    // 构建18字节控制器状态数据
    data[0] = gen->tcs_enabled;         // TCS使能
    data[1] = gen->tcs_active;          // TCS激活
    data[2] = gen->cruise_enabled;      // 巡航使能
    data[3] = gen->cruise_active;       // 巡航激活
    data[4] = gen->work_mode;           // 工作模式
    data[5] = gen->brake_status;        // 制动状态
    data[6] = gen->gear_status;         // 挡位状态
    data[7] = (uint8_t)(gen->temperature & 0xFF);       // 温度低字节
    data[8] = (uint8_t)((gen->temperature >> 8) & 0xFF); // 温度高字节
    
    // 电压 (V * 10)
    uint16_t voltage_raw = (uint16_t)(gen->voltage * 10);
    data[9] = (uint8_t)(voltage_raw & 0xFF);
    data[10] = (uint8_t)((voltage_raw >> 8) & 0xFF);
    
    // 电流 (A * 10)
    int16_t current_raw = (int16_t)(gen->current * 10);
    data[11] = (uint8_t)(current_raw & 0xFF);
    data[12] = (uint8_t)((current_raw >> 8) & 0xFF);
    
    data[13] = gen->soc;                // SOC
    
    // 续航里程
    data[14] = (uint8_t)(gen->range & 0xFF);
    data[15] = (uint8_t)((gen->range >> 8) & 0xFF);
    
    data[16] = gen->fault_code;         // 故障代码
    data[17] = 0;                       // 保留字节
    
    simulator->data_sent_count++;
    return 0;
}

int data_simulator_generate_light_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !simulator->initialized || !data) {
        return -1;
    }

    // 生成灯光屏幕控制数据 (0x56)
    data[0] = 0x01;  // 灯光控制
    data[1] = 0x80;  // 屏幕控制

    simulator->data_sent_count++;
    return 0;
}

int data_simulator_generate_light_warning_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !simulator->initialized || !data) {
        return -1;
    }

    controller_generator_t* gen = &simulator->controller_gen;
    uint32_t current_time = data_simulator_get_timestamp();

    // 生成灯光警示灯数据 (0x63) - 可变长度
    uint8_t pos = 0;

    // 左转向灯状态
    data[pos++] = (current_time / 500) % 2; // 500ms闪烁

    // 右转向灯状态
    data[pos++] = (current_time / 500) % 2; // 500ms闪烁

    // 大灯状态
    data[pos++] = 1; // 常亮

    // 远光灯状态
    data[pos++] = 0; // 关闭

    // 雾灯状态
    data[pos++] = (simulator->scenario == SCENARIO_FAULT_TEST) ? 1 : 0;

    // 双闪状态
    data[pos++] = (gen->fault_code != 0) ? ((current_time / 300) % 2) : 0;

    // 制动灯状态
    data[pos++] = gen->brake_status;

    // 倒车灯状态
    data[pos++] = (gen->gear_status == 0) ? 1 : 0; // R挡时点亮

    simulator->data_sent_count++;
    return pos; // 返回实际数据长度
}

int data_simulator_generate_speed_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !simulator->initialized || !data) {
        return -1;
    }
    
    speed_generator_t* gen = &simulator->speed_gen;
    
    // 生成速度数据 (0x60) - 2字节，小端序
    data[0] = (uint8_t)(gen->current_speed & 0xFF);
    data[1] = (uint8_t)((gen->current_speed >> 8) & 0xFF);
    
    simulator->data_sent_count++;
    return 0;
}

int data_simulator_generate_rpm_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !simulator->initialized || !data) {
        return -1;
    }
    
    rpm_generator_t* gen = &simulator->rpm_gen;
    
    // 生成转速数据 (0x61) - 2字节，小端序
    data[0] = (uint8_t)(gen->current_rpm & 0xFF);
    data[1] = (uint8_t)((gen->current_rpm >> 8) & 0xFF);
    
    simulator->data_sent_count++;
    return 0;
}

int data_simulator_generate_gear_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !simulator->initialized || !data) {
        return -1;
    }
    
    controller_generator_t* gen = &simulator->controller_gen;
    
    // 生成挡位数据 (0x62) - 1字节
    data[0] = gen->gear_status;
    
    simulator->data_sent_count++;
    return 0;
}

int data_simulator_generate_range_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !simulator->initialized || !data) {
        return -1;
    }
    
    controller_generator_t* gen = &simulator->controller_gen;
    
    // 生成续航数据 (0x64) - 2字节，小端序
    data[0] = (uint8_t)(gen->range & 0xFF);
    data[1] = (uint8_t)((gen->range >> 8) & 0xFF);
    
    simulator->data_sent_count++;
    return 0;
}

int data_simulator_generate_mileage_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !simulator->initialized || !data) {
        return -1;
    }
    
    controller_generator_t* gen = &simulator->controller_gen;
    
    // 生成里程数据 (0x65) - 8字节
    // 总里程 (4字节)
    data[0] = (uint8_t)(gen->mileage & 0xFF);
    data[1] = (uint8_t)((gen->mileage >> 8) & 0xFF);
    data[2] = (uint8_t)((gen->mileage >> 16) & 0xFF);
    data[3] = (uint8_t)((gen->mileage >> 24) & 0xFF);
    
    // 小计里程 (4字节)
    data[4] = (uint8_t)(gen->trip_mileage & 0xFF);
    data[5] = (uint8_t)((gen->trip_mileage >> 8) & 0xFF);
    data[6] = (uint8_t)((gen->trip_mileage >> 16) & 0xFF);
    data[7] = (uint8_t)((gen->trip_mileage >> 24) & 0xFF);
    
    simulator->data_sent_count++;
    return 0;
}

int data_simulator_generate_time_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !simulator->initialized || !data) {
        return -1;
    }
    
    time_generator_t* gen = &simulator->time_gen;
    
    // 生成时间数据 (0x66) - 7字节
    data[0] = gen->year;    // 年
    data[1] = gen->month;   // 月
    data[2] = gen->day;     // 日
    data[3] = gen->hour;    // 时
    data[4] = gen->minute;  // 分
    data[5] = gen->second;  // 秒
    data[6] = gen->week;    // 星期
    
    simulator->data_sent_count++;
    return 0;
}

// =============================================================================
// 工具函数实现
// =============================================================================

data_simulator_t* data_simulator_get_instance(void) {
    return &g_simulator;
}

const char* data_simulator_get_scenario_name(simulator_scenario_t scenario) {
    static const char* scenario_names[] = {
        "Normal Driving",
        "High Speed",
        "TCS Active",
        "Cruise Control",
        "Low Battery",
        "Fault Test",
        "Boundary Test",
        "Idle Test",
        "Charging Test",
        "Custom Test"
    };

    if (scenario >= SCENARIO_COUNT) {
        return "Unknown";
    }

    return scenario_names[scenario];
}

int data_simulator_get_statistics(data_simulator_t* simulator,
                                 uint32_t* update_count,
                                 uint32_t* data_sent_count,
                                 uint32_t* error_count) {
    if (!simulator || !simulator->initialized) {
        return -1;
    }

    if (update_count) *update_count = simulator->update_count;
    if (data_sent_count) *data_sent_count = simulator->data_sent_count;
    if (error_count) *error_count = simulator->error_count;

    return 0;
}

void data_simulator_reset_statistics(data_simulator_t* simulator) {
    if (!simulator) {
        return;
    }

    simulator->update_count = 0;
    simulator->data_sent_count = 0;
    simulator->error_count = 0;
}

uint32_t data_simulator_get_timestamp(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint32_t)(tv.tv_sec * 1000 + tv.tv_usec / 1000);
}

// =============================================================================
// 内部函数实现
// =============================================================================

static void init_speed_generator(speed_generator_t* gen, simulator_scenario_t scenario) {
    if (!gen) return;

    memset(gen, 0, sizeof(speed_generator_t));

    switch (scenario) {
        case SCENARIO_NORMAL_DRIVING:
            gen->max_speed = 80;
            gen->target_speed = 40;
            gen->mode = DATA_MODE_SINE_WAVE;
            gen->amplitude = 20.0f;
            gen->frequency = 0.1f;
            break;

        case SCENARIO_HIGH_SPEED:
            gen->max_speed = 120;
            gen->target_speed = 100;
            gen->mode = DATA_MODE_SINE_WAVE;
            gen->amplitude = 15.0f;
            gen->frequency = 0.05f;
            break;

        case SCENARIO_IDLE_TEST:
            gen->max_speed = 5;
            gen->target_speed = 0;
            gen->mode = DATA_MODE_RANDOM;
            gen->amplitude = 2.0f;
            gen->frequency = 0.2f;
            break;

        case SCENARIO_BOUNDARY_TEST:
            gen->max_speed = 120;
            gen->target_speed = 120;
            gen->mode = DATA_MODE_STEP;
            gen->amplitude = 120.0f;
            gen->frequency = 0.01f;
            break;

        default:
            gen->max_speed = 60;
            gen->target_speed = 30;
            gen->mode = DATA_MODE_LINEAR;
            gen->amplitude = 10.0f;
            gen->frequency = 0.1f;
            break;
    }

    gen->current_speed = 0;
    gen->last_update = data_simulator_get_timestamp();
}

static void init_rpm_generator(rpm_generator_t* gen, simulator_scenario_t scenario) {
    if (!gen) return;

    memset(gen, 0, sizeof(rpm_generator_t));

    switch (scenario) {
        case SCENARIO_NORMAL_DRIVING:
            gen->max_rpm = 4000;
            gen->target_rpm = 2000;
            gen->mode = DATA_MODE_SINE_WAVE;
            gen->amplitude = 800.0f;
            gen->frequency = 0.15f;
            break;

        case SCENARIO_HIGH_SPEED:
            gen->max_rpm = 6000;
            gen->target_rpm = 5000;
            gen->mode = DATA_MODE_SINE_WAVE;
            gen->amplitude = 500.0f;
            gen->frequency = 0.08f;
            break;

        case SCENARIO_IDLE_TEST:
            gen->max_rpm = 800;
            gen->target_rpm = 600;
            gen->mode = DATA_MODE_RANDOM;
            gen->amplitude = 100.0f;
            gen->frequency = 0.3f;
            break;

        default:
            gen->max_rpm = 3000;
            gen->target_rpm = 1500;
            gen->mode = DATA_MODE_LINEAR;
            gen->amplitude = 500.0f;
            gen->frequency = 0.1f;
            break;
    }

    gen->current_rpm = 0;
    gen->last_update = data_simulator_get_timestamp();
}

static void init_controller_generator(controller_generator_t* gen, simulator_scenario_t scenario) {
    if (!gen) return;

    memset(gen, 0, sizeof(controller_generator_t));

    // 基础设置
    gen->temperature = 45;
    gen->voltage = 48.0f;
    gen->current = 0.0f;
    gen->soc = 80;
    gen->range = 150;
    gen->mileage = 12345;
    gen->trip_mileage = 123;
    gen->gear_status = 3; // D挡

    switch (scenario) {
        case SCENARIO_TCS_ACTIVE:
            gen->tcs_enabled = 1;
            gen->tcs_active = 1;
            gen->work_mode = 1; // POWER模式
            gen->brake_status = 1;
            break;

        case SCENARIO_CRUISE_CONTROL:
            gen->cruise_enabled = 1;
            gen->cruise_active = 1;
            gen->work_mode = 2; // CRUISE模式
            break;

        case SCENARIO_LOW_BATTERY:
            gen->soc = 15;
            gen->range = 25;
            gen->voltage = 44.5f;
            gen->work_mode = 0; // ECO模式
            break;

        case SCENARIO_FAULT_TEST:
            gen->fault_code = 0x01;
            gen->temperature = 85;
            gen->voltage = 42.0f;
            break;

        case SCENARIO_CHARGING_TEST:
            gen->current = -5.0f; // 负电流表示充电
            gen->soc = 95;
            gen->gear_status = 0; // P挡
            break;

        default:
            gen->work_mode = 0; // ECO模式
            break;
    }

    gen->last_update = data_simulator_get_timestamp();
}

static void init_time_generator(time_generator_t* gen) {
    if (!gen) return;

    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);

    gen->year = tm_info->tm_year - 100;  // 年份从1900开始，转换为从2000开始
    gen->month = tm_info->tm_mon + 1;
    gen->day = tm_info->tm_mday;
    gen->hour = tm_info->tm_hour;
    gen->minute = tm_info->tm_min;
    gen->second = tm_info->tm_sec;
    gen->week = tm_info->tm_wday;

    gen->last_update = data_simulator_get_timestamp();
}
/**
 * @file data_simulator_utils.c
 * @brief 数据模拟器工具函数实现
 * @version 1.0.0
 * @date 2025-07-26
 */

#include "data_simulator.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include <sys/time.h>

// =============================================================================
// 更新函数实现
// =============================================================================

static void update_speed_generator(speed_generator_t* gen, uint32_t current_time) {
    if (!gen) return;
    
    uint32_t elapsed = current_time - gen->last_update;
    if (elapsed < 50) return; // 限制更新频率
    
    float time_sec = current_time / 1000.0f;
    
    switch (gen->mode) {
        case DATA_MODE_SINE_WAVE:
            gen->current_speed = clamp_uint16(
                gen->target_speed + generate_sine_wave(current_time, gen->frequency, gen->amplitude, 0),
                0, gen->max_speed);
            break;
            
        case DATA_MODE_RANDOM:
            gen->current_speed = clamp_uint16(
                gen->target_speed + generate_random_value(-gen->amplitude, gen->amplitude),
                0, gen->max_speed);
            break;
            
        case DATA_MODE_LINEAR:
            {
                float progress = fmod(time_sec * gen->frequency, 2.0f);
                if (progress > 1.0f) progress = 2.0f - progress;
                gen->current_speed = clamp_uint16(progress * gen->max_speed, 0, gen->max_speed);
            }
            break;
            
        case DATA_MODE_STEP:
            {
                int step = (int)(time_sec * gen->frequency) % 4;
                uint16_t steps[] = {0, gen->max_speed/3, gen->max_speed*2/3, gen->max_speed};
                gen->current_speed = steps[step];
            }
            break;
            
        default:
            gen->current_speed = gen->target_speed;
            break;
    }
    
    gen->last_update = current_time;
}

static void update_rpm_generator(rpm_generator_t* gen, uint32_t current_time) {
    if (!gen) return;
    
    uint32_t elapsed = current_time - gen->last_update;
    if (elapsed < 50) return; // 限制更新频率
    
    float time_sec = current_time / 1000.0f;
    
    switch (gen->mode) {
        case DATA_MODE_SINE_WAVE:
            gen->current_rpm = clamp_uint16(
                gen->target_rpm + generate_sine_wave(current_time, gen->frequency, gen->amplitude, 0),
                0, gen->max_rpm);
            break;
            
        case DATA_MODE_RANDOM:
            gen->current_rpm = clamp_uint16(
                gen->target_rpm + generate_random_value(-gen->amplitude, gen->amplitude),
                0, gen->max_rpm);
            break;
            
        case DATA_MODE_LINEAR:
            {
                float progress = fmod(time_sec * gen->frequency, 2.0f);
                if (progress > 1.0f) progress = 2.0f - progress;
                gen->current_rpm = clamp_uint16(progress * gen->max_rpm, 0, gen->max_rpm);
            }
            break;
            
        case DATA_MODE_STEP:
            {
                int step = (int)(time_sec * gen->frequency) % 4;
                uint16_t steps[] = {600, gen->max_rpm/3, gen->max_rpm*2/3, gen->max_rpm};
                gen->current_rpm = steps[step];
            }
            break;
            
        default:
            gen->current_rpm = gen->target_rpm;
            break;
    }
    
    gen->last_update = current_time;
}

static void update_controller_generator(controller_generator_t* gen, uint32_t current_time) {
    if (!gen) return;
    
    uint32_t elapsed = current_time - gen->last_update;
    if (elapsed < 100) return; // 限制更新频率
    
    float time_sec = current_time / 1000.0f;
    
    // 更新温度 (缓慢变化)
    gen->temperature = clamp_int16(
        45 + generate_sine_wave(current_time, 0.01f, 15.0f, 0),
        25, 85);
    
    // 更新电压 (小幅波动)
    gen->voltage = 48.0f + generate_sine_wave(current_time, 0.05f, 2.0f, 0);
    if (gen->voltage < 44.0f) gen->voltage = 44.0f;
    if (gen->voltage > 52.0f) gen->voltage = 52.0f;
    
    // 更新电流 (根据场景变化)
    if (gen->gear_status == 0) { // P挡
        gen->current = generate_random_value(-0.5f, 0.5f);
    } else {
        gen->current = generate_sine_wave(current_time, 0.1f, 10.0f, 5.0f);
    }
    
    // 更新SOC (缓慢下降)
    static uint32_t last_soc_update = 0;
    if (current_time - last_soc_update > 10000) { // 每10秒更新一次
        if (gen->current > 0 && gen->soc > 0) {
            gen->soc--;
        }
        last_soc_update = current_time;
    }
    
    // 更新续航 (与SOC关联)
    gen->range = (uint16_t)(gen->soc * 2.5f); // 假设满电250km
    
    // 更新里程 (缓慢增加)
    static uint32_t last_mileage_update = 0;
    if (current_time - last_mileage_update > 5000) { // 每5秒更新一次
        if (gen->current > 0) { // 行驶中
            gen->mileage++;
            gen->trip_mileage++;
        }
        last_mileage_update = current_time;
    }
    
    // 更新状态标志 (根据场景)
    static uint32_t last_status_update = 0;
    if (current_time - last_status_update > 2000) { // 每2秒更新一次
        // TCS激活状态切换
        if (gen->tcs_enabled) {
            gen->tcs_active = (int)(time_sec) % 4 < 2; // 2秒开，2秒关
        }
        
        // 巡航激活状态切换
        if (gen->cruise_enabled) {
            gen->cruise_active = (int)(time_sec) % 6 < 4; // 4秒开，2秒关
        }
        
        last_status_update = current_time;
    }
    
    gen->last_update = current_time;
}

static void update_time_generator(time_generator_t* gen, uint32_t current_time) {
    if (!gen) return;
    
    uint32_t elapsed = current_time - gen->last_update;
    if (elapsed < 1000) return; // 每秒更新一次
    
    gen->second++;
    if (gen->second >= 60) {
        gen->second = 0;
        gen->minute++;
        if (gen->minute >= 60) {
            gen->minute = 0;
            gen->hour++;
            if (gen->hour >= 24) {
                gen->hour = 0;
                gen->day++;
                gen->week = (gen->week + 1) % 7;
                
                // 简化的月份处理
                uint8_t days_in_month = 30; // 简化为30天
                if (gen->day > days_in_month) {
                    gen->day = 1;
                    gen->month++;
                    if (gen->month > 12) {
                        gen->month = 1;
                        gen->year++;
                    }
                }
            }
        }
    }
    
    gen->last_update = current_time;
}

// =============================================================================
// 工具函数实现
// =============================================================================

static float generate_sine_wave(uint32_t time, float frequency, float amplitude, float offset) {
    float time_sec = time / 1000.0f;
    return amplitude * sinf(2.0f * M_PI * frequency * time_sec) + offset;
}

static uint32_t get_current_time_ms(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint32_t)(tv.tv_sec * 1000 + tv.tv_usec / 1000);
}

static float generate_random_value(float min, float max) {
    float range = max - min;
    return min + (float)rand() / RAND_MAX * range;
}

static uint16_t clamp_uint16(int32_t value, uint16_t min, uint16_t max) {
    if (value < min) return min;
    if (value > max) return max;
    return (uint16_t)value;
}

static int16_t clamp_int16(int32_t value, int16_t min, int16_t max) {
    if (value < min) return min;
    if (value > max) return max;
    return (int16_t)value;
}

/**
 * @brief 生成电池数据 (模拟)
 */
int data_simulator_generate_battery_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !data) {
        return -1;
    }

    controller_generator_t* gen = &simulator->controller_gen;
    uint32_t current_time = get_current_time_ms();

    // 模拟电池电量变化 (在80%-100%之间变化)
    static uint8_t battery_level = 85;
    static bool charging = false;
    static uint32_t last_battery_change = 0;

    // 每5秒改变一次电池状态
    if (current_time - last_battery_change >= 5000) {
        if (charging) {
            battery_level += 2;
            if (battery_level >= 100) {
                battery_level = 100;
                charging = false;
            }
        } else {
            battery_level -= 1;
            if (battery_level <= 80) {
                battery_level = 80;
                charging = true;
            }
        }
        last_battery_change = current_time;
    }

    // 构建电池数据包
    data[0] = battery_level;                    // 电池电量 (%)
    data[1] = charging ? 1 : 0;                 // 充电状态
    uint16_t voltage = (uint16_t)(gen->voltage * 10); // 电压 * 10
    data[2] = voltage & 0xFF;                   // 电压低字节
    data[3] = (voltage >> 8) & 0xFF;            // 电压高字节

    return 0;
}

/**
 * @brief 生成姿态数据 (模拟)
 */
int data_simulator_generate_attitude_data(data_simulator_t* simulator, uint8_t* data) {
    if (!simulator || !data) {
        return -1;
    }

    uint32_t current_time = get_current_time_ms();

    // 模拟姿态角度变化 (正弦波变化)
    static float phase = 0.0f;
    phase += 0.1f;
    if (phase > 6.28f) phase = 0.0f; // 2π

    // 俯仰角 (-5° 到 +5°)
    float pitch = 5.0f * sin(phase);
    int16_t pitch_int = (int16_t)(pitch * 10); // 精度0.1度

    // 横滚角 (-3° 到 +3°)
    float roll = 3.0f * sin(phase * 1.5f);
    int16_t roll_int = (int16_t)(roll * 10); // 精度0.1度

    // 加速度 (0.8g 到 1.2g)
    float acceleration = 1.0f + 0.2f * sin(phase * 2.0f);
    uint16_t acc_int = (uint16_t)(acceleration * 100); // 精度0.01g

    // 构建姿态数据包
    data[0] = pitch_int & 0xFF;                 // 俯仰角低字节
    data[1] = (pitch_int >> 8) & 0xFF;          // 俯仰角高字节
    data[2] = roll_int & 0xFF;                  // 横滚角低字节
    data[3] = (roll_int >> 8) & 0xFF;           // 横滚角高字节
    data[4] = acc_int & 0xFF;                   // 加速度低字节
    data[5] = (acc_int >> 8) & 0xFF;            // 加速度高字节
    data[6] = 0;                                // 保留字节
    data[7] = 0;                                // 保留字节

    return 0;
}
