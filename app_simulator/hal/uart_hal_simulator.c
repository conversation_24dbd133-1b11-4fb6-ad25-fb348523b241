/**
 * @file uart_hal_simulator.c
 * @brief App UART HAL模拟器实现 - 集成数据模拟器
 * @version 2.0.0
 * @date 2025-07-27
 */

#include "uart_hal.h"
#include "data_simulator.h"
#include "uart_protocol.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <pthread.h>
#include <unistd.h>
#include <sys/time.h>

// =============================================================================
// 内部数据结构
// =============================================================================

// 模拟器状态
typedef struct {
    bool initialized;
    bool running;
    pthread_t simulator_thread;
    pthread_mutex_t data_mutex;
    data_simulator_t* simulator;

    // 接收缓冲区
    uint8_t rx_buffer[1024];
    uint16_t rx_head;
    uint16_t rx_tail;
    uint16_t rx_count;

    // 统计信息
    uart_hal_statistics_t stats;

    // 配置信息
    uart_hal_config_t config;
    uint32_t timeout_ms;
} uart_hal_simulator_t;

// 全局模拟器实例
static uart_hal_simulator_t g_simulator = {0};

// =============================================================================
// 内部函数声明
// =============================================================================

static void* simulator_thread_func(void* arg);
static int generate_uart_frame(uint8_t need_ack, uint8_t msg_id, const uint8_t* data, uint16_t data_len, uint8_t* frame_buffer);
static uint16_t calculate_checksum(const uint8_t* data, uint16_t length);
static void add_to_rx_buffer(const uint8_t* data, uint16_t length);
static uint32_t get_timestamp_ms(void);

// =============================================================================
// 公共接口实现
// =============================================================================

uart_hal_error_t uart_hal_init(void) {
    if (g_simulator.initialized) {
        return UART_HAL_SUCCESS;
    }

    UART_HAL_DEBUG_PRINT("初始化UART HAL模拟器");

    // 初始化模拟器状态
    memset(&g_simulator, 0, sizeof(uart_hal_simulator_t));

    // 初始化互斥锁
    if (pthread_mutex_init(&g_simulator.data_mutex, NULL) != 0) {
        UART_HAL_DEBUG_PRINT("互斥锁初始化失败");
        return UART_HAL_ERROR_CONFIG_FAILED;
    }

    // 初始化数据模拟器
    g_simulator.simulator = data_simulator_get_instance();
    if (data_simulator_init(g_simulator.simulator) != 0) {
        UART_HAL_DEBUG_PRINT("数据模拟器初始化失败");
        pthread_mutex_destroy(&g_simulator.data_mutex);
        return UART_HAL_ERROR_CONFIG_FAILED;
    }

    // 设置默认配置
    g_simulator.config.baudrate = 115200;
    g_simulator.config.data_bits = 8;
    g_simulator.config.stop_bits = 1;
    g_simulator.config.parity = 0;
    g_simulator.config.flow_control = false;
    g_simulator.timeout_ms = 1000;

    // 启动模拟器线程
    g_simulator.running = true;
    if (pthread_create(&g_simulator.simulator_thread, NULL, simulator_thread_func, NULL) != 0) {
        UART_HAL_DEBUG_PRINT("模拟器线程创建失败");
        data_simulator_deinit(g_simulator.simulator);
        pthread_mutex_destroy(&g_simulator.data_mutex);
        return UART_HAL_ERROR_CONFIG_FAILED;
    }

    g_simulator.initialized = true;
    UART_HAL_DEBUG_PRINT("UART HAL模拟器初始化完成");

    return UART_HAL_SUCCESS;
}

void uart_hal_deinit(void) {
    if (!g_simulator.initialized) {
        return;
    }

    UART_HAL_DEBUG_PRINT("清理UART HAL模拟器");

    // 停止模拟器线程
    g_simulator.running = false;
    if (g_simulator.simulator_thread) {
        pthread_join(g_simulator.simulator_thread, NULL);
    }

    // 清理数据模拟器
    if (g_simulator.simulator) {
        data_simulator_deinit(g_simulator.simulator);
    }

    // 清理互斥锁
    pthread_mutex_destroy(&g_simulator.data_mutex);

    // 重置状态
    memset(&g_simulator, 0, sizeof(uart_hal_simulator_t));

    UART_HAL_DEBUG_PRINT("UART HAL模拟器清理完成");
}

uart_hal_error_t uart_hal_send_data(const uint8_t* data, uint16_t length) {
    if (!g_simulator.initialized) {
        return UART_HAL_ERROR_NOT_INITIALIZED;
    }

    if (!data || length == 0) {
        return UART_HAL_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_simulator.data_mutex);

    // 更新统计信息
    g_simulator.stats.bytes_sent += length;

    pthread_mutex_unlock(&g_simulator.data_mutex);

    UART_HAL_DEBUG_PRINT("发送数据，长度: %d字节", length);

    // 打印发送的数据（调试用）
    UART_HAL_DEBUG_PRINT("发送数据: ");
    for (int i = 0; i < length && i < 32; i++) {
        printf("%02X ", data[i]);
    }
    if (length > 32) printf("...");
    printf("\n");

    return UART_HAL_SUCCESS;
}

uart_hal_error_t uart_hal_receive_data(uint8_t* buffer, uint16_t buffer_size, uint16_t* received_length) {
    if (!g_simulator.initialized) {
        return UART_HAL_ERROR_NOT_INITIALIZED;
    }

    if (!buffer || !received_length || buffer_size == 0) {
        return UART_HAL_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_simulator.data_mutex);

    *received_length = 0;

    // 从接收缓冲区读取数据
    uint16_t available = g_simulator.rx_count;
    if (available > 0) {
        uint16_t to_copy = (available > buffer_size) ? buffer_size : available;

        for (uint16_t i = 0; i < to_copy; i++) {
            buffer[i] = g_simulator.rx_buffer[g_simulator.rx_tail];
            g_simulator.rx_tail = (g_simulator.rx_tail + 1) % sizeof(g_simulator.rx_buffer);
        }

        g_simulator.rx_count -= to_copy;
        *received_length = to_copy;

        // 更新统计信息
        g_simulator.stats.bytes_received += to_copy;
    }

    pthread_mutex_unlock(&g_simulator.data_mutex);

    return UART_HAL_SUCCESS;
}

bool uart_hal_is_data_available(void) {
    if (!g_simulator.initialized) {
        return false;
    }

    pthread_mutex_lock(&g_simulator.data_mutex);
    bool available = (g_simulator.rx_count > 0);
    pthread_mutex_unlock(&g_simulator.data_mutex);

    return available;
}

uart_hal_error_t uart_hal_set_config(const uart_hal_config_t* config) {
    if (!g_simulator.initialized) {
        return UART_HAL_ERROR_NOT_INITIALIZED;
    }

    if (!config) {
        return UART_HAL_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_simulator.data_mutex);
    g_simulator.config = *config;
    pthread_mutex_unlock(&g_simulator.data_mutex);

    UART_HAL_DEBUG_PRINT("设置UART配置: %d bps, %d%c%d",
           config->baudrate, config->data_bits,
           config->parity ? 'E' : 'N', config->stop_bits);

    return UART_HAL_SUCCESS;
}

uart_hal_error_t uart_hal_get_config(uart_hal_config_t* config) {
    if (!g_simulator.initialized) {
        return UART_HAL_ERROR_NOT_INITIALIZED;
    }

    if (!config) {
        return UART_HAL_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_simulator.data_mutex);
    *config = g_simulator.config;
    pthread_mutex_unlock(&g_simulator.data_mutex);

    return UART_HAL_SUCCESS;
}

uart_hal_error_t uart_hal_set_timeout(uint32_t timeout_ms) {
    if (!g_simulator.initialized) {
        return UART_HAL_ERROR_NOT_INITIALIZED;
    }

    pthread_mutex_lock(&g_simulator.data_mutex);
    g_simulator.timeout_ms = timeout_ms;
    pthread_mutex_unlock(&g_simulator.data_mutex);

    UART_HAL_DEBUG_PRINT("设置超时: %u ms", timeout_ms);
    return UART_HAL_SUCCESS;
}

const uart_hal_statistics_t* uart_hal_get_statistics(void) {
    if (!g_simulator.initialized) {
        static uart_hal_statistics_t empty_stats = {0};
        return &empty_stats;
    }

    return &g_simulator.stats;
}

void uart_hal_reset_statistics(void) {
    if (!g_simulator.initialized) {
        return;
    }

    pthread_mutex_lock(&g_simulator.data_mutex);
    memset(&g_simulator.stats, 0, sizeof(uart_hal_statistics_t));
    pthread_mutex_unlock(&g_simulator.data_mutex);

    UART_HAL_DEBUG_PRINT("重置统计信息");
}

uart_hal_error_t uart_hal_flush_buffers(void) {
    if (!g_simulator.initialized) {
        return UART_HAL_ERROR_NOT_INITIALIZED;
    }

    pthread_mutex_lock(&g_simulator.data_mutex);
    g_simulator.rx_head = 0;
    g_simulator.rx_tail = 0;
    g_simulator.rx_count = 0;
    pthread_mutex_unlock(&g_simulator.data_mutex);

    UART_HAL_DEBUG_PRINT("清空缓冲区");
    return UART_HAL_SUCCESS;
}

bool uart_hal_is_tx_complete(void) {
    // 模拟器中发送总是立即完成
    return true;
}

// =============================================================================
// 标准UART HAL接口 (兼容uart_core.c)
// =============================================================================

uart_hal_error_t uart_hal_send(const uint8_t* data, uint16_t length, uint16_t* sent) {
    if (!sent) {
        return UART_HAL_ERROR_INVALID_PARAM;
    }

    uart_hal_error_t result = uart_hal_send_data(data, length);
    if (result == UART_HAL_SUCCESS) {
        *sent = length;
    } else {
        *sent = 0;
    }

    return result;
}

uart_hal_error_t uart_hal_receive(uint8_t* buffer, uint16_t buffer_size, uint16_t* received) {
    UART_HAL_DEBUG_PRINT("接收请求: 缓冲区大小=%d", buffer_size);

    uart_hal_error_t result = uart_hal_receive_data(buffer, buffer_size, received);

    if (result == UART_HAL_SUCCESS && *received > 0) {
        UART_HAL_DEBUG_PRINT("接收成功: %d 字节", *received);
        // 打印接收的数据
        UART_HAL_DEBUG_PRINT("接收数据: ");
        for (int i = 0; i < *received && i < 16; i++) {
            printf("%02X ", buffer[i]);
        }
        if (*received > 16) printf("...");
        printf("\n");
    } else if (result == UART_HAL_SUCCESS && *received == 0) {
        // 静默处理无数据情况，避免日志过多
    } else {
        UART_HAL_DEBUG_PRINT("接收失败: 错误代码=%d", result);
    }

    return result;
}

bool uart_hal_data_available(void) {
    bool available = uart_hal_is_data_available();

    // 添加调试输出
    static uint32_t debug_counter = 0;
    if (++debug_counter % 1000 == 0 || available) { // 每1000次调用或有数据时打印
        UART_HAL_DEBUG_PRINT("数据可用性检查: %s (缓冲区: %d字节)",
               available ? "有数据" : "无数据", g_simulator.rx_count);
    }

    return available;
}

uart_hal_error_t uart_hal_open(const char* device_path, const uart_hal_config_t* config) {
    (void)device_path; // 模拟器不需要设备路径
    return uart_hal_set_config(config);
}

uart_hal_error_t uart_hal_close(void) {
    // 模拟器不需要关闭操作
    return UART_HAL_SUCCESS;
}

uart_hal_state_t uart_hal_get_state(void) {
    if (!g_simulator.initialized) {
        return UART_HAL_STATE_CLOSED;
    }
    return g_simulator.running ? UART_HAL_STATE_OPEN : UART_HAL_STATE_CLOSED;
}

uart_hal_error_t uart_hal_flush_receive(void) {
    return uart_hal_flush_buffers();
}

uart_hal_error_t uart_hal_flush_send(void) {
    return uart_hal_flush_buffers();
}

uart_hal_error_t uart_hal_set_receive_callback(uart_hal_receive_callback_t callback, void* user_data) {
    // 模拟器暂不支持回调
    (void)callback;
    (void)user_data;
    return UART_HAL_SUCCESS;
}

uart_hal_error_t uart_hal_set_error_callback(uart_hal_error_callback_t callback, void* user_data) {
    // 模拟器暂不支持回调
    (void)callback;
    (void)user_data;
    return UART_HAL_SUCCESS;
}

// =============================================================================
// 内部函数实现
// =============================================================================

static void* simulator_thread_func(void* arg) {
    (void)arg; // 避免未使用参数警告

    UART_HAL_DEBUG_PRINT("模拟器线程启动");

    // 启动数据模拟器
    if (data_simulator_start(g_simulator.simulator, SCENARIO_NORMAL_DRIVING, 0) != 0) {
        UART_HAL_DEBUG_PRINT("数据模拟器启动失败");
        return NULL;
    }

    UART_HAL_DEBUG_PRINT("数据模拟器启动成功，开始生成数据");

    uint32_t last_speed_time = 0;
    uint32_t last_rpm_time = 0;
    uint32_t last_gear_time = 0;
    uint32_t last_controller_time = 0;
    uint32_t last_range_time = 0;
    uint32_t last_mileage_time = 0;
    uint32_t last_time_time = 0;
    uint32_t last_light_time = 0;
    uint32_t last_light_warning_time = 0;
    uint32_t last_battery_time = 0;
    uint32_t last_attitude_time = 0;

    uint32_t loop_counter = 0;

    while (g_simulator.running) {
        uint32_t current_time = get_timestamp_ms();
        loop_counter++;

        // 更新数据模拟器
        data_simulator_update(g_simulator.simulator);

        uint8_t frame_buffer[256];
        uint8_t data_buffer[256];
        int frame_len;

        // 每1000次循环打印一次状态
        if (loop_counter % 1000 == 0) {
            UART_HAL_DEBUG_PRINT("模拟器运行中，循环次数: %u，缓冲区数据: %d字节",
                   loop_counter, g_simulator.rx_count);
        }

        // 生成速度数据 (0x60) - 每300ms
        if (current_time - last_speed_time >= 300) {
            if (data_simulator_generate_speed_data(g_simulator.simulator, data_buffer) == 0) {
                frame_len = generate_uart_frame(NO_ACK, 0x60, data_buffer, 2, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成速度数据帧: %d字节", frame_len);
                }
            }
            last_speed_time = current_time;
        }

        // 生成转速数据 (0x61) - 每150ms
        if (current_time - last_rpm_time >= 300) {
            if (data_simulator_generate_rpm_data(g_simulator.simulator, data_buffer) == 0) {
                frame_len = generate_uart_frame(NO_ACK, 0x61, data_buffer, 2, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成转速数据帧: %d字节", frame_len);
                }
            }
            last_rpm_time = current_time;
        }

        // 生成挡位数据 (0x62) - 每1000ms
        if (current_time - last_gear_time >= 1000) {
            if (data_simulator_generate_gear_data(g_simulator.simulator, data_buffer) == 0) {
                frame_len = generate_uart_frame(NO_ACK, 0x62, data_buffer, 1, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成挡位数据帧: %d字节", frame_len);
                }
            }
            last_gear_time = current_time;
        }

        // 生成控制器数据 (0x67) - 每1000ms
        if (current_time - last_controller_time >= 1000) {
            if (data_simulator_generate_controller_data(g_simulator.simulator, data_buffer) == 0) {
                frame_len = generate_uart_frame(NO_ACK, 0x67, data_buffer, 18, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成控制器数据帧: %d字节", frame_len);
                }
            }
            last_controller_time = current_time;
        }

        // 生成续航数据 (0x64) - 每3000ms
        if (current_time - last_range_time >= 3000) {
            if (data_simulator_generate_range_data(g_simulator.simulator, data_buffer) == 0) {
                frame_len = generate_uart_frame(NO_ACK, 0x64, data_buffer, 2, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成续航数据帧: %d字节", frame_len);
                }
            }
            last_range_time = current_time;
        }

        // 生成里程数据 (0x65) - 每5000ms
        if (current_time - last_mileage_time >= 5000) {
            if (data_simulator_generate_mileage_data(g_simulator.simulator, data_buffer) == 0) {
                frame_len = generate_uart_frame(NO_ACK, 0x65, data_buffer, 8, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成里程数据帧: %d字节", frame_len);
                }
            }
            last_mileage_time = current_time;
        }

        // 生成时间数据 (0x66) - 每1000ms
        if (current_time - last_time_time >= 1000) {
            if (data_simulator_generate_time_data(g_simulator.simulator, data_buffer) == 0) {
                frame_len = generate_uart_frame(NO_ACK, 0x66, data_buffer, 7, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成时间数据帧: %d字节", frame_len);
                }
            }
            last_time_time = current_time;
        }

        // 生成灯光屏幕控制数据 (0x56) - 每1500ms (加快变化频率)
        if (current_time - last_light_time >= 1500) {
            if (data_simulator_generate_light_data(g_simulator.simulator, data_buffer) == 0) {
                frame_len = generate_uart_frame(NEED_ACK, 0x56, data_buffer, 2, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成灯光屏幕控制数据帧: %d字节", frame_len);
                }
            }
            last_light_time = current_time;
        }

        // 生成灯光警示灯数据 (0x63) - 每800ms (加快变化频率)
        if (current_time - last_light_warning_time >= 800) {
            int light_data_len = data_simulator_generate_light_warning_data(g_simulator.simulator, data_buffer);
            if (light_data_len > 0) {
                frame_len = generate_uart_frame(NO_ACK, 0x63, data_buffer, light_data_len, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成灯光警示灯数据帧: %d字节", frame_len);
                }
            }
            last_light_warning_time = current_time;
        }

        // 生成电池数据 (模拟) - 每2000ms
        if (current_time - last_battery_time >= 2000) {
            if (data_simulator_generate_battery_data(g_simulator.simulator, data_buffer) == 0) {
                frame_len = generate_uart_frame(NO_ACK, 0x6A, data_buffer, 4, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成电池数据帧: %d字节", frame_len);
                }
            }
            last_battery_time = current_time;
        }

        // 生成姿态数据 (模拟) - 每1000ms
        if (current_time - last_attitude_time >= 1000) {
            if (data_simulator_generate_attitude_data(g_simulator.simulator, data_buffer) == 0) {
                frame_len = generate_uart_frame(NO_ACK, 0x6B, data_buffer, 8, frame_buffer);
                if (frame_len > 0) {
                    add_to_rx_buffer(frame_buffer, frame_len);
                    UART_HAL_DEBUG_PRINT("生成姿态数据帧: %d字节", frame_len);
                }
            }
            last_attitude_time = current_time;
        }

        // 休眠50ms
        usleep(50000);
    }

    // 停止数据模拟器
    data_simulator_stop(g_simulator.simulator);

    UART_HAL_DEBUG_PRINT("模拟器线程退出");
    return NULL;
}

static int generate_uart_frame(uint8_t need_ack, uint8_t msg_id, const uint8_t* data, uint16_t data_len, uint8_t* frame_buffer) {
    if (!data || !frame_buffer || data_len > 256) {
        return -1;
    }

    // 构建帧格式: AA BB [数据长度] [应答位] [ID] [数据] [校验和低] [校验和高]
    uint8_t* frame = frame_buffer;
    uint8_t frame_len = 0;
    uint8_t checksum_data[MAX_FRAME_SIZE] = {0}; // 初始化为0，避免未初始化警告
    uint16_t checksum;

    // 构建帧头和数据
    frame[frame_len++] = FRAME_HEADER_1;
    frame[frame_len++] = FRAME_HEADER_2;
    frame[frame_len++] = 1 + 1 + data_len + 2; // 长度 = 应答位 + ID + 数据 + 校验和(2字节)
    frame[frame_len++] = need_ack;
    frame[frame_len++] = msg_id;
    
    if (data && data_len > 0) {
        memcpy(&frame[frame_len], data, data_len);
        frame_len += data_len;
    }
    
    // 计算校验和
    checksum_data[0] = need_ack;
    checksum_data[1] = msg_id;
    if (data && data_len > 0) {
        memcpy(&checksum_data[2], data, data_len);
    }
    
    checksum = calculate_checksum(checksum_data, 2 + data_len);

    // 调试日志：打印校验和计算详情
    UART_HAL_DEBUG_PRINT("计算校验和: ");
    UART_HAL_DEBUG_PRINT("输入数据: ");
    for (int i = 0; i < 2 + data_len; i++) {
        printf("%02X ", checksum_data[i]);
    }
    UART_HAL_DEBUG_PRINT("计算结果: 0x%04X", checksum);
    UART_HAL_DEBUG_PRINT("存储格式: %02X %02X (大端序)\n",
           (checksum >> 8) & 0xFF, checksum & 0xFF);

    frame[frame_len++] = (checksum >> 8) & 0xFF;  // 校验和高位
    frame[frame_len++] = checksum & 0xFF;        // 校验和低位

    return frame_len;
}

static uint16_t calculate_checksum(const uint8_t* data, uint16_t length) {
    uint32_t checksum = 0;
    for (uint16_t i = 0; i < length; i++) {
        checksum += data[i];
        checksum += ((checksum & 0xFF) << 8) + 0x100;
        checksum = (checksum ^ (checksum >> 16)) & 0xFFFF;
    }
    return (uint16_t)checksum;
}

static void add_to_rx_buffer(const uint8_t* data, uint16_t length) {
    if (!data || length == 0) {
        return;
    }

    pthread_mutex_lock(&g_simulator.data_mutex);

    // 检查缓冲区空间
    uint16_t available_space = sizeof(g_simulator.rx_buffer) - g_simulator.rx_count;
    if (length > available_space) {
        // 缓冲区空间不足，丢弃旧数据
        uint16_t to_drop = length - available_space;
        g_simulator.rx_tail = (g_simulator.rx_tail + to_drop) % sizeof(g_simulator.rx_buffer);
        g_simulator.rx_count -= to_drop;
        g_simulator.stats.receive_errors++;
        UART_HAL_DEBUG_PRINT("缓冲区空间不足，丢弃 %d 字节旧数据", to_drop);
    }

    // 添加新数据
    for (uint16_t i = 0; i < length; i++) {
        g_simulator.rx_buffer[g_simulator.rx_head] = data[i];
        g_simulator.rx_head = (g_simulator.rx_head + 1) % sizeof(g_simulator.rx_buffer);
    }

    g_simulator.rx_count += length;
    // 更新最后活动时间
    g_simulator.stats.last_activity = get_timestamp_ms();

    UART_HAL_DEBUG_PRINT("添加 %d 字节到接收缓冲区，总计: %d 字节", length, g_simulator.rx_count);

    pthread_mutex_unlock(&g_simulator.data_mutex);
}

static uint32_t get_timestamp_ms(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint32_t)(tv.tv_sec * 1000 + tv.tv_usec / 1000);
}

// =============================================================================
// 扩展接口 - 用于测试和调试
// =============================================================================

/**
 * @brief 设置模拟器场景
 * @param scenario 场景类型
 * @return 0 成功，负数 失败
 */
int uart_hal_simulator_set_scenario(simulator_scenario_t scenario) {
    if (!g_simulator.initialized || !g_simulator.simulator) {
        return -1;
    }

    int result = data_simulator_set_scenario(g_simulator.simulator, scenario);
    if (result == 0) {
        UART_HAL_DEBUG_PRINT("切换到场景: %s",
               data_simulator_get_scenario_name(scenario));
    }

    return result;
}

/**
 * @brief 获取模拟器统计信息
 * @param update_count 更新次数
 * @param data_sent_count 发送数据次数
 * @param error_count 错误次数
 * @return 0 成功，负数 失败
 */
int uart_hal_simulator_get_statistics(uint32_t* update_count, uint32_t* data_sent_count, uint32_t* error_count) {
    if (!g_simulator.initialized || !g_simulator.simulator) {
        return -1;
    }

    return data_simulator_get_statistics(g_simulator.simulator, update_count, data_sent_count, error_count);
}

/**
 * @brief 打印模拟器状态
 */
void uart_hal_simulator_print_status(void) {
    if (!g_simulator.initialized) {
        UART_HAL_DEBUG_PRINT("模拟器未初始化");
        return;
    }

    printf("\n=== UART HAL模拟器状态 ===\n");
    printf("初始化状态: %s\n", g_simulator.initialized ? "已初始化" : "未初始化");
    printf("运行状态: %s\n", g_simulator.running ? "运行中" : "已停止");
    printf("接收缓冲区: %d/%d 字节\n", g_simulator.rx_count, (int)sizeof(g_simulator.rx_buffer));

    const uart_hal_statistics_t* stats = &g_simulator.stats;
    printf("发送字节数: %u\n", stats->bytes_sent);
    printf("接收字节数: %u\n", stats->bytes_received);
    printf("发送错误数: %u\n", stats->send_errors);
    printf("接收错误数: %u\n", stats->receive_errors);
    printf("超时次数: %u\n", stats->timeouts);
    printf("最后活动时间: %u ms\n", stats->last_activity);

    if (g_simulator.simulator) {
        uint32_t update_count, data_sent_count, error_count;
        if (data_simulator_get_statistics(g_simulator.simulator, &update_count, &data_sent_count, &error_count) == 0) {
            printf("模拟器更新次数: %u\n", update_count);
            printf("模拟器数据生成次数: %u\n", data_sent_count);
            printf("模拟器错误次数: %u\n", error_count);
            printf("当前场景: %s\n", data_simulator_get_scenario_name(data_simulator_get_scenario(g_simulator.simulator)));
        }
    }

    printf("========================\n");
}
