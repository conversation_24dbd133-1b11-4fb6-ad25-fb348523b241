/**
 * @file uart_hal.h
 * @brief App UART 硬件抽象层 - UART硬件接口封装
 * @version 1.0.0
 * @date 2025-07-26
 */

#ifndef UART_HAL_H
#define UART_HAL_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// =============================================================================
// UART配置定义
// =============================================================================

/**
 * @brief UART配置结构体
 */
typedef struct {
    uint32_t baudrate;          // 波特率
    uint8_t data_bits;          // 数据位
    uint8_t stop_bits;          // 停止位
    uint8_t parity;             // 奇偶校验
    bool flow_control;          // 流控制
    uint32_t timeout_ms;        // 超时时间
} uart_hal_config_t;

/**
 * @brief UART状态枚举
 */
typedef enum {
    UART_HAL_STATE_CLOSED = 0,  // 关闭状态
    UART_HAL_STATE_OPEN,        // 打开状态
    UART_HAL_STATE_ERROR        // 错误状态
} uart_hal_state_t;

/**
 * @brief UART错误代码
 */
typedef enum {
    UART_HAL_SUCCESS = 0,               // 成功
    UART_HAL_ERROR_INVALID_PARAM,       // 无效参数
    UART_HAL_ERROR_NOT_INITIALIZED,     // 未初始化
    UART_HAL_ERROR_ALREADY_INITIALIZED, // 已初始化
    UART_HAL_ERROR_NOT_OPEN,            // 未打开
    UART_HAL_ERROR_ALREADY_OPEN,        // 已打开
    UART_HAL_ERROR_DEVICE_NOT_OPEN,     // 设备未打开
    UART_HAL_ERROR_DEVICE_OPEN_FAILED,  // 设备打开失败
    UART_HAL_ERROR_CONFIG_FAILED,       // 配置失败
    UART_HAL_ERROR_DEVICE_ERROR,        // 设备错误
    UART_HAL_ERROR_TIMEOUT,             // 超时
    UART_HAL_ERROR_BUFFER_FULL,         // 缓冲区满
    UART_HAL_ERROR_NO_DATA,             // 无数据
    UART_HAL_ERROR_SEND_FAILED,         // 发送失败
    UART_HAL_ERROR_RECEIVE_FAILED,      // 接收失败
    UART_HAL_ERROR_WRITE_FAILED,        // 写入失败
    UART_HAL_ERROR_READ_FAILED          // 读取失败
} uart_hal_error_t;

/**
 * @brief UART统计信息
 */
typedef struct {
    uint32_t bytes_sent;        // 发送字节数
    uint32_t bytes_received;    // 接收字节数
    uint32_t send_errors;       // 发送错误数
    uint32_t receive_errors;    // 接收错误数
    uint32_t timeouts;          // 超时次数
    uint32_t last_activity;     // 最后活动时间
} uart_hal_statistics_t;

// =============================================================================
// 回调函数类型定义
// =============================================================================

/**
 * @brief UART数据接收回调函数类型
 * @param data 接收到的数据
 * @param length 数据长度
 * @param user_data 用户数据
 */
typedef void (*uart_hal_receive_callback_t)(const uint8_t* data, uint16_t length, void* user_data);

/**
 * @brief UART错误回调函数类型
 * @param error_code 错误代码
 * @param error_msg 错误信息
 * @param user_data 用户数据
 */
typedef void (*uart_hal_error_callback_t)(uart_hal_error_t error_code, const char* error_msg, void* user_data);

// =============================================================================
// UART硬件抽象接口
// =============================================================================

/**
 * @brief 初始化UART硬件抽象层
 * @return 错误代码
 */
uart_hal_error_t uart_hal_init(void);

/**
 * @brief 清理UART硬件抽象层
 */
void uart_hal_deinit(void);

/**
 * @brief 打开UART设备
 * @param device_path 设备路径
 * @param config 配置参数
 * @return 错误代码
 */
uart_hal_error_t uart_hal_open(const char* device_path, const uart_hal_config_t* config);

/**
 * @brief 关闭UART设备
 * @return 错误代码
 */
uart_hal_error_t uart_hal_close(void);

/**
 * @brief 获取UART状态
 * @return UART状态
 */
uart_hal_state_t uart_hal_get_state(void);

/**
 * @brief 发送数据
 * @param data 数据缓冲区
 * @param length 数据长度
 * @param sent 实际发送的字节数
 * @return 错误代码
 */
uart_hal_error_t uart_hal_send(const uint8_t* data, uint16_t length, uint16_t* sent);

/**
 * @brief 接收数据
 * @param buffer 接收缓冲区
 * @param buffer_size 缓冲区大小
 * @param received 实际接收的字节数
 * @return 错误代码
 */
uart_hal_error_t uart_hal_receive(uint8_t* buffer, uint16_t buffer_size, uint16_t* received);

/**
 * @brief 检查是否有数据可读
 * @return true 有数据，false 无数据
 */
bool uart_hal_data_available(void);

/**
 * @brief 清空接收缓冲区
 * @return 错误代码
 */
uart_hal_error_t uart_hal_flush_receive(void);

/**
 * @brief 清空发送缓冲区
 * @return 错误代码
 */
uart_hal_error_t uart_hal_flush_send(void);

// =============================================================================
// 回调函数管理
// =============================================================================

/**
 * @brief 设置数据接收回调
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return 错误代码
 */
uart_hal_error_t uart_hal_set_receive_callback(uart_hal_receive_callback_t callback, void* user_data);

/**
 * @brief 设置错误回调
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return 错误代码
 */
uart_hal_error_t uart_hal_set_error_callback(uart_hal_error_callback_t callback, void* user_data);

// =============================================================================
// 配置和状态查询
// =============================================================================

/**
 * @brief 获取当前配置
 * @param config 输出配置
 * @return 错误代码
 */
uart_hal_error_t uart_hal_get_config(uart_hal_config_t* config);

/**
 * @brief 设置超时时间
 * @param timeout_ms 超时时间(毫秒)
 * @return 错误代码
 */
uart_hal_error_t uart_hal_set_timeout(uint32_t timeout_ms);

/**
 * @brief 获取统计信息
 * @return 统计信息指针
 */
const uart_hal_statistics_t* uart_hal_get_statistics(void);

/**
 * @brief 重置统计信息
 */
void uart_hal_reset_statistics(void);

/**
 * @brief 打印统计信息
 */
void uart_hal_print_statistics(void);

// =============================================================================
// 高级功能
// =============================================================================

/**
 * @brief 启用/禁用非阻塞模式
 * @param enabled 是否启用
 * @return 错误代码
 */
uart_hal_error_t uart_hal_set_non_blocking(bool enabled);

/**
 * @brief 设置接收缓冲区大小
 * @param size 缓冲区大小
 * @return 错误代码
 */
uart_hal_error_t uart_hal_set_receive_buffer_size(uint32_t size);

/**
 * @brief 设置发送缓冲区大小
 * @param size 缓冲区大小
 * @return 错误代码
 */
uart_hal_error_t uart_hal_set_send_buffer_size(uint32_t size);

/**
 * @brief 获取接收缓冲区使用情况
 * @param used 已使用字节数
 * @param total 总字节数
 * @return 错误代码
 */
uart_hal_error_t uart_hal_get_receive_buffer_usage(uint32_t* used, uint32_t* total);

/**
 * @brief 获取发送缓冲区使用情况
 * @param used 已使用字节数
 * @param total 总字节数
 * @return 错误代码
 */
uart_hal_error_t uart_hal_get_send_buffer_usage(uint32_t* used, uint32_t* total);

// =============================================================================
// 调试和诊断
// =============================================================================

/**
 * @brief 启用/禁用调试模式
 * @param enabled 是否启用
 */
void uart_hal_set_debug_mode(bool enabled);

/**
 * @brief 执行自检
 * @return 错误代码
 */
uart_hal_error_t uart_hal_self_test(void);

/**
 * @brief 打印设备信息
 */
void uart_hal_print_device_info(void);

/**
 * @brief 获取错误描述
 * @param error_code 错误代码
 * @return 错误描述字符串
 */
const char* uart_hal_get_error_string(uart_hal_error_t error_code);

// =============================================================================
// 默认配置
// =============================================================================

/**
 * @brief 获取默认UART配置
 * @return 默认配置
 */
static inline uart_hal_config_t uart_hal_get_default_config(void) {
    uart_hal_config_t config = {
        .baudrate = 115200,
        .data_bits = 8,
        .stop_bits = 1,
        .parity = 0,        // 无奇偶校验
        .flow_control = false,
        .timeout_ms = 1000
    };
    return config;
}

// =============================================================================
// 常量定义
// =============================================================================

#define UART_HAL_DEFAULT_DEVICE         "/dev/ttyS0"
#define UART_HAL_DEFAULT_BAUDRATE       115200
#define UART_HAL_DEFAULT_TIMEOUT        1000
#define UART_HAL_DEFAULT_BUFFER_SIZE    1024
#define UART_HAL_MAX_DEVICE_PATH        256

// 调试宏
#ifdef UART_HAL_DEBUG
#include <stdio.h>
#define UART_HAL_DEBUG_PRINT(fmt, ...) \
    printf("[UART_HAL_DEBUG] " fmt "\n", ##__VA_ARGS__)
#else
#define UART_HAL_DEBUG_PRINT(fmt, ...)
#endif

#ifdef __cplusplus
}
#endif

#endif // UART_HAL_H
