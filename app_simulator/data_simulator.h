/**
 * @file data_simulator.h
 * @brief 数据模拟器 - 生成各种协议数据用于测试
 * @version 1.0.0
 * @date 2025-07-26
 */

#ifndef DATA_SIMULATOR_H
#define DATA_SIMULATOR_H

#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

// =============================================================================
// 模拟器配置
// =============================================================================

#define SIMULATOR_MAX_SCENARIOS     10      // 最大场景数
#define SIMULATOR_UPDATE_INTERVAL   100     // 更新间隔(ms)
#define SIMULATOR_DATA_BUFFER_SIZE  256     // 数据缓冲区大小

// =============================================================================
// 测试场景定义
// =============================================================================

/**
 * @brief 测试场景类型
 */
typedef enum {
    SCENARIO_NORMAL_DRIVING = 0,    // 正常行驶场景
    SCENARIO_HIGH_SPEED,            // 高速行驶场景
    SCENARIO_TCS_ACTIVE,            // TCS激活场景
    SCENARIO_CRUISE_CONTROL,        // 巡航控制场景
    SCENARIO_LOW_BATTERY,           // 低电量场景
    SCENARIO_FAULT_TEST,            // 故障测试场景
    SCENARIO_BOUNDARY_TEST,         // 边界值测试场景
    SCENARIO_IDLE_TEST,             // 怠速测试场景
    SCENARIO_CHARGING_TEST,         // 充电测试场景
    SCENARIO_CUSTOM_TEST,           // 自定义测试场景
    SCENARIO_COUNT                  // 场景总数
} simulator_scenario_t;

/**
 * @brief 数据变化模式
 */
typedef enum {
    DATA_MODE_STATIC = 0,           // 静态数据
    DATA_MODE_LINEAR,               // 线性变化
    DATA_MODE_SINE_WAVE,            // 正弦波变化
    DATA_MODE_RANDOM,               // 随机变化
    DATA_MODE_STEP,                 // 阶跃变化
    DATA_MODE_PULSE                 // 脉冲变化
} data_mode_t;

// =============================================================================
// 数据生成器结构体
// =============================================================================

/**
 * @brief 速度数据生成器
 */
typedef struct {
    uint16_t current_speed;         // 当前速度 (km/h)
    uint16_t target_speed;          // 目标速度 (km/h)
    uint16_t max_speed;             // 最大速度 (km/h)
    data_mode_t mode;               // 变化模式
    float amplitude;                // 变化幅度
    float frequency;                // 变化频率
    uint32_t last_update;           // 上次更新时间
} speed_generator_t;

/**
 * @brief 转速数据生成器
 */
typedef struct {
    uint16_t current_rpm;           // 当前转速 (RPM)
    uint16_t target_rpm;            // 目标转速 (RPM)
    uint16_t max_rpm;               // 最大转速 (RPM)
    data_mode_t mode;               // 变化模式
    float amplitude;                // 变化幅度
    float frequency;                // 变化频率
    uint32_t last_update;           // 上次更新时间
} rpm_generator_t;

/**
 * @brief 控制器状态生成器
 */
typedef struct {
    uint8_t tcs_enabled;            // TCS使能
    uint8_t tcs_active;             // TCS激活
    uint8_t cruise_enabled;         // 巡航使能
    uint8_t cruise_active;          // 巡航激活
    uint8_t work_mode;              // 工作模式
    uint8_t brake_status;           // 制动状态
    uint8_t gear_status;            // 挡位状态
    int16_t temperature;            // 控制器温度
    float voltage;                  // 母线电压
    float current;                  // 母线电流
    uint8_t soc;                    // SOC
    uint16_t range;                 // 续航里程
    uint32_t mileage;               // 总里程
    uint32_t trip_mileage;          // 小计里程
    uint8_t fault_code;             // 故障代码
    uint32_t last_update;           // 上次更新时间
} controller_generator_t;

/**
 * @brief 时间数据生成器
 */
typedef struct {
    uint8_t year;                   // 年
    uint8_t month;                  // 月
    uint8_t day;                    // 日
    uint8_t hour;                   // 时
    uint8_t minute;                 // 分
    uint8_t second;                 // 秒
    uint8_t week;                   // 星期
    uint32_t last_update;           // 上次更新时间
} time_generator_t;

/**
 * @brief 数据模拟器主结构体
 */
typedef struct {
    bool initialized;               // 是否已初始化
    bool running;                   // 是否运行中
    simulator_scenario_t scenario;  // 当前场景
    uint32_t start_time;            // 开始时间
    uint32_t current_time;          // 当前时间
    uint32_t duration;              // 运行时长
    bool auto_mode;                 // 自动模式
    
    // 数据生成器
    speed_generator_t speed_gen;
    rpm_generator_t rpm_gen;
    controller_generator_t controller_gen;
    time_generator_t time_gen;
    
    // 数据缓冲区
    uint8_t data_buffer[SIMULATOR_DATA_BUFFER_SIZE];
    
    // 统计信息
    uint32_t update_count;          // 更新次数
    uint32_t data_sent_count;       // 发送数据次数
    uint32_t error_count;           // 错误次数
} data_simulator_t;

// =============================================================================
// 模拟器接口
// =============================================================================

/**
 * @brief 初始化数据模拟器
 * @param simulator 模拟器结构体
 * @return 0 成功，负数 失败
 */
int data_simulator_init(data_simulator_t* simulator);

/**
 * @brief 清理数据模拟器
 * @param simulator 模拟器结构体
 */
void data_simulator_deinit(data_simulator_t* simulator);

/**
 * @brief 启动数据模拟器
 * @param simulator 模拟器结构体
 * @param scenario 测试场景
 * @param duration 运行时长(ms)，0表示无限运行
 * @return 0 成功，负数 失败
 */
int data_simulator_start(data_simulator_t* simulator, simulator_scenario_t scenario, uint32_t duration);

/**
 * @brief 停止数据模拟器
 * @param simulator 模拟器结构体
 */
void data_simulator_stop(data_simulator_t* simulator);

/**
 * @brief 更新数据模拟器
 * @param simulator 模拟器结构体
 * @return 0 成功，负数 失败
 */
int data_simulator_update(data_simulator_t* simulator);

/**
 * @brief 设置测试场景
 * @param simulator 模拟器结构体
 * @param scenario 测试场景
 * @return 0 成功，负数 失败
 */
int data_simulator_set_scenario(data_simulator_t* simulator, simulator_scenario_t scenario);

/**
 * @brief 获取当前场景
 * @param simulator 模拟器结构体
 * @return 当前场景
 */
simulator_scenario_t data_simulator_get_scenario(data_simulator_t* simulator);

// =============================================================================
// 数据生成接口
// =============================================================================

/**
 * @brief 生成控制器状态数据 (0x67)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (18字节)
 * @return 0 成功，负数 失败
 */
int data_simulator_generate_controller_data(data_simulator_t* simulator, uint8_t* data);

/**
 * @brief 生成灯光屏幕控制数据 (0x56)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (2字节)
 * @return 0 成功，负数 失败
 */
int data_simulator_generate_light_data(data_simulator_t* simulator, uint8_t* data);

/**
 * @brief 生成速度数据 (0x60)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (2字节)
 * @return 0 成功，负数 失败
 */
int data_simulator_generate_speed_data(data_simulator_t* simulator, uint8_t* data);

/**
 * @brief 生成转速数据 (0x61)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (2字节)
 * @return 0 成功，负数 失败
 */
int data_simulator_generate_rpm_data(data_simulator_t* simulator, uint8_t* data);

/**
 * @brief 生成挡位数据 (0x62)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (1字节)
 * @return 0 成功，负数 失败
 */
int data_simulator_generate_gear_data(data_simulator_t* simulator, uint8_t* data);

/**
 * @brief 生成续航数据 (0x64)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (2字节)
 * @return 0 成功，负数 失败
 */
int data_simulator_generate_range_data(data_simulator_t* simulator, uint8_t* data);

/**
 * @brief 生成里程数据 (0x65)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (8字节)
 * @return 0 成功，负数 失败
 */
int data_simulator_generate_mileage_data(data_simulator_t* simulator, uint8_t* data);

/**
 * @brief 生成时间数据 (0x66)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (7字节)
 * @return 0 成功，负数 失败
 */
int data_simulator_generate_time_data(data_simulator_t* simulator, uint8_t* data);

/**
 * @brief 生成灯光警示灯数据 (0x63)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (可变长度)
 * @return 实际数据长度，负数表示失败
 */
int data_simulator_generate_light_warning_data(data_simulator_t* simulator, uint8_t* data);

/**
 * @brief 生成电池数据 (模拟)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (4字节: 电量%, 充电状态, 电压低字节, 电压高字节)
 * @return 0 成功，负数 失败
 */
int data_simulator_generate_battery_data(data_simulator_t* simulator, uint8_t* data);

/**
 * @brief 生成姿态数据 (模拟)
 * @param simulator 模拟器结构体
 * @param data 输出数据缓冲区 (8字节: 俯仰角, 横滚角, 加速度等)
 * @return 0 成功，负数 失败
 */
int data_simulator_generate_attitude_data(data_simulator_t* simulator, uint8_t* data);

// =============================================================================
// 工具函数
// =============================================================================

/**
 * @brief 获取模拟器实例
 * @return 模拟器结构体指针
 */
data_simulator_t* data_simulator_get_instance(void);

/**
 * @brief 获取场景名称
 * @param scenario 场景类型
 * @return 场景名称字符串
 */
const char* data_simulator_get_scenario_name(simulator_scenario_t scenario);

/**
 * @brief 获取统计信息
 * @param simulator 模拟器结构体
 * @param update_count 更新次数
 * @param data_sent_count 发送数据次数
 * @param error_count 错误次数
 * @return 0 成功，负数 失败
 */
int data_simulator_get_statistics(data_simulator_t* simulator, 
                                 uint32_t* update_count, 
                                 uint32_t* data_sent_count, 
                                 uint32_t* error_count);

/**
 * @brief 重置统计信息
 * @param simulator 模拟器结构体
 */
void data_simulator_reset_statistics(data_simulator_t* simulator);

/**
 * @brief 获取当前时间戳(ms)
 * @return 时间戳
 */
uint32_t data_simulator_get_timestamp(void);

#ifdef __cplusplus
}
#endif

#endif // DATA_SIMULATOR_H
